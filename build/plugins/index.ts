import type { PluginOption } from 'vite';
import vue from '@vitejs/plugin-vue';
import vueJsx from '@vitejs/plugin-vue-jsx';
import VueDevtools from 'vite-plugin-vue-devtools';
import progress from 'vite-plugin-progress';
import { setupElegantRouter } from './router';
import { setupUnocss } from './unocss';
import { setupUnplugin } from './unplugin';
import { setupHtmlPlugin } from './html';
import AutoImport from 'unplugin-auto-import/vite';
import Components from 'unplugin-vue-components/vite';
import { NaiveUiResolver } from 'unplugin-vue-components/resolvers';
import { ProNaiveUIResolver } from 'pro-naive-ui-resolver'
import Icons from 'unplugin-icons/vite'
import IconsResolver from 'unplugin-icons/resolver'
export function setupVitePlugins(viteEnv: Env.ImportMeta, buildTime: string) {
  const plugins: PluginOption = [
    vue(),
    Components({
      resolvers: [NaiveUiResolver(), ProNaiveUIResolver(),IconsResolver({
        prefix: 'Icon',
      })],
      dts: 'src/typings/components.d.ts',
    }),
    AutoImport({
      // 自动导入 Vue 相关函数，如：ref, reactive, toRef 等
      imports: ['vue', "vue-router", "pinia",],
      // 生成自动导入的类型声明文件
      dts: 'src/typings/auto-imports.d.ts',
      eslintrc: {
        enabled: true, // 生成 ESLint 配置
        filepath: './.eslintrc-auto-import.json', //该文件也是自动成功
      },
    }),
    Icons({
      compiler: "vue3",
      autoInstall: true
    }),

    vueJsx(),
    VueDevtools(),
    setupElegantRouter(),
    setupUnocss(viteEnv),
    ...setupUnplugin(viteEnv),
    progress(),
    setupHtmlPlugin(buildTime)
  ];

  return plugins;
}
