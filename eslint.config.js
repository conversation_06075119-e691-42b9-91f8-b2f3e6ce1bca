import { defineConfig } from '@soybeanjs/eslint-config';

export default defineConfig(
  { vue: true, unocss: true },
  {
    rules: {
      'vue/multi-word-component-names': [
        'warn',
        {
          ignores: ['index', 'App', 'Register', '[id]', '[url]']
        }
      ],
      'vue/component-name-in-template-casing': [
        'warn',
        'PascalCase',
        {
          registeredComponentsOnly: false,
          ignores: ['/^icon-/']
        }
      ],
      'unocss/order-attributify': 'off',
      'import/order': [
        'warn',
        {
          groups: ['builtin', 'external', 'internal', 'parent', 'sibling', 'index'],
          'newlines-between': 'always'
        }
      ],
      'padding-line-between-statements': [
        'warn',

        // 函数之间空行
        { blankLine: 'always', prev: 'function', next: 'function' },

        // const 定义与下一个函数之间空行
        { blankLine: 'always', prev: 'const', next: 'function' },

        // 函数后如果是 const，也空一行
        { blankLine: 'always', prev: 'function', next: 'const' },

        // const 常量之间不强制（可选）
        { blankLine: 'any', prev: 'const', next: 'const' }
      ],
      'no-console': ['warn', { allow: ['warn', 'error'] }]
    }
  }
);
