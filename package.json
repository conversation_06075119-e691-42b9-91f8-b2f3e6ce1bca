{"name": "nova-office-pc", "type": "module", "version": "1.0.0", "description": "新星办公平台PC端", "engines": {"node": ">=18.12.0", "pnpm": ">=8.7.0"}, "scripts": {"build": "vite build --mode prod", "build:dev": "vite build --mode dev", "build:stage": "vite build --mode stage", "cleanup": "sa cleanup", "commit": "sa git-commit", "commit:zh": "sa git-commit -l=zh-cn", "dev": "vite --mode dev", "dev:prod": "vite --mode prod", "dev:stage": "vite --mode stage", "gen-route": "sa gen-route", "lint": "eslint . --fix", "prepare": "simple-git-hooks", "preview": "vite preview", "release": "sa release", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "update-pkg": "sa update-pkg"}, "dependencies": {"@better-scroll/core": "2.5.1", "@iconify/vue": "4.2.0", "@sa/axios": "workspace:*", "@sa/color": "workspace:*", "@sa/hooks": "workspace:*", "@sa/materials": "workspace:*", "@sa/utils": "workspace:*", "@vue-office/docx": "^1.6.3", "@vue-office/excel": "^1.7.14", "@vue-office/pptx": "^1.0.1", "@vueuse/core": "12.0.0", "@wangeditor/editor-for-vue": "^5.1.12", "@wangeditor/plugin-ctrl-enter": "^1.1.2", "@wangeditor/plugin-upload-attachment": "^1.1.0", "clipboard": "2.0.11", "crypto-js": "4.2.0", "dayjs": "1.11.13", "echarts": "5.5.1", "json5": "2.2.3", "md-editor-v3": "^5.8.4", "naive-ui": "2.41.1", "nprogress": "0.2.0", "pinia": "2.3.0", "pro-naive-ui": "^2.4.3", "qs": "6.13.1", "tailwind-merge": "2.5.5", "vue": "3.5.13", "vue-draggable-plus": "0.6.0", "vue-i18n": "10.0.5", "vue-router": "4.5.0"}, "devDependencies": {"@elegant-router/vue": "0.3.8", "@iconify/json": "2.2.281", "@sa/scripts": "workspace:*", "@sa/uno-preset": "workspace:*", "@soybeanjs/eslint-config": "1.4.3", "@types/node": "22.10.1", "@types/nprogress": "0.2.3", "@unocss/eslint-config": "0.65.1", "@unocss/preset-icons": "0.65.1", "@unocss/preset-uno": "0.65.1", "@unocss/transformer-directives": "0.65.1", "@unocss/transformer-variant-group": "0.65.1", "@unocss/vite": "0.65.1", "@vitejs/plugin-vue": "5.2.1", "@vitejs/plugin-vue-jsx": "4.1.1", "eslint": "9.16.0", "eslint-plugin-vue": "9.32.0", "lint-staged": "15.2.10", "pro-naive-ui-resolver": "^1.0.2", "sass": "1.82.0", "simple-git-hooks": "2.11.1", "tsx": "4.19.2", "typescript": "5.7.2", "unplugin-auto-import": "^19.3.0", "unplugin-icons": "0.21.0", "unplugin-vue-components": "0.27.5", "vite": "6.0.3", "vite-plugin-progress": "0.0.7", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-vue-devtools": "7.6.7", "vue-eslint-parser": "9.4.3", "vue-tsc": "2.1.10"}, "simple-git-hooks": {}, "lint-staged": {"*": "eslint --fix"}}