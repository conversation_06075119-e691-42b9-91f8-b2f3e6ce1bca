/**
 * Namespace Api
 *
 * All backend api type
 */
declare namespace Api {
  namespace Common {
    /** common params of paginating */
    interface PaginatingCommonParams {
      /** current page number */
      pageNo: number;
      /** page size */
      pageSize: number;
      /** total count */
      readonly total: number;
    }
    type YesOrNoStatus = 'Y' | 'N';
    /**
     * 显示状态
     *
     * - "0": 显示
     * - "1": 隐藏
     */
    type VisibleStatus = '0' | '1';
    /** common params of paginating query list data */
    interface PaginatingQueryRecord<T = any> extends PaginatingCommonParams {
      list: T[];
    }
    /** common params of paginating query list data */
    interface ProPaginatingQueryRecord<T = any> extends PaginatingCommonParams {
      list: T[];
      total: number;
    }

    /** common search params of table */
    type CommonSearchParams = Pick<Common.PaginatingCommonParams, 'pageNo' | 'pageSize'>;

    /** common delete params */
    interface CommonDeleteParams {
      id: number;
    }

    /** 提交成功、编辑成功 */
    type ApiNull = object;

    /**
     * enable status
     *
     * - "1": enabled
     * - "2": disabled
     */
    type EnableStatus = '0' | '1';
    type CommonTenantRecord<T = any> = {
      /** record tenant id */
      tenantId: string;
    } & CommonRecord<T>;

    /** common record */
    type CommonRecord<T = any> = {
      /** record id */
      id?: number;
      /** record create time */
      readonly createTime: string;
      /** record update time */
      readonly updateTime?: string;
    } & T;
  }

  /**
   * namespace Auth
   *
   * backend api module: "auth"
   */
  namespace Auth {
    interface LoginToken {
      accessToken: string;
      refreshToken: string;
    }

    interface UserInfo {
      id?: string;
      username: string;
      nickname: string;
      roles: string[];
      buttons?: string[];
    }
  }

  /**
   * namespace Route
   *
   * backend api module: "route"
   */
  namespace Route {
    type ElegantConstRoute = import('@elegant-router/types').ElegantConstRoute;

    interface MenuRoute extends ElegantConstRoute {
      id: string;
    }

    interface UserRoute {
      routes: MenuRoute[];
      home: import('@elegant-router/types').LastLevelRouteKey;
    }
  }

  namespace SystemManage {
    /** common delete params */
    type CommonDeleteParams = { id: number };

    /** common batch delete params */
    type CommonBatchDeleteParams = { ids: string[] };

    /** role */
    type Role = Common.CommonRecord<{
      /** 角色标志 */
      code: string;
      /** 创建时间 */
      createTime: Date;
      /** 数据范围，参见 DataScopeEnum 枚举类 */
      dataScope: number;
      /** 数据范围(指定部门数组) */
      dataScopeDeptIds: string[];
      /** 角色编号 */
      id: number;
      /** 角色名称 */
      name: string;
      /** 备注 */
      remark?: string;
      /** 显示顺序 */
      sort: number;
      /** 状态，参见 CommonStatusEnum 枚举类 */
      status: number;
      /** 角色类型，参见 RoleTypeEnum 枚举类 */
      type: number;
    }>;
    type PermissionAssignRoleDataScopeReqVO = Pick<Api.SystemManage.Role, 'dataScope' | 'dataScopeDeptIds'> & {
      roleId: number;
      systemName: string;
    };

    /** role add params */
    type RoleAddParams = Pick<Api.SystemManage.Role, 'name' | 'remark'>;

    /** role update params */
    type RoleUpdateParams = CommonType.RecordNullable<Pick<Api.SystemManage.Role, 'id'>> & RoleAddParams;

    /** role search params */
    type RoleSearchParams = CommonType.RecordNullable<
      Pick<Api.SystemManage.Role, 'name' | 'code' | 'status'> & Common.CommonSearchParams
    >;

    /** role list */
    type RoleAllList = Api.Common.PaginatingQueryRecord<Role>;
    type RoleSimpleList = Array<Role>;

    /** role authorized */
    type RoleAuthorized = Api.SystemManage.Role & {
      menuIds: number[];
      apiIds: number[];
      buttonIds: number[];
    };

    /** get role authorized params */
    type RoleAuthorizedParams = Pick<Api.SystemManage.RoleAuthorized, 'id'>;

    /** role authorized list */
    type RoleAuthorizedList = CommonType.RecordNullable<RoleAuthorized>;

    /** all role */
    type AllRole = Pick<Role, 'id' | 'name'>;

    /**
     * user gender
     *
     * - "2": "male"
     * - "1": "female"
     * - "0": "unknow"
     */
    type UserGender = '0' | '1';

    /** user */
    type User = Common.CommonRecord<{
      /** user name */
      username: string;
      deptId: number | null;
      loginDate: string;
      avatar: string;
      loginIp: string;
      /** password */
      password: string;
      /** user gender */
      gender: UserGender | null;
      /** 称呼 */
      nickname: string;
      /** user phone */
      mobile: string;
      /** user email */
      email: string;
      /** user role code collection */
      postIds: number[];
      roleIds?: number[];
      sex: number | null;
      /** user status */
      status: Common.EnableStatus | null;
      /** 钉钉用户id */
      dingtalkUserId: string;
      /* 员工是否离职 */
      deleted: boolean;
    }>;
    type UserProfileRespVO = {
      /** 用户头像 */
      avatar?: string;
      /** 创建时间 */
      createTime: Date;
      dept?: DeptSimpleRespVO;
      /** 用户邮箱 */
      email?: string;
      /** 用户编号 */
      id: number;
      /** 最后登录时间 */
      loginDate: Date;
      /** 最后登录 IP */
      loginIp: string;
      /** 手机号码 */
      mobile?: string;
      /** 用户昵称 */
      nickname: string;
      posts?: PostSimpleRespVO[];
      roles?: RoleSimpleRespVO[];
      /** 用户性别，参见 SexEnum 枚举类 */
      sex?: number;
      socialUsers?: SocialUser[];
      /** 用户账号 */
      username: string;
      /** 是否初始密码 */
      isOriginalPassword: boolean;
    };

    /** DeptSimpleRespVO，管理后台 - 部门精简信息 Response VO */
    export type DeptSimpleRespVO = {
      /** 部门描述 */
      deptDescription: string;
      /** 部门编号 */
      id: number;
      /** 部门名称 */
      name: string;
      /** 父部门 ID */
      parentId: number;
    };

    /** PostSimpleRespVO，管理后台 - 岗位信息的精简 Response VO */
    export type PostSimpleRespVO = {
      /** 岗位序号 */
      id: number;
      /** 岗位名称 */
      name: string;
    };

    /** RoleSimpleRespVO，管理后台 - 角色精简信息 Response VO */
    export type RoleSimpleRespVO = {
      /** 角色编码 */
      code: string;
      /** 角色编号 */
      id: number;
      /** 角色名称 */
      name: string;
    };

    /** SocialUser，社交用户 */
    export type SocialUser = {
      /** 社交用户的 openid */
      openid: string;
      /** 社交平台的类型，参见 SocialTypeEnum 枚举类 */
      type: number;
    };
    /** AuthPermissionInfoRespVO，管理后台 - 登录用户的权限信息 Response VO，额外包括用户信息和角色列表 */
    export type AuthPermissionInfoRespVO = {
      /** 菜单树 */
      menus: MenuVO[];
      /** 操作权限数组 */
      permissions: string[];
      /** 角色标识数组 */
      roles: string[];
      user: UserVO;
    };

    /** MenuVO，管理后台 - 登录用户的菜单信息 Response VO */
    export type MenuVO = {
      /** 是否总是显示 */
      alwaysShow?: boolean;
      children?: MenuVO[];
      /** 组件路径,仅菜单类型为菜单时，才需要传 */
      component?: string;
      /** 组件名 */
      componentName?: string;
      /** 菜单图标,仅菜单类型为菜单或者目录时，才需要传 */
      icon?: string;
      /** 菜单名称 */
      id: number;
      /** 是否缓存 */
      keepAlive: boolean;
      /** 菜单名称 */
      name: string;
      /** 父菜单 ID */
      parentId: number;
      /** 路由地址,仅菜单类型为菜单或者目录时，才需要传 */
      path?: string;
      /** 是否可见 */
      visible: boolean;
    };

    /** UserVO，用户信息 VO */
    export type UserVO = {
      /** 用户头像 */
      avatar: string;
      /** 部门编号 */
      deptId: number;
      /** 用户编号 */
      id: number;
      /** 用户昵称 */
      nickname: string;
    };
    type UserSelectPage = {
      list: User[]; // 确保 list 存在
      total?: number;
    };
    /** user add params */
    type UserAddParams = Pick<
      Api.SystemManage.User,
      'username' | 'sex' | 'mobile' | 'email' | 'status' | 'nickname' | 'password' | 'deptId' | 'postIds'
    >;

    /** user update params */
    type UserUpdateParams = CommonType.RecordNullable<Pick<Api.SystemManage.User, 'id'>> & UserAddParams;

    /** user search params */
    type UserSearchParams = CommonType.RecordNullable<
      Pick<Api.SystemManage.User, 'username' | 'sex' | 'nickname' | 'mobile' | 'email' | 'status' | 'dingtalkUserId'> &
        Common.CommonSearchParams
    >;

    /** user list */
    type UserList = Common.PaginatingQueryRecord<User>;

    /** user select */
    type UserSelect = Array<User>;

    /** 钉钉用户列表 */
    interface DingTalkUser {
      depName: string;
      admin: boolean;
      boss: boolean;
      name: string;
      unionid: string;
      userid: string;
    }
    type MenuType = '1' | '2';
    type IconType = '1' | '2';
    type TreeDeptResp = {
      /** 子部门 */
      children?: TreeDeptResp[];
      /** 创建时间 */
      createTime: Date;
      /** 部门描述 */
      deptDescription: string;
      /** 邮箱 */
      email?: string;
      /** 外部系统的部门 ID */
      externalDeptId?: number;
      /** 部门编号 */
      id: number;
      /** 负责人的用户编号 */
      leaderUserId?: number;
      /** 部门名称 */
      name: string;
      /** 父部门 ID */
      parentId?: number;
      /** 联系电话 */
      phone?: string;
      /** 显示顺序 */
      sort: number;
      /** 状态,见 CommonStatusEnum 枚举 */
      status: number;
    };
    type TreeDeptRespList = Array<TreeDeptResp>;
    type TreeMenuRespVO = {
      /** 是否总是显示 */
      alwaysShow?: boolean;
      /** 子菜单 */
      children?: TreeMenuRespVO[];
      /** 组件路径,仅菜单类型为菜单时，才需要传 */
      component?: string;
      /** 组件名 */
      componentName?: string;
      /** 创建时间 */
      createTime: Date;
      /** 菜单图标,仅菜单类型为菜单或者目录时，才需要传 */
      icon?: string;
      /** 菜单编号 */
      id: number;
      /** 是否缓存 */
      keepAlive?: boolean;
      /** 菜单名称 */
      name: string;
      /** 父菜单 ID */
      parentId: number;
      /** 路由地址,仅菜单类型为菜单或者目录时，才需要传 */
      path?: string;
      /** 权限标识,仅菜单类型为按钮时，才需要传递 */
      permission?: string;
      /** 显示顺序 */
      sort: number;
      /** 状态,见 CommonStatusEnum 枚举 */
      status: number;
      /** 类型，参见 MenuTypeEnum 枚举类 */
      type: number;
      /** 是否可见 */
      visible?: boolean;

      switchLoading?: boolean;
      switchVisibleLoading?: boolean;
    };
    type TreeMenuRespVOList = Array<TreeMenuRespVO>;
    /** MenuSaveVO，管理后台 - 菜单创建/修改 Request VO */
    type MenuSaveVO = {
      /** 是否总是显示 */
      alwaysShow?: boolean;
      /** 组件路径,仅菜单类型为菜单时，才需要传 */
      component?: string;
      /** 组件名 */
      componentName?: string;
      /** 菜单图标,仅菜单类型为菜单或者目录时，才需要传 */
      icon?: string;
      /** 菜单编号 */
      id?: number;
      /** 是否缓存 */
      keepAlive?: boolean;
      /** 菜单名称 */
      name: string;
      /** 父菜单 ID */
      parentId: number;
      /** 路由地址,仅菜单类型为菜单或者目录时，才需要传 */
      path?: string;
      /** 权限标识,仅菜单类型为按钮时，才需要传递 */
      permission?: string;
      /** 显示顺序 */
      sort: number;
      /** 状态,见 CommonStatusEnum 枚举 */
      status: number;
      /** 类型，参见 MenuTypeEnum 枚举类 */
      type: number;
      /** 是否可见 */
      visible: boolean;
    };
    type PermissionAssignRoleMenuRespVO = {
      /** 全选菜单编号列表 */
      menuIds?: number[];
      /** 半选菜单编号列表 */
      selectMenuIds?: number[];
    };
    type PageResultDictTypeRespVO = {
      /** 数据 */
      list: DictTypeRespVO[];
      /** 当前页码,原样返回 */
      pageNo?: number;
      /** 当前页实际数量 */
      pageSize: number;
      /** 总量 */
      total: number;
    };

    /** DictTypeRespVO，管理后台 - 字典类型信息 Response VO */
    type DictTypeRespVO = Common.CommonRecord<{
      /** 字典编号 */
      id: number;
      /** 字典名称 */
      name: string;
      /** 备注 */
      remark?: string;
      /** 状态，参见 CommonStatusEnum 枚举类 */
      status: number;
      /** 字典类型 */
      type: string;
    }>;
  }
}
