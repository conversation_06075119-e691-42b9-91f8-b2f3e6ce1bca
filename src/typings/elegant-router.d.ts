/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

declare module "@elegant-router/types" {
  type ElegantConstRoute = import('@elegant-router/vue').ElegantConstRoute;

  /**
   * route layout
   */
  export type RouteLayout = "base" | "blank";

  /**
   * route map
   */
  export type RouteMap = {
    "root": "/";
    "not-found": "/:pathMatch(.*)*";
    "403": "/403";
    "404": "/404";
    "500": "/500";
    "crm": "/crm";
    "crm_customer": "/crm/customer";
    "crm_follow": "/crm/follow";
    "crm_plan": "/crm/plan";
    "crm_project": "/crm/project";
    "crm_target": "/crm/target";
    "dingtalk-callback": "/dingtalk-callback";
    "home": "/home";
    "hrm": "/hrm";
    "hrm_company-management": "/hrm/company-management";
    "hrm_file-management": "/hrm/file-management";
    "hrm_file-management_contract": "/hrm/file-management/contract";
    "hrm_roster": "/hrm/roster";
    "hrm_roster_abnormal": "/hrm/roster/abnormal";
    "hrm_roster_admission-conditions": "/hrm/roster/admission-conditions";
    "hrm_roster_black": "/hrm/roster/black";
    "hrm_roster_detail": "/hrm/roster/detail";
    "hrm_roster_entry-pending": "/hrm/roster/entry-pending";
    "hrm_roster_export": "/hrm/roster/export";
    "hrm_roster_left": "/hrm/roster/left";
    "hrm_roster_left-company": "/hrm/roster/left-company";
    "hrm_roster_left-history": "/hrm/roster/left-history";
    "hrm_roster_on-job": "/hrm/roster/on-job";
    "hrm_roster_on-job-contract": "/hrm/roster/on-job-contract";
    "hrm_roster_probation": "/hrm/roster/probation";
    "hrm_roster_profile": "/hrm/roster/profile";
    "hrm_roster_promoted": "/hrm/roster/promoted";
    "hrm_roster_qrcode": "/hrm/roster/qrcode";
    "hrm_roster_resign-contract": "/hrm/roster/resign-contract";
    "hrm_roster_resume-launch": "/hrm/roster/resume-launch";
    "hrm_roster_review": "/hrm/roster/review";
    "hrm_roster_transfer": "/hrm/roster/transfer";
    "hrm_roster_user": "/hrm/roster/user";
    "hrm_signature-account": "/hrm/signature-account";
    "hrm_work-number": "/hrm/work-number";
    "iframe-page": "/iframe-page/:url";
    "kip": "/kip";
    "kip_bsc": "/kip/bsc";
    "kip_key-issues": "/kip/key-issues";
    "kip_task-center": "/kip/task-center";
    "login": "/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat)?";
    "system": "/system";
    "system_dept": "/system/dept";
    "system_dict": "/system/dict";
    "system_dict_dict-type": "/system/dict/dict-type";
    "system_menu": "/system/menu";
    "system_role": "/system/role";
    "system_user": "/system/user";
    "system_version": "/system/version";
  };

  /**
   * route key
   */
  export type RouteKey = keyof RouteMap;

  /**
   * route path
   */
  export type RoutePath = RouteMap[RouteKey];

  /**
   * custom route key
   */
  export type CustomRouteKey = Extract<
    RouteKey,
    | "root"
    | "not-found"
  >;

  /**
   * the generated route key
   */
  export type GeneratedRouteKey = Exclude<RouteKey, CustomRouteKey>;

  /**
   * the first level route key, which contain the layout of the route
   */
  export type FirstLevelRouteKey = Extract<
    RouteKey,
    | "403"
    | "404"
    | "500"
    | "crm"
    | "dingtalk-callback"
    | "home"
    | "hrm"
    | "iframe-page"
    | "kip"
    | "login"
    | "system"
  >;

  /**
   * the custom first level route key
   */
  export type CustomFirstLevelRouteKey = Extract<
    CustomRouteKey,
    | "root"
    | "not-found"
  >;

  /**
   * the last level route key, which has the page file
   */
  export type LastLevelRouteKey = Extract<
    RouteKey,
    | "403"
    | "404"
    | "500"
    | "dingtalk-callback"
    | "iframe-page"
    | "login"
    | "crm_customer"
    | "crm_follow"
    | "crm_plan"
    | "crm_project"
    | "crm_target"
    | "home"
    | "hrm_company-management"
    | "hrm_file-management_contract"
    | "hrm_roster_abnormal"
    | "hrm_roster_admission-conditions"
    | "hrm_roster_black"
    | "hrm_roster_detail"
    | "hrm_roster_entry-pending"
    | "hrm_roster_export"
    | "hrm_roster_left-company"
    | "hrm_roster_left-history"
    | "hrm_roster_left"
    | "hrm_roster_on-job-contract"
    | "hrm_roster_on-job"
    | "hrm_roster_probation"
    | "hrm_roster_profile"
    | "hrm_roster_promoted"
    | "hrm_roster_qrcode"
    | "hrm_roster_resign-contract"
    | "hrm_roster_resume-launch"
    | "hrm_roster_review"
    | "hrm_roster_transfer"
    | "hrm_roster_user"
    | "hrm_signature-account"
    | "hrm_work-number"
    | "kip_bsc"
    | "kip_key-issues"
    | "kip_task-center"
    | "system_dept"
    | "system_dict_dict-type"
    | "system_dict"
    | "system_menu"
    | "system_role"
    | "system_user"
    | "system_version"
  >;

  /**
   * the custom last level route key
   */
  export type CustomLastLevelRouteKey = Extract<
    CustomRouteKey,
    | "root"
    | "not-found"
  >;

  /**
   * the single level route key
   */
  export type SingleLevelRouteKey = FirstLevelRouteKey & LastLevelRouteKey;

  /**
   * the custom single level route key
   */
  export type CustomSingleLevelRouteKey = CustomFirstLevelRouteKey & CustomLastLevelRouteKey;

  /**
   * the first level route key, but not the single level
  */
  export type FirstLevelRouteNotSingleKey = Exclude<FirstLevelRouteKey, SingleLevelRouteKey>;

  /**
   * the custom first level route key, but not the single level
   */
  export type CustomFirstLevelRouteNotSingleKey = Exclude<CustomFirstLevelRouteKey, CustomSingleLevelRouteKey>;

  /**
   * the center level route key
   */
  export type CenterLevelRouteKey = Exclude<GeneratedRouteKey, FirstLevelRouteKey | LastLevelRouteKey>;

  /**
   * the custom center level route key
   */
  export type CustomCenterLevelRouteKey = Exclude<CustomRouteKey, CustomFirstLevelRouteKey | CustomLastLevelRouteKey>;

  /**
   * the center level route key
   */
  type GetChildRouteKey<K extends RouteKey, T extends RouteKey = RouteKey> = T extends `${K}_${infer R}`
    ? R extends `${string}_${string}`
      ? never
      : T
    : never;

  /**
   * the single level route
   */
  type SingleLevelRoute<K extends SingleLevelRouteKey = SingleLevelRouteKey> = K extends string
    ? Omit<ElegantConstRoute, 'children'> & {
        name: K;
        path: RouteMap[K];
        component: `layout.${RouteLayout}$view.${K}`;
      }
    : never;

  /**
   * the last level route
   */
  type LastLevelRoute<K extends GeneratedRouteKey> = K extends LastLevelRouteKey
    ? Omit<ElegantConstRoute, 'children'> & {
        name: K;
        path: RouteMap[K];
        component: `view.${K}`;
      }
    : never;
  
  /**
   * the center level route
   */
  type CenterLevelRoute<K extends GeneratedRouteKey> = K extends CenterLevelRouteKey
    ? Omit<ElegantConstRoute, 'component'> & {
        name: K;
        path: RouteMap[K];
        children: (CenterLevelRoute<GetChildRouteKey<K>> | LastLevelRoute<GetChildRouteKey<K>>)[];
      }
    : never;

  /**
   * the multi level route
   */
  type MultiLevelRoute<K extends FirstLevelRouteNotSingleKey = FirstLevelRouteNotSingleKey> = K extends string
    ? ElegantConstRoute & {
        name: K;
        path: RouteMap[K];
        component: `layout.${RouteLayout}`;
        children: (CenterLevelRoute<GetChildRouteKey<K>> | LastLevelRoute<GetChildRouteKey<K>>)[];
      }
    : never;
  
  /**
   * the custom first level route
   */
  type CustomSingleLevelRoute<K extends CustomFirstLevelRouteKey = CustomFirstLevelRouteKey> = K extends string
    ? Omit<ElegantConstRoute, 'children'> & {
        name: K;
        path: RouteMap[K];
        component?: `layout.${RouteLayout}$view.${LastLevelRouteKey}`;
      }
    : never;

  /**
   * the custom last level route
   */
  type CustomLastLevelRoute<K extends CustomRouteKey> = K extends CustomLastLevelRouteKey
    ? Omit<ElegantConstRoute, 'children'> & {
        name: K;
        path: RouteMap[K];
        component?: `view.${LastLevelRouteKey}`;
      }
    : never;

  /**
   * the custom center level route
   */
  type CustomCenterLevelRoute<K extends CustomRouteKey> = K extends CustomCenterLevelRouteKey
    ? Omit<ElegantConstRoute, 'component'> & {
        name: K;
        path: RouteMap[K];
        children: (CustomCenterLevelRoute<GetChildRouteKey<K>> | CustomLastLevelRoute<GetChildRouteKey<K>>)[];
      }
    : never;

  /**
   * the custom multi level route
   */
  type CustomMultiLevelRoute<K extends CustomFirstLevelRouteNotSingleKey = CustomFirstLevelRouteNotSingleKey> =
    K extends string
      ? ElegantConstRoute & {
          name: K;
          path: RouteMap[K];
          component: `layout.${RouteLayout}`;
          children: (CustomCenterLevelRoute<GetChildRouteKey<K>> | CustomLastLevelRoute<GetChildRouteKey<K>>)[];
        }
      : never;

  /**
   * the custom route
   */
  type CustomRoute = CustomSingleLevelRoute | CustomMultiLevelRoute;

  /**
   * the generated route
   */
  type GeneratedRoute = SingleLevelRoute | MultiLevelRoute;

  /**
   * the elegant route
   */
  type ElegantRoute = GeneratedRoute | CustomRoute;
}
