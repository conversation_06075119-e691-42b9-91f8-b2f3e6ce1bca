/** The common type namespace */
declare namespace CommonType {
  /** The strategic pattern */
  interface StrategicPattern {
    /** The condition */
    condition: boolean;
    /** If the condition is true, then call the action function */
    callback: () => void;
  }

  /**
   * The option type
   *
   * @property value: The option value
   * @property label: The option label
   */
  type Option<K = string> = { value: K; label: string; [key: string]: any };

  type YesOrNo = 'Y' | 'N';
  /** The id type */
  type IdType = string | number;
  /** add null to all properties */
  type RecordNullable<T> = {
    [K in keyof T]?: T[K] | null;
  };

  /** The configuration options for constructing tree structure data */
  type TreeConfig = {
    /** id field name */
    idField: string;
    /** parent id field name */
    parentIdField?: string;
    /** children field name */
    childrenField?: string;
    /** filter function */
    filterFn?: (node: any) => boolean;
  };
}
