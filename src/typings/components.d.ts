/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AppProvider: typeof import('./../components/common/app-provider.vue')['default']
    BetterScroll: typeof import('./../components/custom/better-scroll.vue')['default']
    ButtonIcon: typeof import('./../components/custom/button-icon.vue')['default']
    copy: typeof import('./../components/common/form-item-des copy/index.vue')['default']
    CountTo: typeof import('./../components/custom/count-to.vue')['default']
    CustomerButton: typeof import('./../components/common/customer-project-btn/customer-button.vue')['default']
    DarkModeContainer: typeof import('./../components/common/dark-mode-container.vue')['default']
    DateTimePicker: typeof import('./../components/common/date-time-picker.vue')['default']
    DeptTreeSelect: typeof import('./../components/common/dept-tree-select.vue')['default']
    DownloadBtn: typeof import('./../components/common/download-btn/index.vue')['default']
    ExceptionBase: typeof import('./../components/common/exception-base.vue')['default']
    FormGiItemDes: typeof import('./../components/common/form-gi-item-des/index.vue')['default']
    FormItemDes: typeof import('./../components/common/form-item-des/index.vue')['default']
    FormTip: typeof import('./../components/custom/form-tip.vue')['default']
    FullScreen: typeof import('./../components/common/full-screen.vue')['default']
    IconAntDesignReloadOutlined: typeof import('~icons/ant-design/reload-outlined')['default']
    IconAntDesignSettingOutlined: typeof import('~icons/ant-design/setting-outlined')['default']
    IconGridiconsFullscreen: typeof import('~icons/gridicons/fullscreen')['default']
    IconGridiconsFullscreenExit: typeof import('~icons/gridicons/fullscreen-exit')['default']
    IconIcOutlineMoreHoriz: typeof import('~icons/ic/outline-more-horiz')['default']
    IconIcRoundAdd: typeof import('~icons/ic/round-add')['default']
    IconIcRoundArrowBack: typeof import('~icons/ic/round-arrow-back')['default']
    IconIcRoundCh: typeof import('~icons/ic/round-ch')['default']
    IconIcRoundCheck: typeof import('~icons/ic/round-check')['default']
    IconIcRoundCheckCircle: typeof import('~icons/ic/round-check-circle')['default']
    IconIcRoundDelete: typeof import('~icons/ic/round-delete')['default']
    IconIcRoundPlus: typeof import('~icons/ic/round-plus')['default']
    IconIcRoundRefg: typeof import('~icons/ic/round-refg')['default']
    IconIcRoundRefresh: typeof import('~icons/ic/round-refresh')['default']
    IconIcRoundSearch: typeof import('~icons/ic/round-search')['default']
    IconIcRoundSort: typeof import('~icons/ic/round-sort')['default']
    IconIcRoundTemplate: typeof import('~icons/ic/round-template')['default']
    IconIcRoundTemplateFilled: typeof import('~icons/ic/round-template-filled')['default']
    IconIcRoundWarning: typeof import('~icons/ic/round-warning')['default']
    IconMaterialSymbolsAddRounded: typeof import('~icons/material-symbols/add-rounded')['default']
    IconMaterialSymbolsDeleteOutline: typeof import('~icons/material-symbols/delete-outline')['default']
    IconMaterialSymbolsDriveFileRenameOutlineOutline: typeof import('~icons/material-symbols/drive-file-rename-outline-outline')['default']
    IconMdiCloseCircle: typeof import('~icons/mdi/close-circle')['default']
    IconMdiDrag: typeof import('~icons/mdi/drag')['default']
    IconMdiHelpCircle: typeof import('~icons/mdi/help-circle')['default']
    IconMdiRefresh: typeof import('~icons/mdi/refresh')['default']
    IconPajamasIssueTypeFeatureFlag: typeof import('~icons/pajamas/issue-type-feature-flag')['default']
    'IconQuill:collapse': typeof import('~icons/quill/collapse')['default']
    'IconQuill:expand': typeof import('~icons/quill/expand')['default']
    ImageDocuments: typeof import('./../components/common/image-documents.vue')['default']
    LangSwitch: typeof import('./../components/common/lang-switch.vue')['default']
    LookForward: typeof import('./../components/custom/look-forward.vue')['default']
    Markdown: typeof import('./../components/common/markdown/index.vue')['default']
    MenuToggler: typeof import('./../components/common/menu-toggler.vue')['default']
    ModuleHeader: typeof import('./../components/common/module-header/index.vue')['default']
    NAlert: typeof import('naive-ui')['NAlert']
    NAnchor: typeof import('naive-ui')['NAnchor']
    NAnchorLink: typeof import('naive-ui')['NAnchorLink']
    NBadge: typeof import('naive-ui')['NBadge']
    NBreadcrumb: typeof import('naive-ui')['NBreadcrumb']
    NBreadcrumbItem: typeof import('naive-ui')['NBreadcrumbItem']
    NBU: typeof import('naive-ui')['NBU']
    NButton: typeof import('naive-ui')['NButton']
    NCalendar: typeof import('naive-ui')['NCalendar']
    NCard: typeof import('naive-ui')['NCard']
    NCarousel: typeof import('naive-ui')['NCarousel']
    NCarouselItem: typeof import('naive-ui')['NCarouselItem']
    NCheckbox: typeof import('naive-ui')['NCheckbox']
    NCheckboxGroup: typeof import('naive-ui')['NCheckboxGroup']
    NCol: typeof import('naive-ui')['NCol']
    NCollapse: typeof import('naive-ui')['NCollapse']
    NCollapseItem: typeof import('naive-ui')['NCollapseItem']
    NColorPicker: typeof import('naive-ui')['NColorPicker']
    NDataTable: typeof import('naive-ui')['NDataTable']
    NDatePicker: typeof import('naive-ui')['NDatePicker']
    NDescriptions: typeof import('naive-ui')['NDescriptions']
    NDescriptionsItem: typeof import('naive-ui')['NDescriptionsItem']
    NDialogProvider: typeof import('naive-ui')['NDialogProvider']
    NDivider: typeof import('naive-ui')['NDivider']
    NDrawer: typeof import('naive-ui')['NDrawer']
    NDrawerContent: typeof import('naive-ui')['NDrawerContent']
    NDropdown: typeof import('naive-ui')['NDropdown']
    NDynamicInput: typeof import('naive-ui')['NDynamicInput']
    NEllipsis: typeof import('naive-ui')['NEllipsis']
    NEmpty: typeof import('naive-ui')['NEmpty']
    NFlex: typeof import('naive-ui')['NFlex']
    NForm: typeof import('naive-ui')['NForm']
    NFormItem: typeof import('naive-ui')['NFormItem']
    NFormItemGi: typeof import('naive-ui')['NFormItemGi']
    NGi: typeof import('naive-ui')['NGi']
    NGrid: typeof import('naive-ui')['NGrid']
    NGridItem: typeof import('naive-ui')['NGridItem']
    NIcon: typeof import('naive-ui')['NIcon']
    NIma: typeof import('naive-ui')['NIma']
    NImage: typeof import('naive-ui')['NImage']
    NImageGroup: typeof import('naive-ui')['NImageGroup']
    NInput: typeof import('naive-ui')['NInput']
    NInputGroup: typeof import('naive-ui')['NInputGroup']
    NInputGroupLabel: typeof import('naive-ui')['NInputGroupLabel']
    NInputNumber: typeof import('naive-ui')['NInputNumber']
    NInputOtp: typeof import('naive-ui')['NInputOtp']
    NLayout: typeof import('naive-ui')['NLayout']
    NLayoutContent: typeof import('naive-ui')['NLayoutContent']
    NLayoutSider: typeof import('naive-ui')['NLayoutSider']
    NLoadingBarProvider: typeof import('naive-ui')['NLoadingBarProvider']
    NMenu: typeof import('naive-ui')['NMenu']
    NMessageProvider: typeof import('naive-ui')['NMessageProvider']
    NModal: typeof import('naive-ui')['NModal']
    NModalProvider: typeof import('naive-ui')['NModalProvider']
    NNotificationProvider: typeof import('naive-ui')['NNotificationProvider']
    NOption: typeof import('naive-ui')['NOption']
    NPagination: typeof import('naive-ui')['NPagination']
    NPerformantEllipsis: typeof import('naive-ui')['NPerformantEllipsis']
    NPopconfirm: typeof import('naive-ui')['NPopconfirm']
    NPopover: typeof import('naive-ui')['NPopover']
    NProgress: typeof import('naive-ui')['NProgress']
    NRadio: typeof import('naive-ui')['NRadio']
    NRadioButton: typeof import('naive-ui')['NRadioButton']
    NRadioGroup: typeof import('naive-ui')['NRadioGroup']
    NResult: typeof import('naive-ui')['NResult']
    NRow: typeof import('naive-ui')['NRow']
    NS: typeof import('naive-ui')['NS']
    NScrollbar: typeof import('naive-ui')['NScrollbar']
    NSelect: typeof import('naive-ui')['NSelect']
    NSkeleton: typeof import('naive-ui')['NSkeleton']
    NSpace: typeof import('naive-ui')['NSpace']
    NSpin: typeof import('naive-ui')['NSpin']
    NStep: typeof import('naive-ui')['NStep']
    NSteps: typeof import('naive-ui')['NSteps']
    NSwitch: typeof import('naive-ui')['NSwitch']
    NTab: typeof import('naive-ui')['NTab']
    NTable: typeof import('naive-ui')['NTable']
    NTabPane: typeof import('naive-ui')['NTabPane']
    NTabs: typeof import('naive-ui')['NTabs']
    NTag: typeof import('naive-ui')['NTag']
    NText: typeof import('naive-ui')['NText']
    NTimeline: typeof import('naive-ui')['NTimeline']
    NTimelineItem: typeof import('naive-ui')['NTimelineItem']
    NTooltip: typeof import('naive-ui')['NTooltip']
    NTree: typeof import('naive-ui')['NTree']
    NTreeSelect: typeof import('naive-ui')['NTreeSelect']
    NVirtualList: typeof import('naive-ui')['NVirtualList']
    NWatermark: typeof import('naive-ui')['NWatermark']
    PinToggler: typeof import('./../components/common/pin-toggler.vue')['default']
    PositionPro: typeof import('./../components/common/position-pro.vue')['default']
    PositionProperty: typeof import('./../components/common/position-property.vue')['default']
    PreviewFile: typeof import('./../components/common/preview-file.vue')['default']
    ProCard: typeof import('pro-naive-ui')['ProCard']
    ProDataTable: typeof import('pro-naive-ui')['ProDataTable']
    ProDeptSelect: typeof import('./../components/common/pro-dept-select/pro-dept-select/index.vue')['default']
    ProDigit: typeof import('pro-naive-ui')['ProDigit']
    ProField: typeof import('pro-naive-ui')['ProField']
    ProFormList: typeof import('pro-naive-ui')['ProFormList']
    ProInput: typeof import('pro-naive-ui')['ProInput']
    ProjectButton: typeof import('./../components/common/customer-project-btn/project-button.vue')['default']
    ProModalForm: typeof import('pro-naive-ui')['ProModalForm']
    ProRadioGroup: typeof import('pro-naive-ui')['ProRadioGroup']
    ProSearchForm: typeof import('pro-naive-ui')['ProSearchForm']
    ProSelect: typeof import('pro-naive-ui')['ProSelect']
    ProTextarea: typeof import('pro-naive-ui')['ProTextarea']
    ReButton: typeof import('./../components/common/re-button.vue')['default']
    ReloadButton: typeof import('./../components/common/reload-button.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SelectWithSearch: typeof import('./../components/common/select-with-search.vue')['default']
    SoybeanAvatar: typeof import('./../components/custom/soybean-avatar.vue')['default']
    SvgIcon: typeof import('./../components/custom/svg-icon.vue')['default']
    SystemLogo: typeof import('./../components/common/system-logo.vue')['default']
    TableColumnSetting: typeof import('./../components/advanced/table-column-setting.vue')['default']
    TableHeaderOperation: typeof import('./../components/advanced/table-header-operation.vue')['default']
    TableInfoList: typeof import('./../components/common/table-info-list.vue')['default']
    TableSiderLayout: typeof import('./../components/advanced/table-sider-layout.vue')['default']
    TagEnumView: typeof import('./../components/common/tag-enum-view.vue')['default']
    ThemeSchemaSwitch: typeof import('./../components/common/theme-schema-switch.vue')['default']
    ToolTipHover: typeof import('./../components/common/tool-tip-hover/index.vue')['default']
    Verify: typeof import('./../components/common/verifition/src/Verify.vue')['default']
    VerifyPoints: typeof import('./../components/common/verifition/src/Verify/VerifyPoints.vue')['default']
    VerifySlide: typeof import('./../components/common/verifition/src/Verify/VerifySlide.vue')['default']
    WangeEditor: typeof import('./../components/common/wange-editor.vue')['default']
    WaveBg: typeof import('./../components/custom/wave-bg.vue')['default']
  }
}
