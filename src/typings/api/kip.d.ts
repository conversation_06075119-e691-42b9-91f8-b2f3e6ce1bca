declare namespace Api {
  namespace Kip {
    // *********** 责任部门 ***********
    // 责任部门参数
    type Dept = {
      /** id 责任部门id */
      id: number;
      /** 责任部门名称 责任部门名称 */
      name: string;
      /** 责任部门角色定位 责任部门角色定位 */
      responsibility?: string;
      externalDeptId: number;
    };
    // 部门列表
    type DeptList = Array<Dept>;
    //   部门搜索
    type DeptSearchParams = CommonType.RecordNullable<Pick<Dept, 'name' | 'responsibility'>>;
    // *********** 关键事项 ***********
    //  关键事项参数
    type KeyIssues = {
      id: number;
      /** 所属月份，从 1 开始 */
      months?: number[];
      issueOrder?: number;
      /** 所属年份 */
      year?: number;

      /** 日期（完整年月日） */
      atDate: string;

      /** 责任部门 ID */
      deptId: number;

      /** 责任部门对象 */
      dept: Dept;

      sort: string;
      /** 责任部门名称 */
      deptName?: string;

      /** 责任部门描述 */
      deptDescription?: string;

      /** 指标 ID */
      metricId: number | null;
      updateDate: string;

      /** 指标对象 */
      metric: {
        id: number;
        name: string;
        level?: string;
        sort?: string;
      };

      /** 关键事项名称 */
      name?: string;

      /** 预期结果 */
      exceptedResult?: string;

      /** 模块 */
      module?: string;

      /** 资源需求 */
      resourceDemand?: string;

      /** 协同部门（ID列表） */
      withDept?: {
        id: number;
        name: string;
        type?: string;
      }[];

      /** 协同部门（完整对象列表） */
      withDeptList?: {
        id: number;
        name: string;
        type?: string;
      }[];

      /** 完成情况类型 */
      deliveredType?: number;

      /** 完成情况说明 */
      deliveryOutcome?: string;
      strategicBlueprint: string;
    };

    // 表单
    type KeyIssuesFormParams = CommonType.RecordNullable<
      Pick<
        KeyIssues,
        | 'name'
        | 'module'
        | 'deptId'
        | 'exceptedResult'
        | 'resourceDemand'
        | 'withDept'
        | 'id'
        | 'atDate'
        | 'dept'
        | 'deliveredType'
        | 'deliveryOutcome'
        | 'metricId'
        | 'withDeptList'
        | 'metric'
        | 'strategicBlueprint'
        | 'year'
        | 'months'
        | 'sort'
      >
    >;
    type KeyIssuesFormSearch = CommonType.RecordNullable<Pick<KeyIssues, 'name' | 'deptId' | 'metricId' | 'year'>>;
    // 关键事项列表
    type KeyIssuesList = Array<KeyIssues>;
    //   查询详情
    type KeyIssuesDetail = CommonType.RecordNullable<Pick<KeyIssues, 'id'>>;
    /** *********** 部门 ********** */
    // 部门
    type DeptSelect = {
      /** 部门编号 */
      id: number;
      /** 部门名称 */
      name: string;
      /** 父部门 ID */
      parentId: number;
      /** 角色定位 ID */
      deptDescription: string;
      // 状态
      status: number;
      //   工号规则
      initialJobNumber: string;
      //   最大工号
      maxJobNumber: string;
    };
    type DeptSelectForm = CommonType.RecordNullable<Pick<DeptSelect, 'name' | 'id' | 'deptDescription' | 'status'>>;
    // 部门列表
    type DeptSelectList = Array<DeptSelect>;
    /** *********** bsc ********** */
    type BscInfo = {
      id: number;
      name: string;
    };
    type BscParams = {
      /** 数据输出部门 */
      dataOutputDept: string;
      // 是否拆解关键事项
      haveKeyIssues?: boolean;
      /** 责任部门 */
      dept: Array<BscInfo>;
      /** 计算口径 */
      formula: string;
      /** 战略指标id */
      id: number;
      /** 层面id,sys_dict.type=kip_bsc_metric_level */
      levelId: number;
      // 单位
      label: number;
      /** 战略指标名称 */
      metricName: string;
      /** 数据周期,sys_dict.type=kip_bsc_statistic_cycle */
      statisticCycle: number;
      /** 战略目标名称 */
      targetName: string;
      sort: string;
      /** 责任人 */
      user?: Array<BscInfo>;
    };
    type BscList = Array<BscParams>;

    type BscFormParams = CommonType.RecordNullable<
      BscParams & {
        metricId: number;
        deptId: number[];
        userId: number[];
        label: number;
        year: string;
        target: string;
        invalid: number;
        keyIssues?: KeyIssuesFormParams[];
      }
    >;
    type BscSearchParams = CommonType.RecordNullable<BscParams>;
    type MetricInfoRespVo = {
      /** 实际数据 */
      actual?: string | null;
      /** 预期数据 */
      excepted?: string | null;
      deviation?: string;
      /** 指标详情id */
      id: number;
      /** 单位,system_dict.kip_bsc_metric_label */
      label?: number;
      /** 所属月份 */
      month?: number;
      /** 战略指标id */
      parentId?: number;
      /** 所属年份 */
      year?: number;
    };

    type MetricInfoRespList = Array<MetricInfoRespVo>;
    type MetricInfoRespInfo = {
      data: MetricInfoRespList;
      id: number;
      ytdDeviation: string;
      ytdTotal: string;
    };
    type UpdateYtd = {
      id: number;
      deviation?: string;
      total?: string;
    };
    type KipMetricsInfoDataReqVo = {
      /** 指标值 */
      excepted?: string;
      /** 指标详情id,为空时当作新增 */
      id?: number;
      /** 单位,system_dict.kip_bsc_metric_label */
      label?: number;
      /** 所属月份 */
      month: number;
    };
    type KipMetricsInfoDataReqList = Array<KipMetricsInfoDataReqVo>;
    type KipMetricsInfo = {
      parentId: number;
      year: number;
      data: MetricInfoRespList;
    };

    type KipSubMetricsInfo = {
      /** 实际数据统计起始时间 */
      actualBeginTime?: string;
      /** 实际数据统计结束时间 */
      actualEndTime?: string;
      /** 实际数据 */
      actualData?: number | null;
      id?: number;
      /** 指标详情id */
      metricsInfoId?: number;
      // 未转换的时间
      date: number | [number, number] | null;
    };
    type KipSubMetricsInfoList = Array<KipSubMetricsInfo>;

    type ActualMetricsDataRespVo = {
      data?: ActualDataInfoRespVo[];
      parentId?: number;
      sum: string;
      deviation: string;
    };

    type ActualDataInfoRespVo = {
      /** 实际数据 */
      actual?: string;
      deviation?: string;
      /** 该数据起始日期 */
      begin?: string;
      /** 该数据结束日期 */
      end?: string;
      /** 实际数据详情id */
      id?: number;
      date: [string | undefined, string | undefined];
    };
    type ActualDataInfoRespVoList = {
      sum: string;
      data: Array<ActualDataInfoRespVo>;
    };

    type MetricsWithKeyIssuesRespVo = {
      /** 指标id */
      metricId?: number | null;
      metric?: {
        id: number;
        /** 指标名称 */
        name?: string;
      } | null;
      /** 关键事项 */
      keyIssues?: KeyIssues[];
      /** 全盘规划 */
      strategicBlueprint?: string | null;
    };
    type MoveKeyIssueReqVo = {
      /** 关键事项id */
      keyIssueId: number;
      /** 转移后的指标id */
      metricId: number;
    };
    // 待办事项列表
    type QueryTodoListRespVO = {
      assignedBy: Api.SystemManage.User;
      /** 下发人id */
      assignedById: number;
      assignee: Api.SystemManage.User;
      /** 责任人id */
      assigneeId: string;
      /** 完成时间 */
      completedTime: string;
      /** 是否允许删除 */
      deletable: boolean;
      /** 是否允许编辑 */
      editable: boolean;
      /** id */
      id: number;
      /** 生成时间 */
      insertedTime: string;
      /** 待办事项名称 */
      name: string;
      /** 是否允许标记完成/未完成 */
      processable: boolean;
      processStatus: IdNameCompletionStatus;
      /** 是否允许评审 */
      reviewable: boolean;
      reviewStatus?: IdNameCompletionStatus;
    };
    type IdNameConfirmStatus = {
      /** id */
      id?: number;
      /** 名称 */
      name?: string;
    };

    /** IdNameLong，id-name对象 */
    export type IdNameLong = {
      /** id */
      id?: number;
      /** 名称 */
      name?: string;
    };

    type TaskCenterList = Common.PaginatingQueryRecord<QueryTodoListRespVO>;
    type TaskCenterSearchParams = CommonType.RecordNullable<
      Pick<QueryTodoListRespVO, 'assignedBy' | 'assignee' | 'name'> & {
        processStatus: QueryTodoListRespVO['processStatus'];
      } & Common.CommonSearchParams
    >;
    type TaskCenterFormParams = CommonType.RecordNullable<
      Pick<QueryTodoListRespVO, 'id' | 'name' | 'assignee' | 'processStatus'> & {
        completionStatus: IdNameCompletionStatus;
      }
    >;
    type TaskCenterPutParams = CommonType.RecordNullable<Pick<QueryTodoListRespVO, 'name'>>;
    /** MarkProcessedReqVO，标记为已处理 */
    export type TaskCenterPutProcessParams = {
      /** 处理情况说明 */
      description: string;
      /** 处理结果 */
      status: string;
    };

    /** TodoListDetailRespVO，待办事项详情 */
    export type TodoListDetailRespVO = {
      assignedBy: AdminUserDTO;
      /** 下发人id */
      assignedById: number;
      assignee: AdminUserDTO;
      /** 责任人id */
      assigneeId: number;
      /** 完成时间 */
      completedTime: string;
      /** 处理历史，按处理时间倒序排序 */
      histories?: TodoProcessedHistoryRespVO[];
      /** id */
      id: number;
      /** 生成时间 */
      insertedTime: string;
      /** 最后一次处理结果说明 */
      lastProcessDescription?: string;
      lastProcessStatus: IdNameCompletionStatus;
      /** 最后一次处理结果名称 */
      lastProcessStatusName?: string;
      /** 最后一次处理时间 */
      lastProcessTime?: string;
      /** 最后一次评审结果说明 */
      lastReviewDescription?: string;
      lastReviewer?: AdminUserDTO;
      /** 最后一次评审人id */
      lastReviewerId?: number;
      /** 最后一次评审结果状态 */
      lastReviewStatus?: string;
      /** 最后一次评审结果状态名称 */
      lastReviewStatusName?: string;
      /** 最后一次评审时间 */
      lastReviewTime?: string;
      /** 待办事项名称 */
      name: string;
      /** 评审为已通过时间 */
      reviewPassTime?: string;
      /** 最后更新时间 */
      updateTime: string;
    };
    type AdminUserDTO = {
      /** 用户头像 */
      avatar: string;
      /** 是否删除 */
      deleted: boolean;
      /** 部门编号 */
      deptId: number;
      /** 外部系统用户ID */
      externalUserId: string;
      /** 用户 ID */
      id: number;
      /** 手机号码 */
      mobile: string;
      /** 用户昵称 */
      nickname: string;
      /** 岗位编号数组 */
      postIds: number[];
      /** 帐号状态 */
      status: number;
      transMap?: { [key: string]: { [key: string]: any } };
    };
    /** IdNameCompletionStatus，id-name对象 */
    export type IdNameCompletionStatus = {
      /** id */
      id: string;
      /** 名称 */
      name: string;
    };

    /** TodoProcessedHistoryRespVO，待办事项处理历史 */
    export type TodoProcessedHistoryRespVO = {
      /** 处理说明 */
      description?: string;
      /** 处理时间 */
      processTime: string;
      /** 状态 */
      status: string;
    };
  }
}
