declare namespace Api {
  namespace Crm {
    // *********** 目标客户 ***********
    type Target = Common.CommonRecord<{
      name: string;
      businessType: string;
      businessTypes: Array<string>;
      bankType: number;
      businessCategory: string;
      isSuitable: number;
      biddingTime: string;
      updaterName: string;
    }>;

    type ProjectDetailKeys =
      | 'totalScale'
      | 'biddingTime'
      | 'contractEndTime'
      | 'expectedStartTime'
      | 'partnerDuration'
      | 'entryCountries'
      | 'singleEstimatedValue'
      | 'baseMode'
      | 'city'
      | 'averageRevenue'
      | 'requirement'
      | 'supplierInfo';

    type ExtendedProjectDetail = Pick<Api.Crm.ProjectDetail, ProjectDetailKeys> & {
      bankType: number;
      businessCategory: string;
      remark: string;
    };

    type TargetFormParams = CommonType.RecordNullable<Target> & CommonType.RecordNullable<ExtendedProjectDetail>;

    /** target search params */
    type TargetSearchParams = CommonType.RecordNullable<
      Pick<Api.Crm.Target, 'name' | 'businessType' | 'isSuitable'> & Common.CommonSearchParams
    >;

    /** target list */
    type TargetList = Common.PaginatingQueryRecord<Target & ExtendedProjectDetail>;

    /** target select */
    type TargetSelect = Array<Target>;

    // *********** 项目管理 ***********
    // 项目基本信息
    type ProjectBaseInfo = Common.CommonRecord<{
      targetCustomerId?: number;
      targetCustomer?: Target;
      bankName: string;
      averageRevenue: string;
      baseMode: string;
      biddingTime: string;
      businessType: string;
      middleman: string;
      city: string;
      contractEndTime: string;
      entryCountries: string;
      expectedStartTime: string;
      partnerDuration: string;
      singleEstimatedValue: string;
      status: number;
      totalScale: string;
      ownerUserId: number;
      user?: Api.SystemManage.User;
      winningProbability: string;
      workplaceType: number;
      requirement: string;
      supplierInfo: string;
      firstBatchSeats: string;
      projectStartDate: string;
      projectOnlineDate: string;
      remark: string;
      updaterName: string;
      updateTime: string;
    }>;

    // 服务内容确定
    type ProjectContentSure = {
      standardWorkdays: string;
      schedulingReq: string;
      trainingCycle: string;
      trainingSettle: string;
      trainingSupport: string;
      onSiteMgmt: string;
      businesss: string;
      dedicatedLineReq: string;
      lineReq: string;
      productConfirm: string;
    };

    // 数据确认
    type ProjectDataSure = {
      listAllocRule: string;
      merchantDistRule: string;
    };

    // 团队配置要求
    type ProjectTeam = {
      key: number;
      educationJob: string;
      educationReq: string;
      businessExp: string;
      mgmtStaffRatio: string;
    };
    type ProjectTeamConfig = {
      mgmtStaffRatio: ProjectTeam[];
    };

    // 职场配置要求
    type ProjectConfig = {
      exitWorkplaceCost: number;
    };

    // 招标确认
    type ProjectTender = {
      biddingProcess: string;
      supplyQty: string;
      biddingKeyPoints: string;
      contractYears: string;
    };

    // 项目基本信息表单
    type ProjectFormParams = ProjectBaseInfo &
      ProjectContentSure &
      ProjectDataSure &
      ProjectConfig &
      ProjectSettle &
      ProjectTender &
      ProjectTeamConfig;

    // 结算相关确认
    type ProjectSettle = {
      settlementRule: string;
      listQuantity: number;
      connectRate: number;
      callMethodLimit: string[];
      connectQty: number;
      avgSuccessCnt: number;
      refundRate: number;
      avgOrder: number;
      avgPerfMonth: number;
      avgRevenueMonth: number;
      settlementFactor: string;
      settlementStd: string;
      penaltyRisk: string;
      supportPolicy: string;
      systemCost: number;
      callCost: number;
      workplaceCost: number;
      rankingRule: string;
      reconSettleCycle: string;
      invoiceType: string;
      competitorProfit: string;
      otherNotes: string;
      connectConv: number;
    };

    // 项目详情
    type ProjectDetail = ProjectBaseInfo &
      ProjectContentSure &
      ProjectDataSure &
      ProjectConfig &
      ProjectSettle &
      ProjectTender &
      ProjectTeamConfig & {
        projectId?: number;
        isMarker: boolean;
      };

    // 添加项目返回的结果
    type ProjectResponse = {
      id: number;
    };

    // 项目分页列表
    type Project = ProjectBaseInfo;
    type TransferRequest = {
      /** 线索编号 */
      id: number;
      /** 新负责人的用户编号 */
      newOwnerUserId: number;
      /** 老负责人的用户编号 */
      ownerUserId: number;
      userName: string;
      isReceive: boolean;
    };
    type TransferModel = CommonType.RecordNullable<TransferRequest>;
    /** project search params */
    type ProjectSearchParams = CommonType.RecordNullable<
      Pick<Api.Crm.Project, 'bankName' | 'status' | 'ownerUserId'> & Common.CommonSearchParams & { all: boolean }
    >;

    /** project list */
    type ProjectList = Common.PaginatingQueryRecord<Project>;

    /** project select */
    type ProjectSelect = Array<Project>;

    // *********** 项目状态 ***********
    /** project state */
    type ProjectState = Common.CommonRecord<{
      scene: number;
      sceneObj: object;
      remarks: string;
      oldStatus: number;
      newStatus: number;
      handoverUser: Api.SystemManage.User;
      user: Api.SystemManage.User;
    }>;

    /** project state search params */
    interface ProjectStateSearchParams {
      projectId?: number;
    }

    // *********** 项目立项 ***********
    type MarkerSearchParams = {
      /** 项目编号 */
      projectId: number;
      /** 场景 */
      scene: number;
    };

    // 发起立项
    type MarkerTigger = {
      /** 发起说明 */
      tiggerComment: string;
      /** 发起人 */
      tiggerUserId: number;
      tiggerUser: Api.SystemManage.User;
      /** 发起时间 */
      createTime: string;
    } & MarkerSearchParams;

    // 立项确认
    type MarkerReview = {
      /** 确认说明 */
      confirmComment: string | null;
      /** 确认人 */
      confirmUserId: number;
      confirmUser: Api.SystemManage.User;
      /** 确认结果 */
      confirmResult: number;
      /** 确认时间 */
      confirmedAt: string;
    } & MarkerSearchParams;

    // 立项详情
    type Marker = Common.CommonRecord<MarkerTigger & MarkerReview>;

    // *********** 联络人管理 ***********
    /** customer */
    type Customer = Common.CommonRecord<{
      name: string;
      department: string;
      title: string;
      profileImage: string;
      projectId: number;
      project: Api.Crm.Project;
    }>;

    type CustomerFormParams = CommonType.RecordNullable<Customer>;

    /** customer search params */
    type CustomerSearchParams = CommonType.RecordNullable<
      Pick<Api.Crm.Customer, 'projectId' | 'name'> & { planId: number } & Common.CommonSearchParams
    >;

    /** customer list */
    type CustomerList = Common.PaginatingQueryRecord<Customer>;

    /** customer select */
    type CustomerSelect = Array<Customer>;

    // *********** 拜访记录 ***********
    /** follow */
    type Follow = Common.CommonRecord<{
      interactionLevel: string;
      isValid: number;
      summary: string;
      visitDate: string;
      project: Api.Crm.Project;
      projectId: number;
      user: Api.SystemManage.User;
      userId: number;
      customers: Array<Api.Crm.Customer>;
      customerIds: Array<number>;
      planId: number;
      plan?: Api.Crm.Plan;
    }>;

    type FollowFormParams = CommonType.RecordNullable<Follow>;

    /** follow add params */
    type FollowAddParams = FollowFormParams & PlanFormParams;

    /** follow search params */
    type FollowSearchParams = CommonType.RecordNullable<
      Pick<Follow, 'projectId' | 'userId' | 'customerIds' | 'isValid'>
    > &
      Common.CommonSearchParams;

    /** follow list */
    type FollowList = Common.PaginatingQueryRecord<Follow>;

    /** follow select */
    type FollowSelect = Array<Follow>;

    /** 拜访详情 */
    type FollowDetail = Follow;

    // *********** 拜访计划管理 ***********
    /** plan */
    type Plan = Common.CommonRecord<{
      visitId: number;
      planComment: string;
      planDate: string;
      planStatus: number;
      cancelReason: string;
      user?: Api.SystemManage.User;
      userId?: number;
      customers?: Array<Api.Crm.Customer>;
      customerIds?: Array<number>;
      project?: Api.Crm.Project;
      projectId?: number;
    }>;

    // 不分页的列表
    type PlanList = Array<Plan>;

    // 下拉
    type PlanSelect = Array<Plan>;

    type PlanSearchParams = CommonType.RecordNullable<
      Pick<Plan, 'planComment' | 'planDate'> & {
        times: Array<string>;
      }
    >;

    // 表单
    type PlanFormParams = CommonType.RecordNullable<Omit<Plan, 'planStatus' | 'cancelReason'>>;

    // 取消计划
    type PlanCancelParams = CommonType.RecordNullable<Pick<Plan, 'id' | 'cancelReason'>>;

    // *********** 评审管理 ***********
    /** review */
    type Review = Common.CommonRecord<{
      visitId: number;
      isValid: number;
      comment: string;
      userId?: number;
      user?: Api.SystemManage.User;
    }>;

    /** 评审拜访 */
    type FollowReviewParams = CommonType.RecordNullable<Review>;
  }
}
