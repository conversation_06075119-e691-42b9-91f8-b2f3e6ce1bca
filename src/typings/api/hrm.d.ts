declare namespace Api {
  namespace Hrm {
    type IdNameLong = {
      id?: number;
      name?: string;
    };
    type IdNameString = {
      id?: string;
      name?: string;
    };

    /** IdNameInteger，员工状态id-name json串 */
    export type IdNameInteger = {
      id: number;
      name: string;
      data: IdNameInteger;
    };
    /** SimpleEmployeeInfo，花名册列表用户信息 */
    type SimpleEmployeeInfo = {
      dept: IdNameLong;
      /** 员工部门id */
      deptId: number;
      /** 入职时间 */
      entryDate: string;
      /** 员工id */
      id: number;
      /** 工号 */
      jobNumber?: string;
      idNumber?: string;
      /** 员工姓名 */
      name: string;
      /** 员工手机号 */
      phoneNumber?: string;
      /* 是否已经在钉钉 */
      inDingTalk: boolean;
      position: IdNameString;
      /** 当前职位id */
      positionId: string;
      status: IdNameInteger;
      /** 员工状态id */
      statusId: number;
      subStatus: IdNameIntegerObject;
      subStatusId: number;
      /** 离职交接钉钉审批详情url */
      resignationHandoverDingTalkUrl?: string;
      /** 离职申请钉钉审批详情url */
      resignationRequestDingTalkUrl?: string;
      positionProperty: string;
    };
    type SimpleEmployeeInfoSearch = CommonType.RecordNullable<
      Pick<
        SimpleEmployeeInfo,
        'name' | 'deptId' | 'positionId' | 'statusId' | 'subStatus' | 'jobNumber' | 'positionProperty'
      > &
        Common.CommonSearchParams & { selectedId: number; status: number }
    >;
    type SimpleEmployeeInfoList = Api.Common.PaginatingQueryRecord<SimpleEmployeeInfo>;

    type IdAndBasicInfoRespVO = {
      idFrontImg: string;
      idBackImg: string;
      /** 身份证反面编号 */
      backIdCardId: number;
      /** 出生日期 */
      birthday: string;
      /** 现居住地 */
      currentResidence?: string;
      /** 电子照片编号 */
      electronicPhotoId: number;
      /** 邮箱 */
      email: string;
      /** 民族 */
      ethnicity: number;
      ethnicityText: string;
      /** 身份证正面编号 */
      frontIdCardId: number;
      /** 性别 */
      gender: number;
      genderText: string;
      /** 特长/爱好 */
      hobbies: string;
      /** 户籍所在地 */
      householdRegistration: string;
      /** 编号，主键自增 */
      id: number;
      /** 身份证号码 */
      idNumber: string;
      /** 婚姻状况 */
      maritalStatus: number;
      /** 婚姻状况 */
      maritalStatusText: string;
      /** 姓名 */
      name: string;
      /** 手机号 */
      phoneNumber: string;
      /** 政治面貌 */
      politicalStatus: number;
      /** 政治面貌 */
      politicalStatusText: string;
      electronicPhotoFile: string;
      entryPosition: IdNameStringObject;
      workClothesSize: string;
      workClothesSizeText: string;

      // 基础信息
      /** 用户头像 */
      avatar?: string;
      /** 部门名称 */
      deptName?: string;
      /** 公司主体 */
      companyEntity?: string;
      // 岗位属性
      positionProperty: string;
      /** 司龄 */
      workDay?: number;
      status?: IdNameInteger;
      subStatus?: IdNameIntegerObject;
      // 入职日期
      entryDate: string;
      position: IdNameStringObject;
    };
    type FormItem = {
      time: string;
      begin: string; // 开始
      end: string; // 结束
      name: string; // 学校
      major: string; // 专业
      status: number; // 学历状态
      educationMode: number;
      educationModeText: string;
      educationStatusText: string;
      educationLevel: number; // 学历
      educationLevelText: string;
      proofDocument: {
        fileId?: number;
        kind?: number;
        name?: null;
        type?: null;
      }[]; // 学历证明
      badgePhoto1: number;
      badgePhoto2: number;
      badgePhoto3: number;
    };
    type Request = {
      /** 专业能力介绍 */
      description: string;
      /** 工作经历 */
      details: WorkExperienceDetailReqVO[];
      /** 工作年限 */
      year: number;
    };
    /** WorkExperienceDetailReqVO，工作经历详情 */
    type WorkExperienceDetailReqVO = {
      time: string;
      /** 起始日期，yyyy-MM-dd */
      begin: string;
      /** 公司名称 */
      companyName: string;
      /** 结束日期，yyyy-MM-dd */
      end: string;
      /** 工作业绩、奖项、表现或者所参与项目的成效 */
      performanceSummary: string;
      /** 担任职位 */
      position: string;
      /** 项目名称 */
      projectName: string;
      /** 项目规模 */
      projectScale: string;
    };

    type UpdateTrainingAndCertificatesReqVO = {
      certificateFile: [];
      /** 证书等级 */
      certificateLevel: number;
      /** 证书等级名称 */
      certificateLevelText: string;
      /** 证书名称 */
      certificateName: string;
      /** 证书类型 */
      certificateType: number;
      /** 证书类型名称 */
      certificateTypeText: string;
      /** 培训起始日期 */
      trainBegin: string;
      /** 培训结束日期 */
      trainEnd: string;
      trainTime: string;
      /** 证书获取时间 */
      certificateTime: string;
      /** 培训名称 */
      trainName: string;
      certificateFileId: number;
    };
    type UpdateLegalComplianceReqVO = {
      /** 预计提供日期 */
      expectedProvideDate: string;
      label: string;
      fileIds: number[];
      files: [];
      /** 是否有文件 */
      hasFile: boolean;
      /** 类型 */
      kind: number;
      /** 未提供原因 */
      missingReason: string;
    };
    type BankCardInfoRespVO = {
      /** 银行卡号 */
      bankAccountNumber: string;
      cardTypeText: string;
      /** 开户网点名称 */
      bankBranchName: string;
      /** 银行卡照片文件id */
      bankCard: number;
      /** 银行名称 */
      bankName: string;
      /** 银行流水信息文件id */
      bankStatementPhotos: number[];
      bankCardFile: [];
      /** 银行卡类型 */
      cardType: string;
      /** 有效期限 */
      expireDate: string;
    };
    type UpdateFamilyMemberDetailRequest = {
      /** 家庭成员信息详情 */
      details: UpdateFamilyMemberDetailReqVO[];
      /** 紧急联系人姓名 */
      emergencyContact: string;
      /** 紧急联系人联系方式 */
      emergencyContactPhone: string;
      /** 亲友姓名，选中有亲友在公司任职时必填 */
      familyOrFriendName: string;
      /** 是否有亲友在公司任职 */
      hasFamilyOrFriendInCompany: boolean;
      /** 亲友任职岗位 */
      familyOrFriendPositionName: string;
    };

    /** UpdateFamilyMemberDetailReqVO，家庭成员信息详情 */
    type UpdateFamilyMemberDetailReqVO = {
      /** 年龄 */
      age: number;
      /** 单位及职位名称 */
      companyAndPositionName: string;
      /** 姓名 */
      name: string;
      /** 联系方式 */
      phoneNumber: string;
      /** 与成员关系 */
      relation: string;
      /** 与成员关系 */
      relationText: string;
    };
    /** CreateOnboardingReqVo，创建新的入职流程链接请求类 */
    type CreateOnboardingReqVo = {
      /** id */
      id: number;
      /** 部门id */
      deptId: number;
      /** 岗位id */
      positionId: string;
      /** 直接上级id */
      user: number;
      notifier: number;
      notifierUser: IdNameLongObject | null;
      positionProperty: string;
    };
    type OnboardingSearchForm = CommonType.RecordNullable<
      CreateOnboardingReqVo & { status: number } & Common.CommonSearchParams
    >;
    /** OnboardingLinkRespVO，数据 */
    type OnboardingLinkRespVO = {
      /** 部门名称 */
      deptName: string;
      /** 入职链接id */
      id: number;
      /** 入职链接 */
      link: string;
      notifier: IdNameLongObject;
      /** 通知人user_id */
      notifierId: number;
      /** 岗位名称 */
      positionName: string;
      /** base64的二维码 */
      qrcode: string;
      /** 直接上级名称 */
      userName: string;
      /** 是否有效 */
      valid: boolean;
      positionProperty: string;
      user: IdNameLongObject;
    };
    type OnboardingLinkRespVOList = Api.Common.PaginatingQueryRecord<
      Common.CommonRecord<OnboardingLinkRespVO & { createTime: string }>
    >;
    type ReviewEmployeeReqVO = {
      /** 错误的步骤，是否通过为false时必填 */
      errorSteps?: number[];
      /** 不通过的原因，是否通过为 false 时必填 */
      reason?: string;
      /** 是否通过 */
      status: boolean;
      isBlack: boolean;
      blackReason: string;
      isInvite: boolean;
    };
    /** BlackListRespVO，黑名单列表页 */
    type BlackListRespVO = {
      /** 对应的员工id，没有的时候为空 */
      employeeId: number;
      /** 黑名单id */
      id?: number;
      /** 姓名 */
      name: string;
      /** 拉黑原因 */
      reason: string;
      idNumber?: string;
      reviewId: number | null;
    };
    type BlackListTable = Api.Common.PaginatingQueryRecord<
      Common.CommonRecord<BlackListRespVO & { updaterName: string }>
    >;
    type BlackListArray = Array<BlackListRespVO>;
    type BlackListSearchParams = CommonType.RecordNullable<
      Common.CommonSearchParams & {
        name: string;
        idNumber: string;
      }
    >;
    type BlackListSearchForm = CommonType.RecordNullable<{
      name: string;
      idNumber: string;
    }>;
    type ProbationRespVOList = Api.Common.PaginatingQueryRecord<ProbationRespVO>;
    /** ProbationRespVO，试用期信息 */
    type ProbationRespVO = {
      dept?: IdNameLongObject;
      /** 部门id */
      deptId?: string;
      /** 入职日期 */
      entryDate?: string;
      /** 员工id */
      id: number;
      /** 身份证 */
      idNumber?: string;
      /** 员工姓名 */
      name: string;
      /** 计划转正日期 */
      planRegularDate?: string;
      position?: IdNameStringObject;
      /** 岗位id */
      positionId?: string;
      /** 实际转正日期 */
      regularDate?: string;
      subStatus?: IdNameIntegerObject;
      /** 员工子状态 */
      subStatusId?: number;
      /** 试用期 */
      trialPeriod?: string;
      /* 钉钉审批链接 */
      dingTalkUrl: string;
    };

    /** IdNameLongObject，员工部门id-name json串 */
    type IdNameLongObject = {
      data: { [key: string]: any };
      id: number;
      name: string;
    };

    /** IdNameStringObject */
    type IdNameStringObject = {
      data?: { [key: string]: any };
      id?: string;
      name?: string;
    };

    /** IdNameIntegerObject */
    type IdNameIntegerObject = {
      data?: { [key: string]: any };
      id?: number;
      name?: string;
    };
    type ResignedRespVO = {
      dept?: IdNameLongObject;
      /** 离职时部门id */
      deptId?: string;
      /** 入职日期 */
      entryDate?: string;
      /** 员工id */
      id: number;
      /** 员工姓名 */
      name: string;
      position: IdNameStringObject;
      /** 离职时岗位id */
      positionId?: string;
      /** 离职日期 */
      resignedDate?: string;
      /** 离职原因 */
      resignedReason?: string;
      subStatus: IdNameIntegerObject;
      /** 员工子状态 */
      subStatusId?: number;
    };
    type ResignedRespVOList = Api.Common.PaginatingQueryRecord<ResignedRespVO>;
    type PromotionRespVO = {
      /** 晋升后岗位名称 */
      afterPosition?: string;
      /** 晋升前岗位名称 */
      beforePosition?: string;
      /** 入职日期 */
      entryDate?: string;
      /** 员工id */
      id: number;
      /** 员工姓名 */
      name: string;
      /** 晋升日期 */
      promotionDate?: string;
      /** 晋升时部门名称 */
      promotionDept?: string;
      /** 钉钉审批链接 */
      dingTalkUrl: string;
    };
    type PromotionRespVOList = Api.Common.PaginatingQueryRecord<PromotionRespVO>;
    type TransferPostRespVO = {
      /** 调岗后部门名称 */
      afterDept?: string;
      /** 调岗后职位名称 */
      afterPosition?: string;
      /** 调岗前部门名称 */
      beforeDept?: string;
      /** 调岗前职位名称 */
      beforePosition?: string;
      /** 入职日期 */
      entryDate?: string;
      /** 员工id */
      id: number;
      /** 员工姓名 */
      name: string;
      /** 调岗日期 */
      transferPostDate?: string;
      /** 钉钉审批链接 */
      dingTalkUrl: string;
      subStatusId: number;
      subStatus: IdNameIntegerObject;
    };
    type TransferPostRespVOList = Api.Common.PaginatingQueryRecord<TransferPostRespVO>;
    type EmployeeStatusRespVO = {
      /** 错误步骤，入职流程为已拒绝时第一个错误的步骤 */
      errorStep?: number;
      /** 当前入职流程事件子状态 */
      eventTypeSubStatus: number;
      /** 员工id */
      id: number;
      /** 上次入职流程填写步骤 */
      lastStep: number;
      /** 员工姓名 */
      name: string;
      /** 被拒绝原因，入职流程状态为已拒绝时提供 */
      rejectionReason?: string;
      reviewTime?: string;
      reviewer?: IdNameIntegerObject;
      /** 子状态id */
      subStatusId: number;
      // 审核状态
      reviewStatus: string;
    };
    type CompanyListRespVO = {
      /** 分公司地址 */
      address: string;
      /** 联系方式 */
      contactWay: string;
      /** 分公司id */
      id?: number;
      /** 法人姓名 */
      legalPersonName: string;
      /** 分公司名称 */
      name: string;
    };
    type CompanyListRespVOList = Api.Common.PaginatingQueryRecord<Common.CommonRecord<CompanyListRespVO>>;
    type CompanyListRespParams = CommonType.RecordNullable<
      Pick<Api.Hrm.CompanyListRespVO, 'contactWay' | 'legalPersonName' | 'name'> & Common.CommonSearchParams
    >;
    // 录入条件确认
    type PromotionConditionsListOrDetailRespVO = {
      company: IdNameLongObject;
      /** 分公司id */
      companyId: number;
      /** 员工姓名 */
      employeeName: string;
      /** id */
      id?: number;
      /** 身份证号码 */
      idNumber: string;
      position: IdNameStringObject;
      /** 岗位id */
      positionId: string;
    };
    type PromotionConditionsListOrDetailRespVOList =
      Api.Common.PaginatingQueryRecord<PromotionConditionsListOrDetailRespVO>;

    type PromotionConditionsListOrDetailRespVOParams = CommonType.RecordNullable<
      Pick<Api.Hrm.PromotionConditionsListOrDetailRespVO, 'companyId' | 'idNumber' | 'positionId'> &
        Common.CommonSearchParams & {
          name: string;
        }
    >;
    type UpdateOrCreatePromotionConditionsReqVO = {
      /** 分公司id */
      companyId: number | null;
      /** 员工姓名 */
      employeeName: string;
      /** 身份证号码 */
      idNumber: string;
      id?: number;
      /** 岗位id */
      positionId: string | null;
      /** 至少提供一条试用期绩效考核项目 */
      probationPerformanceCriteria: ProbationPerformanceCriteriaReqVO[];
      /** 岗位职责 */
      responsibilities: string[];
      company?: IdNameLongObject;
      position?: IdNameStringObject;
      ratingRules: string;
    };

    /** ProbationPerformanceCriteriaReqVO，试用期绩效考核目标 */
    export type ProbationPerformanceCriteriaReqVO = {
      /** 考核项目 */
      name: string;
      /** 第一个月 */
      first: string;
      /** 第二个月 */
      second: string;
      /** 第三个月 */
      third: string;
      /* 评分规则 */
      ratingRules?: string;
      /** 权重 */
      weight: number;
    };
    type QiYueSuoTemplateRespVO = {
      /** 业务分类id */
      categoryId: number;
      /** 模板id */
      id: number;
      /** 模板名称 */
      name: string;
      /** 模板参数 */
      params: QiYueSuoTemplateParamsRespVO[];
    };

    /** QiYueSuoTemplateParamsRespVO，契约锁模板参数 */
    export type QiYueSuoTemplateParamsRespVO = {
      description?: string;
      expression?: QiYueSuoTemplateParamsExpressionRespVO;
      /** 名称 */
      name: string;
      /** 可选项，类型是单选或多选时返回 */
      options?: string[];
      optionsConfig?: string[];
      /** 是否必填 */
      required: boolean;
      /** 类型 */
      type: string;
      value?: string | number;
    };

    /** QiYueSuoTemplateParamsExpressionRespVO，取值表达式 */
    export type QiYueSuoTemplateParamsExpressionRespVO = {
      /** api */
      api: string;
      /** dictType */
      dictType?: string;
      /** disabled */
      disabled?: boolean;
      /** key */
      key: string;
      /** type */
      type?: string;
    };
    /** CreateContractDraftReqVO，创建合同草稿参数 */
    export type CreateContractDraftReqVO = {
      /** 业务分类id */
      categoryId: number;
      /** 公司名称 */
      companyName: string;
      /** 员工id */
      employeeId: number;
      /** 员工类型 */
      employeeType: number;
      /** 岗位属性 */
      positionProperty: number;
      /** 试用期类型 */
      probationPeriodType: number;
      /** 参数列表 */
      templateParams?: CreateContractTemplateParamReqVO[];
    };

    /** CreateContractTemplateParamReqVO，创建合同草稿时需要传递合同内的参数 */
    export type CreateContractTemplateParamReqVO = {
      /** 参数名称 */
      name: string;
      /** 可选项，类型是单选或多选时返回 */
      options?: string[];
      /** 是否必填 */
      required: boolean;
      /** 参数类型 */
      type?: string;
      /** 参数值 */
      value: string;
    };
    /** ContractListRespVo，合同列表 */
    type ContractListRespVo = {
      /** 业务分类id */
      categoryId: number;
      /** 业务分类名称 */
      categoryName: string;
      /** 公司名称 */
      companyName: string;
      createTime: string;
      /** 合同id */
      contractId: number;
      contractStatus: IdNameStringObject;
      /** 合同状态代码 */
      contractStatusCode: string;
      /** 员工部门 */
      employeeDept: string;
      /** 员工id */
      employeeId: number;
      /** 员工姓名 */
      employeeName: string;
      /** 员工岗位 */
      employeePosition: string;
      employeeStatus: IdNameIntegerObject;
      /** 员工当前主状态id */
      employeeStatusId: number;
      /** 发起合同时员工主状态id */
      employeeStatusIdOnSend: number;
      employeeStatusOnSend: IdNameIntegerObject;
      employeeSubStatus: IdNameIntegerObject;
      /** 员工当前子状态id */
      employeeSubStatusId: number;
      /** 发起合同时员工子状态id */
      employeeSubStatusIdOnSend: number;
      employeeSubStatusOnSend: IdNameIntegerObject;
      /** 合同包含的文件列表，已签署或已作废的合同有值 */
      fileId?: number[];
      /** 主键 */
      id: number;
    };
    type ContractListRespVoList = Api.Common.PaginatingQueryRecord<ContractListRespVo>;
    /** FeeInfoRespVo，费用信息 */
    export type FeeInfoRespVo = {
      /** 数据更新时间 */
      lastUpdate: string;
      /** 数据总量 */
      total: number;
      /** 剩余数量 */
      remaining: number;
    };
    /** InviteToDingTalkReqVO，邀请进入钉钉请求 */
    type InviteToDingTalkReqVO = {
      /** 公司id */
      companyId: number;
      /** 员工类型 */
      employeeType: string;
      /** 入职日期 */
      entryDate: string;
      /** 计划转正日期 */
      planRegularTime: string;
      /** 试用期类型 */
      probationPeriodType: string;
      /** 开班日期 */
      reminderDateForClassOpening: string;
      /** 审核id */
      reviewId: number | null;
    };
    type InviteToDingTalkReqVOData = CommonType.RecordNullable<InviteToDingTalkReqVO>;

    /** PreEntryRespVO，待入职对象 */
    type PreEntryRespVO = {
      /** 合同id */
      contractId: number;
      contractStatus: IdNameStringObject;
      /** 合同状态 */
      contractStatusId?: string;
      dept?: IdNameLongObject;
      /** 部门id */
      deptId?: number;
      /** 入职日期 */
      entryDate?: string;
      /** 员工id */
      id: number;
      /** 身份证 */
      idNumber: string;
      /** 是否已经在钉钉 */
      inDingTalk: boolean;
      /** 员工姓名 */
      name: string;
      position: IdNameStringObject;
      /** 岗位id */
      positionId: string;
      subStatus: IdNameIntegerObject;
      /** 员工子状态id */
      subStatusId: number;
    };
    type PreEntryRespVOList = Api.Common.PaginatingQueryRecord<Common.CommonRecord<PreEntryRespVO>>;
    /** UpdateStatusReqVO，更新员工状态 */
    type UpdateStatusReqVO = {
      /** 是否拉黑，仅当新状态为离职时可提供，其他状态忽略 */
      block?: boolean;
      /** 拉黑原因，当新状态为离职，block为true时必填 */
      blockReason?: string;
      /** 公司id */
      companyId: number;
      /** 员工类型 */
      employeeType?: number;
      /** 入职日期 */
      entryDate?: string;
      /** 拉黑时可以提供，如果已有身份证号，会忽略 */
      idNumber?: string;
      /** 离职日期 */
      leaveDate?: string;
      /** 新状态 */
      newStatus: number;
      /** 计划转正日期 */
      planRegularTime?: string;
      /** 岗位属性 */
      positionProperty?: string;
      /** 试用期类型 */
      probationPeriodType?: number;
      /** 实际转正日期 */
      regularizeDate?: string;
      /** 离职原因 */
      resignedRemark?: string;
      /** 离职原因 */
      resignedReason?: string[];
      /** 离职原因备注 */
      leaveReasonRemark?: string;
    };
    type UpdateStatusReqVOData = CommonType.RecordNullable<UpdateStatusReqVO>;
    type UpdateBlackListReqVO = CommonType.RecordNullable<
      Pick<
        UpdateStatusReqVO,
        'block' | 'blockReason' | 'idNumber' | 'leaveDate' | 'newStatus' | 'resignedRemark' | 'resignedReason'
      >
    >;
    /** EmployeeReviewRespVO，审核信息接口 */
    export type EmployeeReviewRespVO = {
      /** 错误步骤 */
      errorStep?: number;
      /** 不通过原因 */
      rejectionReason?: string;
      reviewer?: IdNameLongObject;
      /** 审核人id */
      reviewerId?: number;
      /** 审核状态 */
      reviewStatus: string;
      /** 审核事件 */
      reviewTime?: string;
    };
    // 离职
    type ResignedReqVO = {
      /** 离职申请状态 */
      applyStatus?: number;
      /** 离职交接状态 */
      handoverStatus?: number;
      /** 工号，精确匹配 */
      jobNumber?: string;
      /** 姓名，模糊查询 */
      name?: string;
      /** 岗位名 */
      positionName?: string;
    };

    type ResignedReqVOData = Common.CommonSearchParams &
      CommonType.RecordNullable<
        ResignedReqVO & {
          subStatus: number;
        }
      >;

    /** ResignationRespVO，离职中信息 */
    type ResignationRespVO = {
      /** 离职申请描述 */
      applyDescription?: string;
      /** 离职申请钉钉审批详情url */
      applyExternalUrl?: string;
      /** 离职申请状态 */
      applyStatus?: number;
      /** 离职时部门 */
      deptList?: DeptDTO[];
      /** 入职日期 */
      entryDate?: string;
      /** 离职交接描述 */
      handoverDescription?: string;
      /** 离职交接钉钉审批详情url */
      handoverExternalUrl?: string;
      /** 离职交接状态 */
      handoverStatus?: number;
      /** 员工id */
      id: number;
      /** 员工姓名 */
      name: string;
      /** 离职时岗位 */
      positionName?: string;
      /** 岗位属性 */
      positionProperty?: number;
      /** 离职原因备注 */
      reasonMemo?: string;
      /** 离职日期 */
      resignationDate?: string;
      /** 离职原因 */
      resignationReason?: string;
      // 工号
      jobNumber?: string;
    };

    /** DeptDTO，调岗/晋升后部门 */
    type DeptDTO = {
      deptId?: number;
      deptName?: string;
    };
    type ResignedReqVOList = Api.Common.PaginatingQueryRecord<ResignationRespVO>;
    // 晋升
    type PromotionReqVO = {
      /** 调岗/晋升生效时间 */
      effectiveDate?: string[];
      /** 调整前岗位名 */
      fromPositionName?: string;
      /** 姓名，模糊查询 */
      name?: string;
      /** 调岗/晋升状态 */
      status?: number;
      /** 调整后岗位名 */
      toPositionName?: string;
      /** 调岗/晋升类型 */
      type: number;
      // 审批状态
      statuses?: number[];
      positionProperty?: string;
    };
    type PromotionReqVOData = Common.CommonSearchParams & CommonType.RecordNullable<PromotionReqVO>;

    /** PositionChangeRespVO，调岗/晋升信息 */
    export type PositionChangeRespVO = {
      /** 调岗/晋升描述 */
      description?: string;
      /** 调岗日期 */
      effectiveDate?: string;
      /** 第三方审批详情url */
      externalUrl?: string;
      /** 调岗/晋升前部门 */
      fromDepts?: DeptDTO[];
      /** 变动前职位名称 */
      fromPosition?: string;
      /** 员工id */
      id: number;
      /** 发起日期 */
      issueDate?: string;
      /** 员工姓名 */
      name: string;
      /** 钉钉审批状态 */
      status?: number;
      /** 调岗/晋升后部门 */
      toDepts?: DeptDTO[];
      /** 变动后职位名称 */
      toPosition?: string;
      /** 员工工号 */
      employeeId?: number;
      /** 岗位属性 */
      positionProperty?: string;
      /** 创建时间 */
      createTime?: string;
    };
    type PositionChangeReqVOData = Api.Common.PaginatingQueryRecord<PositionChangeRespVO>;
    type ResignedHistoryReqVO = {
      /** 离职时间 */
      leaveDate?: string[];
      /** 姓名，模糊查询 */
      name?: string;
      /** 岗位属性 */
      positionProperty?: string;
    };
    type ResignedHistoryReqVOData = Common.CommonSearchParams & CommonType.RecordNullable<ResignedHistoryReqVO>;
    /** ResignationHistoryRespVO，已离职信息 */
    type ResignationHistoryRespVO = {
      /** 离职时部门 */
      deptList?: DeptDTO[];
      /** 员工id */
      id: number;
      /** 离职日期 */
      leaveDate?: string;
      /** 主要部门 */
      mainDeptId?: number;
      /** 主要部门名称 */
      mainDeptName?: string;
      /** 员工姓名 */
      name: string;
      /** 离职原因备注 */
      reasonMemo?: string;
      /** 离职原因 */
      resignationReason?: string;
      /** 是否主动离职 */
      voluntaryResignated?: boolean;
    };
    type ResignedHistoryReqVOList = Api.Common.PaginatingQueryRecord<ResignationHistoryRespVO>;
    /** EmployeeEventTimelineRespVO，员工事件时间线响应 */
    type EmployeeEventTimelineRespVO = {
      /** 事件描述 */
      description: string;
      /** 事件id */
      id: number;
      /** 操作时间 */
      operationDate?: string;
      /** 事件标题 */
      title: string;
      /** 详情链接 */
      url?: string;
    };
    type EmployeeEventTimelineRespVOList = Api.Common.PaginatingQueryRecord<EmployeeEventTimelineRespVO>;

    /** EmployeeMappingRespVO，契约锁员工映射 */
    type EmployeeMappingRespVO = {
      switchLoading?: boolean;
      /** 是否全局推送人 */
      global?: boolean;
      localEmployee: SimpleEmployeeInfos;
      /** id */
      mappingId: number;
      qiyuesuoEmployee: QiYueSuoEmployeeRespVO | null;
    };
    type EmployeeMappingRespVOList = Array<EmployeeMappingRespVO>;
    /** SimpleEmployeeInfo，花名册列表用户信息 */
    type SimpleEmployeeInfos = {
      dept: IdNameLongObject;
      /** 员工部门id */
      deptId: number;
      /** 入职时间 */
      entryDate: string;
      /** 员工id */
      id: number;
      /** 工号 */
      jobNumber?: string;
      /** 员工姓名 */
      name: string;
      /** 员工手机号 */
      phoneNumber?: string;
      position: IdNameStringObject;
      /** 当前职位id */
      positionId: string;
      status: IdNameIntegerIdNameIntegerObject;
      /** 员工状态id */
      statusId: number;
    };

    /** IdNameIntegerIdNameIntegerObject，员工状态id-name json串 */
    type IdNameIntegerIdNameIntegerObject = {
      data?: IdNameIntegerObject;
      id?: number;
      name?: string;
    };

    /** QiYueSuoEmployeeRespVO，契约锁员工信息 */
    type QiYueSuoEmployeeRespVO = {
      /** 契约锁联系方式 */
      contact: string;
      /** 契约锁联系方式类型 */
      contactType: string;
      /** 契约锁id */
      id: number;
      /** 契约锁用户名称 */
      name: string;
    };
    /** IdNameLongQiYueSuoEmployeeRespVO */
    type IdNameLongQiYueSuoEmployeeRespVO = {
      data: QiYueSuoEmployeeRespVO;
      id: number;
      name: string;
    };
    type PostEmployeeMappingReqVO = {
      /** 联系方式 */
      contact: string;
      /** 联系方式类型 */
      contactType: string;
      /** 员工id */
      employeeId: number;
      /** 是否全局推送 */
      global?: boolean;
      /** 本地员工姓名 */
      localName: string;
      /** 契约锁员工id */
      qiyuesuoId: number | null;
      /** 契约锁员工姓名 */
      qiyuesuoName: string;
    };

    /** SimpleContractRespVO，简易合同信息 */
    export type SimpleContractRespVO = {
      /** 业务分类名称 */
      categoryName: string;
      /** 公司主体名称 */
      companyName: string;
      /** 契约锁合同id */
      contractId: number;
      /** 创建时间 */
      createTime: string;
      /** 主键 */
      id: number;
      status: IdNameStringObject;
      /** 合同状态代码 */
      statusCode: string;
    };
  }
}
