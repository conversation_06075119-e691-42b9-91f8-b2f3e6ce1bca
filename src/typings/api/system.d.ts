declare namespace Api {
  namespace System {
    type ChangePasswordParams = {
      oldPassword: string;
      newPassword: string;
      confirmPassword?: string;
      captcha: string[];
    };
    type OptionItem = {
      key: string;
      value: string | number;
    };
    // 字典返回结果结构
    type OptionObject = Record<string, OptionItem[]>;

    // 转换后用于组件使用的格式
    type DictOption = Array<{
      label: string;
      value: number;
    }>;

    type FormattedOption = {
      label: string;
      value: string | number;
    };
    /** UserSimpleRespVO，管理后台 - 用户精简信息 Response VO */
    type UserSimpleRespVO = {
      /** 部门ID */
      deptId?: number;
      /** 部门名称 */
      deptName?: string;
      /** 外部系统用户ID */
      externalUserId: number;
      /** 用户编号 */
      id: number;
      /** 用户昵称 */
      nickname: string;
    };
    /** data scope */
    type DataScope = '1' | '2' | '3' | '4' | '5' | '6';

    /** role */
    type Role = Common.CommonRecord<{
      /** 数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限） */
      dataScope: DataScope;
      /** 部门树选择项是否关联显示 */
      deptCheckStrictly: boolean;
      /** 用户是否存在此角色标识 默认不存在 */
      flag: boolean;
      /** 菜单树选择项是否关联显示 */
      menuCheckStrictly: boolean;
      /** 备注 */
      remark?: string;
      /** 角色ID */
      roleId: CommonType.IdType;
      /** 角色权限字符串 */
      roleKey: string;
      /** 角色名称 */
      roleName: string;
      /** 显示顺序 */
      roleSort: number;
      /** 角色状态（0正常 1停用） */
      status: string;
      /** 是否管理员 */
      superAdmin: boolean;
    }>;

    /** role search params */
    type RoleSearchParams = CommonType.RecordNullable<
      Pick<Api.System.Role, 'roleName' | 'roleKey' | 'status'> & Api.Common.CommonSearchParams
    >;

    /** role operate params */
    type RoleOperateParams = CommonType.RecordNullable<
      Pick<
        Api.System.Role,
        | 'roleId'
        | 'roleName'
        | 'roleKey'
        | 'roleSort'
        | 'menuCheckStrictly'
        | 'deptCheckStrictly'
        | 'dataScope'
        | 'status'
        | 'remark'
      > & { menuIds: CommonType.IdType[]; deptIds: CommonType.IdType[] }
    >;

    /** role list */
    type RoleList = Common.PaginatingQueryRecord<Role>;

    /** role menu tree select */
    type RoleMenuTreeSelect = Common.CommonRecord<{
      checkedKeys: CommonType.IdType[];
      menus: MenuList;
    }>;
    /** teannt-package menu tree select */
    type TenantPackageMenuTreeSelect = Common.CommonRecord<{
      checkedKeys: CommonType.IdType[];
      menus: MenuList;
    }>;
    /** role dept tree select */
    type RoleDeptTreeSelect = Common.CommonRecord<{
      checkedKeys: CommonType.IdType[];
      depts: Dept[];
    }>;

    /** all role */
    type AllRole = Pick<Role, 'roleId' | 'roleName' | 'roleKey'>;

    /**
     * user gender
     *
     * - "1": "male"
     * - "2": "female"
     */
    type UserGender = '1' | '2';

    /** user */
    type User = Common.CommonTenantRecord<{
      /** 用户ID */
      userId: CommonType.IdType;
      /** 部门ID */
      deptId: CommonType.IdType;
      /** 部门名称 */
      deptName: string;
      /** 用户账号 */
      userName: string;
      /** 用户昵称 */
      nickName: string;
      /** 用户类型（sys_user系统用户） */
      userType: string;
      /** 用户邮箱 */
      email: string;
      /** 手机号码 */
      phonenumber: string;
      /** 用户性别（0男 1女 2未知） */
      sex: string;
      /** 头像地址 */
      avatar: string;
      /** 密码 */
      password: string;
      /** 帐号状态（0正常 1停用） */
      status: string;
      /** 最后登录IP */
      loginIp: string;
      /** 最后登录时间 */
      loginDate: Date;
      /** 备注 */
      remark?: string;
    }>;

    /** user search params */
    type UserSearchParams = CommonType.RecordNullable<
      Pick<User, 'deptId' | 'userName' | 'nickName' | 'phonenumber' | 'status'> & {
        roleId: CommonType.IdType;
      } & Common.CommonSearchParams
    >;

    /** user operate params */
    type UserOperateParams = CommonType.RecordNullable<
      Pick<
        User,
        | 'userId'
        | 'deptId'
        | 'userName'
        | 'nickName'
        | 'email'
        | 'phonenumber'
        | 'sex'
        | 'password'
        | 'status'
        | 'remark'
      > & { roleIds: CommonType.IdType[]; postIds: CommonType.IdType[] }
    >;

    /** user profile operate params */
    type UserProfileOperateParams = CommonType.RecordNullable<Pick<User, 'nickName' | 'email' | 'phonenumber' | 'sex'>>;

    /** user password operate params */
    type UserPasswordOperateParams = CommonType.RecordNullable<
      Pick<User, 'userId' | 'password'> & { newPassword: string }
    >;

    /** user info */
    type UserInfo = {
      /** user post ids */
      postIds: string[];
      /** user role ids */
      roleIds: string[];
    };

    /** user list */
    type UserList = Common.PaginatingQueryRecord<User>;

    /** auth role */
    type AuthRole = {
      user: User;
      roles: Role[];
    };

    /** social */
    type Social = Common.CommonRecord<{
      /** 主键 */
      id: CommonType.IdType;
      /** 用户ID */
      userId: CommonType.IdType;
      /** 租户ID */
      tenantId: CommonType.IdType;
      /** 认证的唯一ID */
      authId: string;
      /** 用户来源 */
      source: string;
      /** 用户的授权令牌 */
      accessToken: string;
      /** 用户的授权令牌的有效期，部分平台可能没有 */
      expireIn: number;
      /** 刷新令牌，部分平台可能没有 */
      refreshToken: string;
      /** 用户的 open id */
      openId: string;
      /** 授权的第三方账号 */
      userName: string;
      /** 授权的第三方昵称 */
      nickName: string;
      /** 授权的第三方邮箱 */
      email: string;
      /** 授权的第三方头像地址 */
      avatar: string;
      /** 平台的授权信息，部分平台可能没有 */
      accessCode: string;
      /** 用户的 unionid */
      unionId: string;
      /** 授予的权限，部分平台可能没有 */
      scope: string;
      /** 个别平台的授权信息，部分平台可能没有 */
      tokenType: string;
      /** id token，部分平台可能没有 */
      idToken: string;
      /** 小米平台用户的附带属性，部分平台可能没有 */
      macAlgorithm: string;
      /** 小米平台用户的附带属性，部分平台可能没有 */
      macKey: string;
      /** 用户的授权code，部分平台可能没有 */
      code: string;
      /** Twitter平台用户的附带属性，部分平台可能没有 */
      oauthToken: string;
      /** Twitter平台用户的附带属性，部分平台可能没有 */
      oauthTokenSecret: string;
    }>;

    /**
     * icon type
     *
     * - "1": iconify icon
     * - "2": local icon
     */
    type IconType = '1' | '2';

    /**
     * menu type
     *
     * - "M": "目录"
     * - "C": "菜单"
     * - "F": "按钮"
     */
    type MenuType = 'M' | 'C' | 'F';

    /**
     * 是否外链
     *
     * - "0": "是"
     * - "1": "否"
     * - "2": "iframe"
     */
    type IsMenuFrame = '0' | '1' | '2';

    type Menu = Common.CommonRecord<{
      /** 菜单 ID */
      menuId: CommonType.IdType;
      /** 父菜单 ID */
      parentId: CommonType.IdType;
      /** 菜单名称 */
      menuName: string;
      /** 显示顺序 */
      orderNum: number;
      /** 路由地址 */
      path: string;
      /** 组件路径 */
      component: string;
      /** 路由参数 */
      queryParam: string;
      /** 是否为外链（0是 1否 2iframe） */
      isFrame: IsMenuFrame;
      /** 是否缓存（0缓存 1不缓存） */
      isCache: Common.EnableStatus;
      /** 菜单类型（M目录 C菜单 F按钮） */
      menuType: MenuType;
      /** 显示状态（0显示 1隐藏） */
      visible: Common.VisibleStatus;
      /** 菜单状态（0正常 1停用） */
      status: Common.EnableStatus;
      /** 权限标识 */
      perms: string;
      /** 菜单图标 */
      icon: string;
      /** 备注 */
      remark?: string;
      /** 父菜单名称 */
      parentName: string;
      /** 子菜单 */
      children: MenuList;
      id?: CommonType.IdType;
      label?: string;
    }>;

    /** menu list */
    type MenuList = Menu[];

    /** menu search params */
    type MenuSearchParams = CommonType.RecordNullable<Pick<Menu, 'menuName' | 'status' | 'menuType' | 'parentId'>>;

    /** menu operate params */
    type MenuOperateParams = CommonType.RecordNullable<
      Pick<
        Menu,
        | 'menuId'
        | 'menuName'
        | 'parentId'
        | 'orderNum'
        | 'path'
        | 'component'
        | 'queryParam'
        | 'isFrame'
        | 'isCache'
        | 'menuType'
        | 'visible'
        | 'status'
        | 'perms'
        | 'icon'
        | 'remark'
      >
    >;

    /** 字典类型 */
    type DictType = Common.CommonRecord<{
      /** 字典主键 */
      dictId: CommonType.IdType;
      /** 字典名称 */
      dictName: string;
      /** 字典类型 */
      dictType: string;
      /** 备注 */
      remark: string;
    }>;

    /** dict type search params */
    type DictTypeSearchParams = CommonType.RecordNullable<
      Pick<Api.System.DictType, 'dictName' | 'dictType'> & Api.Common.CommonSearchParams
    >;

    /** dict type operate params */
    type DictTypeOperateParams = CommonType.RecordNullable<
      Pick<Api.System.DictType, 'dictId' | 'dictName' | 'dictType' | 'remark'>
    >;

    /** dict type list */
    type DictTypeList = Api.Common.PaginatingQueryRecord<DictType>;

    /** 字典数据 */
    type DictData = Common.CommonRecord<{
      /** 样式属性（其他样式扩展） */
      cssClass: string;
      /** 字典编码 */
      dictCode: CommonType.IdType;
      /** 字典标签 */
      dictLabel: string;
      /** 字典排序 */
      dictSort: number;
      /** 字典类型 */
      dictType: string;
      /** 字典键值 */
      dictValue: string;
      /** 是否默认（Y是 N否） */
      isDefault: string;
      /** 表格回显样式 */
      listClass: NaiveUI.ThemeColor;
      /** 备注 */
      remark: string;
    }>;

    /** dict data search params */
    type DictDataSearchParams = CommonType.RecordNullable<
      Pick<Api.System.DictData, 'dictLabel' | 'dictType'> & Api.Common.CommonSearchParams
    >;

    /** dict data operate params */
    type DictDataOperateParams = CommonType.RecordNullable<
      Pick<
        Api.System.DictData,
        'dictCode' | 'dictSort' | 'dictLabel' | 'dictValue' | 'dictType' | 'cssClass' | 'listClass' | 'remark'
      >
    >;

    /** dict data list */
    type DictDataList = Api.Common.PaginatingQueryRecord<DictData>;

    /** dept */
    type Dept = Api.Common.CommonRecord<{
      /** 部门id */
      id: CommonType.IdType;
      /** 父部门id */
      parentId: CommonType.IdType;
      /** 部门名称 */
      name: string;
      /** 角色 */
      deptDescription: string;
      /** 部门状态（0正常 1停用） */
      status: string;
      maxJobNumber: string | null;
      initialJobNumber: string | null;
    }>;

    /** dept search params */
    type DeptSearchParams = CommonType.RecordNullable<
      Pick<Api.System.Dept, 'name' | 'status'> & Api.Common.CommonSearchParams
    >;

    /** dept operate params */
    type DeptOperateParams = CommonType.RecordNullable<
      Pick<
        Api.System.Dept,
        'id' | 'parentId' | 'name' | 'deptDescription' | 'status' | 'maxJobNumber' | 'initialJobNumber'
      >
    >;

    /** dept list */
    type DeptList = Api.Common.PaginatingQueryRecord<Dept>;

    /** post */
    type Post = Common.CommonRecord<{
      /** 岗位ID */
      postId: CommonType.IdType;
      /** 租户编号 */
      tenantId: CommonType.IdType;
      /** 部门id */
      deptId: CommonType.IdType;
      /** 岗位编码 */
      postCode: string;
      /** 类别编码 */
      postCategory: string;
      /** 岗位名称 */
      postName: string;
      /** 显示顺序 */
      postSort: number;
      /** 状态（0正常 1停用） */
      status: string;
      /** 备注 */
      remark: string;
    }>;

    /** post search params */
    type PostSearchParams = CommonType.RecordNullable<
      Pick<Api.System.Post, 'deptId' | 'postCode' | 'postName' | 'status'> & {
        belongDeptId: CommonType.IdType;
      } & Api.Common.CommonSearchParams
    >;

    /** post operate params */
    type PostOperateParams = CommonType.RecordNullable<
      Pick<
        Api.System.Post,
        'postId' | 'deptId' | 'postCode' | 'postCategory' | 'postName' | 'postSort' | 'status' | 'remark'
      >
    >;

    /** post list */
    type PostList = Api.Common.PaginatingQueryRecord<Post>;

    /** config */
    type Config = Common.CommonRecord<{
      /** 参数主键 */
      configId: CommonType.IdType;
      /** 租户编号 */
      tenantId: CommonType.IdType;
      /** 参数名称 */
      configName: string;
      /** 参数键名 */
      configKey: string;
      /** 参数键值 */
      configValue: string;
      /** 是否内置 */
      configType: string;
      /** 备注 */
      remark: string;
    }>;

    /** config search params */
    type ConfigSearchParams = CommonType.RecordNullable<
      Pick<Api.System.Config, 'configName' | 'configKey' | 'configType' | 'createTime'> & Api.Common.CommonSearchParams
    >;

    /** config operate params */
    type ConfigOperateParams = CommonType.RecordNullable<
      Pick<Api.System.Config, 'configId' | 'configName' | 'configKey' | 'configValue' | 'configType' | 'remark'>
    >;

    /** config list */
    type ConfigList = Api.Common.PaginatingQueryRecord<Config>;

    /** tenant */
    type Tenant = Common.CommonRecord<{
      /** id */
      id: CommonType.IdType;
      /** 租户编号 */
      tenantId: CommonType.IdType;
      /** 联系人 */
      contactUserName: string;
      /** 联系电话 */
      contactPhone: string;
      /** 企业名称 */
      companyName: string;
      /** 统一社会信用代码 */
      licenseNumber: string;
      /** 地址 */
      address: string;
      /** 企业简介 */
      intro: string;
      /** 域名 */
      domain: string;
      /** 备注 */
      remark: string;
      /** 租户套餐编号 */
      packageId: CommonType.IdType;
      /** 过期时间 */
      expireTime: string;
      /** 用户数量（-1不限制） */
      accountCount: number;
      /** 租户状态（0正常 1停用） */
      status: string;
      /** 删除标志（0代表存在 1代表删除） */
      delFlag: string;
    }>;

    /** tenant search params */
    type TenantSearchParams = CommonType.RecordNullable<
      Pick<Api.System.Tenant, 'tenantId' | 'contactUserName' | 'contactPhone' | 'companyName'> &
        Api.Common.CommonSearchParams
    >;

    /** tenant operate params */
    type TenantOperateParams = CommonType.RecordNullable<
      Pick<
        Api.System.Tenant,
        | 'id'
        | 'tenantId'
        | 'contactUserName'
        | 'contactPhone'
        | 'companyName'
        | 'licenseNumber'
        | 'address'
        | 'intro'
        | 'domain'
        | 'remark'
        | 'packageId'
        | 'expireTime'
        | 'accountCount'
        | 'status'
      > & {
        username: string;
        password: string;
      }
    >;

    /** tenant package sync params */
    type TenantPackageSyncParams = CommonType.RecordNullable<Pick<Api.System.Tenant, 'tenantId' | 'packageId'>>;

    /** tenant list */
    type TenantList = Api.Common.PaginatingQueryRecord<Tenant>;

    /** tenant package */
    type TenantPackage = Common.CommonRecord<{
      /** 租户套餐id */
      packageId: CommonType.IdType;
      /** 套餐名称 */
      packageName: string;
      /** 关联菜单id */
      menuIds: CommonType.IdType[];
      /** 备注 */
      remark: string;
      /** 菜单树选择项是否关联显示 */
      menuCheckStrictly: boolean;
      /** 状态（0正常 1停用） */
      status: string;
      /** 删除标志（0代表存在 1代表删除） */
      delFlag: string;
    }>;

    /** tenant package search params */
    type TenantPackageSearchParams = CommonType.RecordNullable<
      Pick<Api.System.TenantPackage, 'packageName' | 'menuIds' | 'menuCheckStrictly' | 'status'> &
        Api.Common.CommonSearchParams
    >;

    /** tenant package operate params */
    type TenantPackageOperateParams = CommonType.RecordNullable<
      Pick<
        Api.System.TenantPackage,
        'packageId' | 'packageName' | 'menuIds' | 'remark' | 'menuCheckStrictly' | 'status'
      >
    >;

    /** tenant package list */
    type TenantPackageList = Api.Common.PaginatingQueryRecord<TenantPackage>;

    /** tenant package select list */
    type TenantPackageSelectList = Common.CommonRecord<Pick<TenantPackage, 'packageId' | 'packageName'>>;

    /** notice */
    type Notice = Common.CommonRecord<{
      /** 公告ID */
      noticeId: CommonType.IdType;
      /** 租户编号 */
      tenantId: CommonType.IdType;
      /** 公告标题 */
      noticeTitle: string;
      /** 公告类型 */
      noticeType: string;
      /** 公告内容 */
      noticeContent: string;
      /** 公告状态 */
      status: string;
      /** 创建者 */
      createByName: string;
      /** 备注 */
      remark: string;
    }>;

    /** notice search params */
    type NoticeSearchParams = CommonType.RecordNullable<
      Pick<Api.System.Notice, 'noticeTitle' | 'noticeType'> & Api.Common.CommonSearchParams
    >;

    /** notice operate params */
    type NoticeOperateParams = CommonType.RecordNullable<
      Pick<Api.System.Notice, 'noticeId' | 'noticeTitle' | 'noticeType' | 'noticeContent' | 'status'>
    >;

    /** notice list */
    type NoticeList = Api.Common.PaginatingQueryRecord<Notice>;

    /** client */
    type Client = Common.CommonRecord<{
      /** id */
      id: CommonType.IdType;
      /** 客户端id */
      clientId: string;
      /** 客户端key */
      clientKey: string;
      /** 客户端秘钥 */
      clientSecret: string;
      /** 授权类型 */
      grantType: string;
      /** 授权类型列表 */
      grantTypeList: string[];
      /** 设备类型 */
      deviceType: string;
      /** token活跃超时时间 */
      activeTimeout: number;
      /** token固定超时 */
      timeout: number;
      /** 状态 */
      status: string;
      /** 删除标志（0代表存在 1代表删除） */
      delFlag: string;
    }>;

    /** client search params */
    type ClientSearchParams = CommonType.RecordNullable<
      Pick<Api.System.Client, 'clientKey' | 'clientSecret' | 'status'> & Api.Common.CommonSearchParams
    >;

    /** client operate params */
    type ClientOperateParams = CommonType.RecordNullable<
      Pick<
        Api.System.Client,
        | 'id'
        | 'clientId'
        | 'clientKey'
        | 'clientSecret'
        | 'grantTypeList'
        | 'deviceType'
        | 'activeTimeout'
        | 'timeout'
        | 'status'
      >
    >;

    /** client list */
    type ClientList = Api.Common.PaginatingQueryRecord<Client>;

    /** social source */
    type SocialSource =
      | 'maxkey'
      | 'topiam'
      | 'qq'
      | 'weibo'
      | 'gitee'
      | 'dingtalk'
      | 'baidu'
      | 'csdn'
      | 'coding'
      | 'oschina'
      | 'alipay_wallet'
      | 'wechat_open'
      | 'wechat_mp'
      | 'wechat_enterprise'
      | 'gitlab'
      | 'github';

    /** oss */
    type Oss = Common.CommonRecord<{
      /** 对象存储主键 */
      ossId: CommonType.IdType;
      /** 租户编号 */
      tenantId: CommonType.IdType;
      /** 文件名 */
      fileName: string;
      /** 原名 */
      originalName: string;
      /** 文件后缀名 */
      fileSuffix: string;
      /** URL地址 */
      url: string;
      /** 扩展属性 */
      ext1: string;
      /** 服务商 */
      service: string;
      /** 创建者名称 */
      createByName: string;
    }>;

    /** oss search params */
    type OssSearchParams = CommonType.RecordNullable<
      Pick<Api.System.Oss, 'fileName' | 'originalName' | 'fileSuffix' | 'service'> & Api.Common.CommonSearchParams
    >;

    /** oss list */
    type OssList = Api.Common.PaginatingQueryRecord<Oss>;

    /** oss access policy */
    type OssAccessPolicy = '0' | '1' | '2';

    /** oss config */
    type OssConfig = Common.CommonRecord<{
      /** 主键 */
      ossConfigId: CommonType.IdType;
      /** 租户编号 */
      tenantId: CommonType.IdType;
      /** 配置名称 */
      configKey: string;
      /** accessKey */
      accessKey: string;
      /** 秘钥secretKey */
      secretKey: string;
      /** 桶名称 */
      bucketName: string;
      /** 前缀 */
      prefix: string;
      /** 访问站点 */
      endpoint: string;
      /** 自定义域名 */
      domain: string;
      /** 是否https（Y=是,N=否） */
      isHttps: Api.Common.YesOrNoStatus;
      /** 域 */
      region: string;
      /** 桶权限类型 */
      accessPolicy: Api.System.OssAccessPolicy;
      /** 是否默认（0=是,1=否） */
      status: Api.Common.EnableStatus;
      /** 扩展字段 */
      ext1: string;
      /** 备注 */
      remark: string;
    }>;

    /** oss config search params */
    type OssConfigSearchParams = CommonType.RecordNullable<
      Pick<Api.System.OssConfig, 'configKey' | 'bucketName' | 'region' | 'status'> & Api.Common.CommonSearchParams
    >;

    /** oss config operate params */
    type OssConfigOperateParams = CommonType.RecordNullable<
      Pick<
        Api.System.OssConfig,
        | 'ossConfigId'
        | 'configKey'
        | 'accessKey'
        | 'secretKey'
        | 'bucketName'
        | 'prefix'
        | 'endpoint'
        | 'domain'
        | 'isHttps'
        | 'region'
        | 'accessPolicy'
        | 'status'
        | 'remark'
      >
    >;

    /** oss config list */
    type OssConfigList = Api.Common.PaginatingQueryRecord<OssConfig>;
    type FileRespVO = {
      /** 配置编号 */
      configId: number;
      /** 创建时间 */
      createTime: Date;
      /** 文件编号 */
      id: number;
      /** 原文件名 */
      name: string;
      /** 文件路径 */
      path: string;
      /** 文件大小 */
      size: number;
      /** 文件MIME类型 */
      type?: string;
      /** 文件 URL */
      url: string;
    };
    // 版本管理
    type Version = {
      /** 实际发布日期 */
      actualReleaseAt?: string[];
      /** 发布状态 */
      status?: number;
      /** 版本号 */
      versionNo?: string;
    };
    type VersionSearchParams = Api.Common.CommonSearchParams & Version;

    /** VersionRespVO，管理后台 - 模块发布 Response VO */
    type VersionRespVO = {
      /** 实际发布概述 */
      actualDesc?: string;
      /** 实际发布日期 */
      actualReleaseAt?: string;
      /** 额外抄送人员信息 */
      ccUserList?: AdminUserDO[];
      /** 额外抄送人员列表 */
      ccUsers?: number[];
      /** 更新说明 */
      detailList?: ModuleDetailReqVO[];
      /** 编号 */
      id?: number;
      /** 版本信息 */
      moduleVersionList?: ModuleVersionRespVO[];
      /** 预计发布概述 */
      plannedDesc?: string;
      /** 预计发布日期 */
      plannedReleaseAt?: string;
      /** 关联需求文档 */
      requirementDoc?: string;
      /** 发布状态 */
      status: number;
      /** 总版本号 */
      versionNo: string;
    };

    /** AdminUserDO，额外抄送人员信息 */
    type AdminUserDO = {
      avatar?: string;
      createTime?: Date;
      creator?: string;
      deleted?: boolean;
      deptId?: number;
      email?: string;
      externalUserId?: string;
      id?: number;
      jobNumber?: string;
      loginDate?: Date;
      loginIp?: string;
      mobile?: string;
      nickname?: string;
      password?: string;
      postIds?: number[];
      remark?: string;
      sex?: number;
      status?: number;
      tenantId?: number;
      unionid?: string;
      updater?: string;
      updateTime?: Date;
      username?: string;
    };

    /** ModuleDetailReqVO，管理后台 - 模块详情 Request VO */
    type ModuleDetailReqVO = {
      /** 更新内容 */
      content: string;
      /** 编号 */
      id?: number;
      /** 菜单ID */
      menuId: number;
      /** 排序字段 */
      sort?: number;
    };

    /** ModuleVersionRespVO，管理后台 - 模块版本 Response VO */
    type ModuleVersionRespVO = {
      /** 编号 */
      id: number;
      /** 模块名称 */
      module: string;
      /** 模块ID */
      moduleId: number;
      /** 模块版本号 */
      moduleVersionNo?: string;
      /** 排序号 */
      sort?: number;
      /** 版本表ID */
      versionId: number;
    };

    type VersionList = Api.Common.ProPaginatingQueryRecord<VersionRespVO>;
    type ModuleRespVO = {
      id?: number;
      /** 说明 */
      description?: string;
      /** 模块名称 */
      module: string;
      /** 推送角色编号列表，范围为角色列表时 */
      pushRoles?: number[];
    };
    type ModuleSearchParams = Api.Common.CommonSearchParams & CommonType.RecordNullable<Pick<ModuleRespVO, 'module'>>;
    type ModuleList = Api.Common.ProPaginatingQueryRecord<ModuleRespVO>;
    /** VersionDingTalkMessageReqVO，管理后台 - 版本发布钉钉请求消息 Request VO */
    type VersionDingTalkMessageReqVO = {
      /** 内容 */
      content: string;
      /** 版本表ID */
      id?: number;
      /** 推送人员 */
      pushUsers: number[];
      /** 标题 */
      title: string;
    };
    /** ModuleVersionReqVO，管理后台 - 模块版本 Request VO */
    type ModuleVersionReqVO = {
      id?: number;
      /** 模块ID */
      moduleId: number;
      /** 模块版本号 */
      moduleVersionNo?: string;
      /** 排序号 */
      sort: number;
    };
    type ModuleVersionSearchParams = CommonType.RecordNullable<ModuleVersionReqVO>;

    /** VersionPushUserRespVO，管理后台 - 推送人员接口 Response VO */
    type VersionPushUserRespVO = {
      /** 推送人员信息 */
      pushUserList?: AdminUserDO[];
      /** 推送人员列表 */
      pushUsers?: number[];
    };
  }
}
