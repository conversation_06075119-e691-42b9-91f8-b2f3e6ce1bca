// import type { ProTreeSelectWithSearchProps } from '@/components/common/dept-select/props';
import type { ProSelectWithSearchProps } from '@/components/common/select-with-search/props';
import type { ProSelectWithSearchSlots } from '@/components/common/select-with-search/slots';

declare module 'pro-naive-ui' {
  interface ProFieldCustomColumn {
    /** 如果扩展了多个 field，你应该如下编写 column: JsonCodeColumn | XColumn | YColumn | .... */
    column: SelectWithSearchColumn | DeptTreeSelectColumn;
  }

  interface SelectWithSearchColumn {
    field: 'select-with-search';
    fieldSlots: ProSelectWithSearchSlots;
    fieldProps: ProSelectWithSearchProps['fieldProps'];
  }

  interface DeptTreeSelectColumn {
    field: 'dept-tree-select';
    // fieldProps: ProTreeSelectWithSearchProps['fieldProps'];
    // fieldSlots: ProSelectWithSearchSlots;
  }
}
