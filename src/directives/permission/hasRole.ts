import type { App } from 'vue';
import { localStg } from '@/utils/storage';
import { $t } from '@/locales';
import { useAuthStore } from '@/store/modules/auth';

export function hasRole(app: App<Element>) {
  app.directive('hasRole', (el: Element, binding) => {
    const { value } = binding;
    const super_admin = 'super_admin';
    const userInfo = localStg.get('userInfo');
    const roles = userInfo?.roles || [];

    if (value && Array.isArray(value) && value.length > 0) {
      const roleFlag = value;

      const hasRole = roles.some((role: string) => {
        return super_admin === role || roleFlag.includes(role);
      });

      if (!hasRole) {
        el.parentNode && el.parentNode.removeChild(el);
      }
    } else {
      throw new Error($t('permission.hasRole'));
    }
  });
}
export function checkHasRole(value: string[]): boolean {
  const authStore = useAuthStore();
  const super_admin = 'super_admin';

  const roles: string[] = authStore.roles || [];

  if (Array.isArray(value) && value.length > 0) {
    if (roles.includes(super_admin)) return true;
    return roles.some((role: string) => value.includes(role));
  }

  throw new Error($t('permission.hasRole'));
}
