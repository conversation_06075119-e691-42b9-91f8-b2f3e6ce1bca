import type { App } from 'vue';
import { $t } from '@/locales';
import { useAuthStore } from '@/store/modules/auth';

/** 判断权限的指令 directive */
export function hasPermi(app: App<Element>) {
  app.directive('hasPermi', (el, binding) => {
    const { value } = binding;
    if (Array.isArray(value) && value.length === 0) {
      return true;
    }
    if (value && Array.isArray(value) && value.length > 0) {
      const hasPermissions = hasPermission(value);
      if (!hasPermissions) {
        el.parentNode && el.parentNode.removeChild(el);
      }
    } else {
      throw new Error($t('permission.hasPermission'));
    }
  });
}

/** 判断权限的方法 function */

const ALL_PERMISSION = 'super_admin';

export const hasPermission = (permissions: string[]) => {
  const authStore = useAuthStore();
  const userPermissions: string[] = authStore.permissions;

  // 拥有全部权限
  if (userPermissions.includes(ALL_PERMISSION)) {
    return true;
  }

  // 拥有传入的任意一个权限
  return permissions.some(p => userPermissions.includes(p));
};
