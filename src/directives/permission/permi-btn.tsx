import { NButton, NPopconfirm } from 'naive-ui';
import { h } from 'vue';

import { useAuth } from '@/hooks/business/auth';
import { $t } from '@/locales';

type BtnType = 'info' | 'primary' | 'success' | 'warning' | 'error';

// eslint-disable-next-line max-params
export function roleBtn(name: string, roles: string[], type: BtnType, clickFun: () => void) {
  const { hasAuth } = useAuth();

  if (!hasAuth(roles) && roles.length > 0) return null;

  // 删除按钮特殊处理：带二次确认
  if (name === '删除') {
    return h(
      NPopconfirm,
      {
        onPositiveClick: clickFun,
        positiveText: '确认',
        negativeText: '取消'
      },
      {
        trigger: () =>
          h(
            NButton,
            {
              ghost: true,
              type,
              size: 'small'
            },
            { default: () => name }
          ),
        default: () => $t('common.confirmDelete')
      }
    );
  }

  // 普通按钮（编辑、新增、详情等）
  return h(
    NButton,
    {
      ghost: true,
      type,
      size: 'small',
      onClick: clickFun
    },
    { default: () => name }
  );
}

// 快捷方法：详情按钮
export function infoRoleBtn(roles: string[], type: BtnType, clickFun: () => void) {
  return roleBtn('详情', roles, type, clickFun);
}

// 快捷方法：编辑按钮
export function editRoleBtn(roles: string[], clickFun: () => void) {
  return roleBtn('编辑', roles, 'primary', clickFun);
}

// 快捷方法：新增按钮
export function addRoleBtn(roles: string[], type: BtnType, clickFun: () => void) {
  return roleBtn('新增', roles, type, clickFun);
}

// 快捷方法：删除按钮（带确认）
export function deleteRoleBtn(roles: string[], clickFun: () => void) {
  return roleBtn('删除', roles, 'error', clickFun);
}
