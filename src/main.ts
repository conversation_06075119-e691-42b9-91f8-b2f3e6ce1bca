import { createApp } from 'vue';

import App from './App.vue';
import { setupI18n } from './locales';
import {
  setupAppVersionNotification,
  setupDayjs,
  setupIconifyOffline,
  setupLoading,
  setupNProgress,
  setupProNaiveComponents
} from './plugins';
import './plugins/assets';
import { setupRouter } from './router';
import { setupStore } from './store';

import { setupAuth, setupMountedFocus } from '@/directives';

async function setupApp() {
  const app = createApp(App);

  setupStore(app);

  setupLoading();

  setupNProgress();

  setupIconifyOffline();

  setupDayjs();

  await setupRouter(app);
  setupProNaiveComponents(app);
  setupI18n(app);

  setupAuth(app);
  setupMountedFocus(app);
  setupAppVersionNotification();
  app.mount('#app');
}

setupApp();
