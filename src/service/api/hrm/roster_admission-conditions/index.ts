import { hrmBaseUrl, request } from '@/service/request';

// ***** 分页查询试用期录用条件 *****
export function fetchGetPromotionConditionsList(params: Api.Hrm.PromotionConditionsListOrDetailRespVOParams) {
  return request<Api.Hrm.PromotionConditionsListOrDetailRespVOList>({
    url: hrmBaseUrl('/promotion-conditions'),
    method: 'get',
    params
  });
}
// 新建试用期录用条件
export function fetchPostPromotionConditions(data: Api.Hrm.PromotionConditionsListOrDetailRespVOParams) {
  return request<Api.Common.ApiNull, 'json'>({
    url: hrmBaseUrl('/promotion-conditions'),
    method: 'post',
    data
  });
}
// 更新试用期录用条件
export function fetchPutPromotionConditions(
  id: Api.Hrm.UpdateOrCreatePromotionConditionsReqVO['id'],
  data: Api.Hrm.PromotionConditionsListOrDetailRespVOParams
) {
  return request<Api.Common.ApiNull, 'json'>({
    url: hrmBaseUrl(`/promotion-conditions/${id}`),
    method: 'put',
    data
  });
}
// 试用期录用条件详情
export function fetchGetPromotionConditions(id: Api.Hrm.UpdateOrCreatePromotionConditionsReqVO['id']) {
  return request<Api.Hrm.UpdateOrCreatePromotionConditionsReqVO>({
    url: hrmBaseUrl(`/promotion-conditions/${id}`),
    method: 'get'
  });
}
// 获取员工的试用期录用条件详情
export function fetchGetPromotionConditionsByEmployeeId(id: Api.Hrm.UpdateOrCreatePromotionConditionsReqVO['id']) {
  return request<Api.Hrm.UpdateOrCreatePromotionConditionsReqVO>({
    url: hrmBaseUrl(`/promotion-conditions/employee/${id}`),
    method: 'get'
  });
}
