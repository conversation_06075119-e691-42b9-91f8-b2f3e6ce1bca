import { hrmBaseUrl, request } from '@/service/request';

// ***** 黑名单列表 *****
export function fetchGetBlackListList(params: Api.Hrm.BlackListSearchParams) {
  return request<Api.Hrm.BlackListTable>({
    url: hrmBaseUrl('/blacklist/blacklist'),
    method: 'get',
    params
  });
}
// ***** 拉黑员工 *****
export function fetchPostBlackList(data: Api.Hrm.BlackListRespVO) {
  return request<Api.Common.ApiNull, 'json'>({
    url: hrmBaseUrl('/blacklist'),
    method: 'post',
    data
  });
}
// ***** 移除拉黑员工 *****
export function fetchDeleteBlackList(id: number) {
  return request<Api.Common.ApiNull, 'json'>({
    url: hrmBaseUrl(`/blacklist/${id}`),
    method: 'delete'
  });
}
// ***** 查询相似的黑名单成员 *****
export function fetchSimilarBlackList(employeeId: string) {
  return request<Api.Hrm.BlackListTable>({
    url: hrmBaseUrl(`/blacklist/similar/${employeeId}`),
    method: 'get'
  });
}
