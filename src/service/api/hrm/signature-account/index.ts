import { hrmBaseUrl, request } from '@/service/request';

// ***** 获取员工事件时间线 *****
export function fetchGetEmployeeMapping() {
  return request<Api.Hrm.EmployeeMappingRespVOList>({
    url: hrmBaseUrl(`/employee/mapping`),
    method: 'get'
  });
}
// ***** 标记全局推送人 *****
export function fetchPutEmployeeMapping(id: number, params: { global: boolean }) {
  return request<Api.Common.ApiNull, 'json'>({
    url: hrmBaseUrl(`/employee/mapping/mark-global/${id}`),
    method: 'put',
    params
  });
}
// ***** 获取契约锁员工下拉框 *****
export function fetchPutEmployeeMappingOptionsQiyuesuo(params: { name: string }) {
  return request<Array<Api.Hrm.IdNameLongQiYueSuoEmployeeRespVO>>({
    url: hrmBaseUrl(`/employee/mapping/options/qiyuesuo`),
    method: 'get',
    params
  });
}
// ***** 更新指定员工的同步结果 *****
export function fetchPostEmployeeMapping(data: Api.Hrm.PostEmployeeMappingReqVO) {
  return request<Api.Common.ApiNull, 'json'>({
    url: hrmBaseUrl(`/employee/mapping/sync`),
    method: 'post',
    data
  });
}
