import { hrmBaseUrl, request } from '@/service/request';

// ***** 获取离职中用户信息 *****
export function fetchGetResigningList(params: Api.Hrm.ResignedReqVOData) {
  const { status, ...rest } = params;
  const applyStatus = status === undefined || status === 1 ? 1 : null;
  const handoverStatus = status === undefined || status === 2 ? 1 : null;
  return request<Api.Hrm.ResignedReqVOList>({
    url: hrmBaseUrl('/process/resignation-page'),
    method: 'get',
    params: {
      ...rest,
      applyStatus,
      handoverStatus
    }
  });
}
// ***** 获取离职结束用户信息 *****
export function fetchGetResigningHistoryList(params: Api.Hrm.ResignedHistoryReqVOData) {
  return request<Api.Hrm.ResignedHistoryReqVOList>({
    url: hrmBaseUrl('/process/resignation-history-page'),
    method: 'get',
    params
  });
}
// ***** 离职历史列表数量 *****
export function fetchGetResigningHistoryCount(name: string) {
  return request<number>({
    url: hrmBaseUrl(`/process/history-count/${name}`),
    method: 'get'
  });
}
