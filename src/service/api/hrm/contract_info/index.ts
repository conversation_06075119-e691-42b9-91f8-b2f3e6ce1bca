import { hrmBaseUrl, request } from '@/service/request';

// ***** 获取业务分类下拉选项 *****
export function fetchGetContactOptions(params: { companyName: string }) {
  return request<Api.Hrm.IdNameInteger[]>({
    url: hrmBaseUrl('/contract/category/options'),
    method: 'get',
    params
  });
}
// ***** 获取业务分类中的所有模板及其参数 *****
export function fetchGetContactDetail(id: number | null) {
  return request<Api.Hrm.QiYueSuoTemplateRespVO[]>({
    url: hrmBaseUrl(`/contract/category/${id}`),
    method: 'get'
  });
}
// ***** 预览合同 *****
export function fetchGetContactPreview(data: Api.Hrm.CreateContractDraftReqVO) {
  return request<Api.Hrm.IdNameInteger>({
    url: hrmBaseUrl(`/contract/preview`),
    method: 'post',
    data
  });
}
// ***** 发起合同 *****
export function fetchPutContactPreview(id: number, userId: number) {
  return request<Api.Common.ApiNull, 'json'>({
    url: hrmBaseUrl(`/contract/send/${id}/${userId}`),
    method: 'put'
  });
}
// ***** 获取合同历史 *****
export function fetchGetContractHistory(employeeId: string) {
  return request<Api.Hrm.SimpleContractRespVO[]>({
    url: hrmBaseUrl(`/contract/history/${employeeId}`),
    method: 'get'
  });
}
// ***** 费用信息 *****
export function fetchGetFeeInfo(companyName: string) {
  return request<Api.Hrm.FeeInfoRespVo>({
    url: hrmBaseUrl(`/contract/fee/${companyName}`),
    method: 'get'
  });
}
// ***** 邀请员工进入钉钉 *****
export function fetchPutInviteToDingtalk(id: number, data: Api.Hrm.InviteToDingTalkReqVOData) {
  return request<Api.Common.ApiNull, 'json'>({
    url: hrmBaseUrl(`/employee/invite-to-dingtalk/${id}`),
    method: 'put',
    data
  });
}
// ***** 获取入职详情 *****
export function fetchGetPreEntryDetail(id: number) {
  return request<Api.Hrm.EmployeeReviewRespVO>({
    url: hrmBaseUrl(`/employee/invite-to-dingtalk/${id}`),
    method: 'get'
  });
}
