import { hrmBaseUrl, request } from '@/service/request';

// ***** 获取合同文件列表 *****
export function fetchGetContactList(params: Api.Common.CommonSearchParams) {
  return request<Api.Hrm.ContractListRespVoList>({
    url: hrmBaseUrl('/contract'),
    method: 'get',
    params
  });
}
// ***** 获取合同文件 *****
export function fetchGetContact(contractId: Api.Hrm.ContractListRespVo['id']) {
  return request<string>({
    url: hrmBaseUrl(`/contract/preview/${contractId}`),
    method: 'get'
  });
}
