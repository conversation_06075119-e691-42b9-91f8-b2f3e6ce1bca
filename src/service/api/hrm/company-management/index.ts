import { hrmBaseUrl, request } from '@/service/request';

// ***** 查询分公司列表 *****
export function fetchGetCompanyList(params: Api.Hrm.CompanyListRespParams) {
  return request<Api.Hrm.CompanyListRespVOList>({
    url: hrmBaseUrl('/company'),
    method: 'get',
    params
  });
}
// ***** 新建分公司 *****
export function fetchPostCompany(data: Api.Hrm.CompanyListRespVO) {
  return request<Api.Common.ApiNull, 'json'>({
    url: hrmBaseUrl('/company'),
    method: 'post',
    data
  });
}
// ***** 更新分公司 *****
export function fetchPutCompany(id: Api.Hrm.CompanyListRespVO['id'], data: Api.Hrm.CompanyListRespVO) {
  return request<Api.Common.ApiNull, 'json'>({
    url: hrmBaseUrl(`/company/${id}`),
    method: 'put',
    data
  });
}
// ***** 获取公司下拉选项 *****
export function fetchPutCompanyOptions() {
  return request<Api.Hrm.IdNameLongObject[]>({
    url: hrmBaseUrl('/company/options'),
    method: 'get'
  });
}
// ***** 获取公司下拉选项 *****
export function fetchGetCompanyInfo(id: Api.Hrm.CompanyListRespVO['id']) {
  return request<Api.Hrm.IdNameLongObject[]>({
    url: hrmBaseUrl(`/company/${id}`),
    method: 'get'
  });
}
