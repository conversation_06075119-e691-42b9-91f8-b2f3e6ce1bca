import { hrmBaseUrl, request } from '@/service/request';

// ***** 获取用户信息列表 *****
export function fetchGetEmployeeList(params: Api.Hrm.SimpleEmployeeInfoSearch) {
  return request<Api.Hrm.SimpleEmployeeInfoList>({
    url: hrmBaseUrl('/employee'),
    method: 'get',
    params
  });
}
// ***** 获取用户信息列表 *****
export function getHrmBasicInfo(id: string) {
  return request<Api.Hrm.IdAndBasicInfoRespVO>({
    url: hrmBaseUrl(`/employee/${id}/basic-info`),
    method: 'get'
  });
}
// ***** 获取教育经历 *****
export function getHrmEducationExperience(id: string) {
  return request<Api.Hrm.FormItem[]>({
    url: hrmBaseUrl(`/employee/${id}/education-experience`),
    method: 'get'
  });
}
// ***** 获取工作经历 *****
export function getHrmWorkExperience(id: string) {
  return request<Api.Hrm.Request>({
    url: hrmBaseUrl(`/employee/${id}/work-experience`),
    method: 'get'
  });
}
// ***** 获得技能与资质证明 *****
export function getHrmTrainCertificate(id: string) {
  return request<Api.Hrm.UpdateTrainingAndCertificatesReqVO[]>({
    url: hrmBaseUrl(`/employee/${id}/train-certificate`),
    method: 'get'
  });
}
// ***** 获得法律与合规文件 *****
export function getHrmLegalCompliance(id: string) {
  return request<Api.Hrm.UpdateLegalComplianceReqVO[]>({
    url: hrmBaseUrl(`/employee/${id}/legal-compliance`),
    method: 'get'
  });
}
// ***** 获取银行与财务信息 *****
export function getFinancialBankInfo(id: string) {
  return request<Api.Hrm.BankCardInfoRespVO>({
    url: hrmBaseUrl(`/employee/${id}/financial-bank-info`),
    method: 'get'
  });
}
// ***** 获取银行与财务信息 *****
export function getFamilyMember(id: string) {
  return request<Api.Hrm.UpdateFamilyMemberDetailRequest>({
    url: hrmBaseUrl(`/employee/${id}/family-members`),
    method: 'get'
  });
}
// ***** 审核员工入职申请 *****
export function putReviewEmployee(id?: string, data: Api.Hrm.ReviewEmployeeReqVO) {
  return request<number, 'json'>({
    url: hrmBaseUrl(`/employee/${id}/review`),
    method: 'put',
    data
  });
}
// ***** 员工状态统计 *****
export function getEmployeeStatusCount() {
  return request<{ [key: string]: number }>({
    url: hrmBaseUrl(`/employee/status/count`),
    method: 'get'
  });
}
// ***** 员工状态 *****
export function getEmployeeStatus(id: string) {
  return request<Api.Hrm.EmployeeStatusRespVO>({
    url: hrmBaseUrl(`/employee/${id}/status`),
    method: 'get'
  });
}
// ***** 获取审核详情 *****
export function getReviewDetail(employeeId: string) {
  return request<Api.Hrm.EmployeeReviewRespVO>({
    url: hrmBaseUrl(`/employee/review/${employeeId}`),
    method: 'get'
  });
}
//  导出员工花名册
export function exportEmployeeRoster(params: {
  deptIds: CommonType.IdType[] | null;
  entryDate: [string, string] | null;
  statusList: number[] | null;
}) {
  return request<Blob, 'blob'>({
    url: hrmBaseUrl(`/employee/export`),
    method: 'get',
    params,
    responseType: 'blob'
  });
}
