import { hrmBaseUrl, request } from '@/service/request';

// ***** 更新员工状态 *****
export function fetchPutEmployeeStatus(id: Api.Hrm.SimpleEmployeeInfo['id'], data: Api.Hrm.UpdateStatusReqVOData) {
  return request<Api.Common.ApiNull, 'json'>({
    url: hrmBaseUrl(`/employee/update-status/${id}`),
    method: 'put',
    data
  });
}
// 获取离职原因选项
export function fetchGetLeaveReasonOptions() {
  return request<Api.Hrm.IdNameLong[]>({
    url: hrmBaseUrl('/employee/options/dismission-reasons'),
    method: 'get'
  });
}
