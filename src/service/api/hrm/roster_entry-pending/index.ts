import { hrmBaseUrl, request } from '@/service/request';

// ***** 获取待入职用户 *****
export function fetchGetPreEntryList(params: Api.Hrm.SimpleEmployeeInfoSearch) {
  return request<Api.Hrm.PreEntryRespVOList>({
    url: hrmBaseUrl('/employee/pre-entry'),
    method: 'get',
    params
  });
}
// ***** 员工放弃入职 *****
export function fetchGiveupPreEntry(employeeId: number, data: { reason: string }) {
  return request<Api.Common.ApiNull, 'json'>({
    url: hrmBaseUrl(`/onboarding/give-up/${employeeId}`),
    method: 'put',
    data
  });
}
