import { hrmBaseUrl, request } from '@/service/request';

// ***** 查询入职链接 *****
export function fetchGetEmployeeList(params: Api.Hrm.OnboardingSearchForm) {
  return request<Api.Hrm.OnboardingLinkRespVOList>({
    url: hrmBaseUrl('/onboarding'),
    method: 'get',
    params
  });
}
// ***** 获取岗位下拉框选项 *****
export function fetchGetPositionOptions(params: { name: string }) {
  return request<Api.Hrm.IdNameLongObject[]>({
    url: hrmBaseUrl('/onboarding/position/options'),
    method: 'get',
    params
  });
}
// ***** 更新入职链接 *****
export function fetchPostOnboarding(data: Api.Hrm.CreateOnboardingReqVo) {
  return request<Api.Common.ApiNull, 'json'>({
    url: hrmBaseUrl('/onboarding'),
    method: 'post',
    data
  });
}
// ***** 更新通知人 *****
export function fetchPutOnboarding(id: number, data: { notifierId: number }) {
  return request<Api.Common.ApiNull, 'json'>({
    url: hrmBaseUrl(`/onboarding/${id}`),
    method: 'put',
    data
  });
}
export function fetchPutStatus(id: number, params: { valid: boolean }) {
  return request<Api.Common.ApiNull, 'json'>({
    url: hrmBaseUrl(`/onboarding/${id}/status`),
    method: 'put',
    params
  });
}
export function fetchDeleteStatus(id: number) {
  return request<Api.Common.ApiNull, 'json'>({
    url: hrmBaseUrl(`/onboarding/${id}`),
    method: 'delete'
  });
}
