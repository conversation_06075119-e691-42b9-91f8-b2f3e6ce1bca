import { adminBaseUrl, request } from '../request';

/**
 * Login
 *
 * @param username User name
 * @param password Password
 * @param captchaEnable Captcha enable
 * @param captchaVerification Captcha verification
 */
// eslint-disable-next-line max-params
export function fetchLogin(username: string, password: string, captchaVerification: string, captchaEnable) {
  return request<Api.Auth.LoginToken>({
    url: adminBaseUrl('/system/auth/login'),
    method: 'post',
    data: {
      username,
      password,
      captchaVerification,
      captchaEnable: captchaEnable !== 'false'
    }
  });
}

// 社交快捷登录，使用 code 授权码
export const socialLogin = (params: any) => {
  return request<Api.Auth.LoginToken>({
    url: adminBaseUrl('/system/auth/dingtalk-login'),
    method: 'post',
    params
  });
};
/** Get user info */
export function fetchGetUserInfo() {
  return request<Api.Auth.UserInfo>({ url: adminBaseUrl('/system/user/profile/get') });
}
/** Permission Info */
export function fetchGetPermissionInfo() {
  return request<Api.SystemManage.AuthPermissionInfoRespVO>({
    url: adminBaseUrl('/system/auth/get-nova-permission-info')
  });
}

/**
 * Refresh token
 *
 * @param refreshToken Refresh token
 */
export function fetchRefreshToken(refreshToken: string) {
  return request<Api.Auth.LoginToken>({
    url: adminBaseUrl('/system/auth/refresh-token'),
    method: 'post',
    params: {
      refreshToken
    }
  });
}
// 滑动或者点选验证
export const reqCheck = (data: any) => {
  return request<Api.Auth.LoginToken>({
    url: adminBaseUrl('/system/captcha/nova-check'),
    method: 'post',
    data
  });
};
// 获取验证图片以及 token
export const getCode = (data: any) => {
  return request<Api.Auth.LoginToken>({
    url: adminBaseUrl('/system/captcha/nova-get'),
    method: 'post',
    data
  });
};
/**
 * return custom backend error
 *
 * @param code error code
 * @param msg error message
 */
export function fetchCustomBackendError(code: string, msg: string) {
  return request({ url: adminBaseUrl('/auth/error'), params: { code, msg } });
}
