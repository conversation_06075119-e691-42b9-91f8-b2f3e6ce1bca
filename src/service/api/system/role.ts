import { adminBaseUrl, request } from '@/service/request';

/** get role all list */
export function fetchGetRoleList(params?: Api.SystemManage.RoleSearchParams) {
  return request<Api.SystemManage.RoleAllList>({
    url: adminBaseUrl('/system/role/page'),
    method: 'get',
    params
  });
}
/** 创建角色 */
export function fetchPostRole(data?: Api.SystemManage.RoleAddParams) {
  return request<Api.Common.ApiNull, 'json'>({
    url: adminBaseUrl('/system/role/create'),
    method: 'post',
    data
  });
}
/** 创建角色 */
export function fetchPutRole(data?: Api.SystemManage.RoleUpdateParams) {
  return request<Api.Common.ApiNull, 'json'>({
    url: adminBaseUrl('/system/role/update'),
    method: 'put',
    data
  });
}
/** 更新角色权限 */
export function fetchPostRoleAuth(data?: Api.SystemManage.PermissionAssignRoleDataScopeReqVO) {
  return request<Api.Common.ApiNull, 'json'>({
    url: adminBaseUrl('/system/permission/assign-role-data-scope'),
    method: 'post',
    data
  });
}

/** get role simple list */
export function fetchGetSimpleRoleList(params?: Api.SystemManage.UserSearchParams) {
  return request<Api.SystemManage.RoleSimpleList>({
    url: adminBaseUrl('/system/role/simple-list'),
    method: 'get',
    params
  });
}
// 删除角色
export function fetchDeleteRole(params?: { id?: number }) {
  return request<Api.SystemManage.RoleSimpleList>({
    url: adminBaseUrl('/system/role/delete'),
    method: 'delete',
    params
  });
}
/** 获得角色拥有的菜单编号 */
export function fetchGetTreeMenuRoleList(params: { roleId?: number }) {
  return request<Array<number>>({
    url: adminBaseUrl('/system/permission/list-role-menus'),
    method: 'get',
    params
  });
}
/** 获得角色拥有的菜单编号 */
export function fetchGetTreeMenuRoleSelect(params: { roleId?: number }) {
  return request<Api.SystemManage.PermissionAssignRoleMenuRespVO>({
    url: adminBaseUrl('/system/permission/list-role-menus-select'),
    method: 'get',
    params
  });
}
/** 赋予角色菜单 */
export function fetchPostTreeMenuRoleList(data: { roleId?: number; menuIds?: number[] }) {
  return request<Array<number>>({
    url: adminBaseUrl('/system/permission/assign-role-menu'),
    method: 'post',
    data
  });
}
/** 赋予角色菜单 */
export function fetchGetSystemOptions() {
  return request<Array<string>>({
    url: adminBaseUrl('/system/role/system-name-options'),
    method: 'get'
  });
}
