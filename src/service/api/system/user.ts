import { adminBaseUrl, hrmBaseUrl, request } from '@/service/request';

/** get user list */
export function fetchGetUserPage(params: Api.SystemManage.UserSearchParams) {
  return request<Api.SystemManage.UserList>({
    url: adminBaseUrl('/system/user/page'),
    method: 'get',
    params
  });
}
// 详情
export function fetchGetUser(params: { id?: number }) {
  return request<Api.SystemManage.UserList>({
    url: adminBaseUrl('/system/user/get'),
    method: 'get',
    params
  });
}
// 更新
export function fetchPutUser(data: Omit<Api.SystemManage.UserUpdateParams, 'password'>) {
  return request<Api.SystemManage.UserList>({
    url: adminBaseUrl('/system/user/update'),
    method: 'put',
    data
  });
}
// 新增
export function fetchPostUser(data: Api.SystemManage.UserUpdateParams) {
  return request<Api.SystemManage.UserList>({
    url: adminBaseUrl('/system/user/create'),
    method: 'post',
    data
  });
}
// 删除
export function fetchDeleteUser(params: { id?: number }) {
  return request<Api.Common.ApiNull, 'json'>({
    url: adminBaseUrl('/system/user/delete'),
    method: 'delete',
    params
  });
}
// 修改用户状态
export function fetchUpdateUserStatus(data: { id?: number; status: number }) {
  return request<Api.Common.ApiNull, 'json'>({
    url: adminBaseUrl('/system/user/update-status'),
    method: 'put',
    data
  });
}
// 获取用户角色
export function fetchGetUserRoles(params: { userId?: number }) {
  return request<Api.Common.ApiNull, 'json'>({
    url: adminBaseUrl('/system/permission/list-user-roles'),
    method: 'get',
    params
  });
}
// 获取用户角色
export function fetchUpdateUserRoles(data: { userId?: number; roleIds?: number[] }) {
  return request<Api.Common.ApiNull, 'json'>({
    url: adminBaseUrl('/system/permission/assign-user-role'),
    method: 'post',
    data
  });
}
// 重置密码
export function fetchResetPassword(data: { id?: number; password?: string }) {
  return request<Api.Common.ApiNull, 'json'>({
    url: adminBaseUrl('/system/user/update-password'),
    method: 'put',
    data
  });
}
// 推送系统账号到钉钉
export function fetchPushAccount(params: { id?: number }) {
  return request<Api.Common.ApiNull, 'json'>({
    url: adminBaseUrl('/system/user/send_account'),
    method: 'post',
    params
  });
}
// ***** 获取岗位下拉框选项 *****
export function fetchGetPositionOptions() {
  return request({
    url: hrmBaseUrl('/onboarding/position/options'),
    method: 'get'
  });
}
// 获取表单描述
export function fetchGetFormDesc(params: { code: string; scene: string }) {
  return request({
    url: adminBaseUrl('/system/reference-data/desc'),
    method: 'get',
    params
  });
}
