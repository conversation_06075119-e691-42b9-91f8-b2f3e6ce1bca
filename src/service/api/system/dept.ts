import { adminBaseUrl, request } from '@/service/request';

/** 获取部门平铺列表 */
export function fetchGetDeptList(params: Api.System.DeptSearchParams) {
  return request<Api.Kip.Dept[]>({
    url: adminBaseUrl('/system/dept/list'),
    method: 'get',
    params
  });
}
/** 获取部门树形列表 */
export function fetchGetDeptTreeList() {
  return request<Api.SystemManage.TreeDeptRespList>({
    url: adminBaseUrl('/system/dept/tree-list'),
    method: 'get'
  });
}
