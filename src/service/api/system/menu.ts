import { adminBaseUrl, request } from '@/service/request';

/** 获取树形菜单列表 */
export function fetchGetTreeMenuList() {
  return request<Api.SystemManage.TreeMenuRespVOList>({
    url: adminBaseUrl('/system/menu/nova-tree-list'),
    method: 'get'
  });
}
/** 修改菜单 */
export function fetchPutMenu(data: Api.System.MenuOperateParams) {
  return request<Api.Common.ApiNull, 'json'>({
    url: adminBaseUrl('/system/menu/nova-update'),
    method: 'put',
    data
  });
}
/** 新增 */
export function fetchPostMenu(data: Api.System.MenuOperateParams) {
  return request<Api.Common.ApiNull, 'json'>({
    url: adminBaseUrl('/system/menu/nova-create'),
    method: 'post',
    data
  });
}
/** 删除 */
export function fetchDeleteMenu(params: { id?: number }) {
  return request<Api.Common.ApiNull, 'json'>({
    url: adminBaseUrl('/system/menu/delete'),
    method: 'delete',
    params
  });
}
export function fetchGetMenuList(params?: Api.System.MenuSearchParams) {
  return request<Api.System.MenuList>({
    url: adminBaseUrl('/system/menu/nova-list'),
    method: 'get',
    params
  });
}
