import { adminBaseUrl, request } from '@/service/request';

/** 获取版本列表 */
export function fetchGetVersionPage(params: Api.System.VersionSearchParams) {
  return request<Api.System.VersionList>({
    url: adminBaseUrl('/system/version/version-page'),
    method: 'get',
    params
  });
}
/** 创建版本 */
export function fetchCreateVersion(data: Api.System.VersionRespVO) {
  return request<Api.Common.ApiNull, 'json'>({
    url: adminBaseUrl('/system/version/create-version'),
    method: 'post',
    data
  });
}
// 更新版本
export function fetchUpdateVersion(id: number, data: Api.System.VersionRespVO) {
  return request<Api.Common.ApiNull, 'json'>({
    url: adminBaseUrl(`/system/version/${id}/version`),
    method: 'put',
    data
  });
}
// 删除版本
export function fetchDeleteVersion(id: number) {
  return request<Api.Common.ApiNull, 'json'>({
    url: adminBaseUrl(`/system/version/delete-version`),
    method: 'delete',
    params: {
      id
    }
  });
}
// 获取版本详情
export function fetchGetVersionDetail(id: number) {
  return request<Api.System.VersionRespVO>({
    url: adminBaseUrl(`/system/version/${id}/version`),
    method: 'get'
  });
}

// 获取模块列表
export function fetchGetModuleList(params: Api.System.ModuleSearchParams) {
  return request<Api.System.ModuleList>({
    url: adminBaseUrl(`/system/version/module-page`),
    method: 'get',
    params
  });
}
// 新增模版数据
export function fetchCreateModule(data: Api.System.ModuleRespVO) {
  return request<Api.Common.ApiNull, 'json'>({
    url: adminBaseUrl(`/system/version/create-module`),
    method: 'post',
    data
  });
}
// 获取单个模版信息
export function fetchGetModuleDetail(id: number) {
  return request<Api.System.ModuleRespVO>({
    url: adminBaseUrl(`/system/version/${id}/module`),
    method: 'get'
  });
}
// 更新模版数据
export function fetchUpdateModule(id: number, data: Api.System.ModuleRespVO) {
  return request<Api.Common.ApiNull, 'json'>({
    url: adminBaseUrl(`/system/version/${id}/module`),
    method: 'put',
    data
  });
}
// 删除模版数据
export function fetchDeleteModule(id: number) {
  return request<Api.Common.ApiNull, 'json'>({
    url: adminBaseUrl(`/system/version/delete-module`),
    method: 'delete',
    params: {
      id
    }
  });
}
// 推送版本发布消息
export function fetchPushVersionMessage(data: Api.System.VersionDingTalkMessageReqVO) {
  return request<Api.Common.ApiNull, 'json'>({
    url: adminBaseUrl(`/system/version/message`),
    method: 'post',
    data
  });
}
// 更新模块版本信息
export function fetchUpdateModuleVersion(id: number, data: Api.System.ModuleVersionSearchParams[]) {
  return request<Api.Common.ApiNull, 'json'>({
    url: adminBaseUrl(`/system/version/${id}/module-version`),
    method: 'put',
    data
  });
}
// 获取模块版本列表
export function fetchGetModuleVersionList(params: { moduleId: number; pageSize: number }) {
  return request<Api.Common.ProPaginatingQueryRecord<Api.System.ModuleVersionReqVO>>({
    url: adminBaseUrl(`/system/version/module-version-page`),
    method: 'get',
    params
  });
}
// 获取版本推送人员
export function fetchGetVersionPushUsers(id: number) {
  return request<Api.System.VersionPushUserRespVO>({
    url: adminBaseUrl(`/system/version/${id}/push-user`),
    method: 'get'
  });
}
