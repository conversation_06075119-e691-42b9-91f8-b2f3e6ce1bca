import { adminBaseUrl, request } from '@/service/request';

// 扩展搜索参数类型，添加createTime字段
type ExtendedDictTypeSearchParams = Api.System.DictTypeSearchParams & {
  createTime?: string | [string | undefined, string | undefined];
  pageSize?: number;
  pageNo?: number;
};

export function fetchGetDictList(params?: ExtendedDictTypeSearchParams) {
  return request<Api.SystemManage.PageResultDictTypeRespVO>({
    url: adminBaseUrl('/system/dict-type/page'),
    method: 'get',
    params
  });
}

export function fetchGetDict(params?: { id: number }) {
  return request<Api.SystemManage.DictTypeRespVO>({
    url: adminBaseUrl('/system/dict-type/get'),
    method: 'get',
    params
  });
}
export function fetchDeleteDict(params?: { id: number }) {
  return request<Api.Common.ApiNull, 'json'>({
    url: adminBaseUrl('/system/dict-type/delete'),
    method: 'delete',
    params
  });
}
export function fetchPostDict(data?: Api.SystemManage.DictTypeRespVO) {
  return request<Api.Common.ApiNull, 'json'>({
    url: adminBaseUrl('/system/dict-type/create'),
    method: 'post',
    data
  });
}

export function fetchPutDict(data?: Api.SystemManage.DictTypeRespVO) {
  return request<Api.Common.ApiNull, 'json'>({
    url: adminBaseUrl('/system/dict-type/update'),
    method: 'put',
    data
  });
}
export function fetchGetDictDataList(params?: ExtendedDictTypeSearchParams) {
  return request<Api.SystemManage.PageResultDictTypeRespVO>({
    url: adminBaseUrl('/system/dict-data/page'),
    method: 'get',
    params
  });
}
// 子数据
export function fetchGetDictTypeList(params?: ExtendedDictTypeSearchParams) {
  return request<Api.SystemManage.PageResultDictTypeRespVO>({
    url: adminBaseUrl('/system/dict-type/list-all-simple'),
    method: 'get',
    params
  });
}
// 子数据添加
export function fetchPostDictData(data?: Api.SystemManage.DictData) {
  return request<Api.Common.ApiNull, 'json'>({
    url: adminBaseUrl('/system/dict-data/create'),
    method: 'post',
    data
  });
}
// 子数据更新
export function fetchPutDictData(data?: Api.SystemManage.DictData) {
  return request<Api.Common.ApiNull, 'json'>({
    url: adminBaseUrl('/system/dict-data/update'),
    method: 'put',
    data
  });
}
// 获取子数据
export function fetchGetDictData(params?: { id: number }) {
  return request<Api.Common.ApiNull, 'json'>({
    url: adminBaseUrl('/system/dict-data/get'),
    method: 'get',
    params
  });
}
// 删除子数据
export function fetchDeleteDictData(params?: { id: number }) {
  return request<Api.Common.ApiNull, 'json'>({
    url: adminBaseUrl('/system/dict-data/delete'),
    method: 'delete',
    params
  });
}
