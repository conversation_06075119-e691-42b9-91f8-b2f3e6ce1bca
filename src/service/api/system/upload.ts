import { adminBaseUrl, request } from '@/service/request';

/** 上传文件 */
export function fetchUploadFile(tag: 'COMPANY' | 'STAFF', data: FormData) {
  return request<Api.Common.ApiNull, 'json'>({
    url: adminBaseUrl(`/infra/file/upload/${tag}`),
    method: 'post',
    data,
    timeout: 300 * 1000
  });
}
/** 获取文件详情 */
export function fetchGetFile(id: number) {
  return request<Api.System.FileRespVO>({
    url: adminBaseUrl(`/infra/file/detail/${id}`),
    method: 'get'
  });
}
