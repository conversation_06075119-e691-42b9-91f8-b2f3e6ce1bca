import { crmBaseUrl, request } from '@/service/request'; // ***** 目标客户接口 *****

// ***** 目标客户接口 *****
export function fetchGetTargetList(params?: Api.Crm.TargetSearchParams) {
  return request<Api.Crm.TargetList>({
    url: crmBaseUrl('/targets/page'),
    method: 'get',
    params
  });
}
// ***** 获得目标客户查询结果，不分页 *****
export function fetchGetTargetListAll(params?: Api.Crm.TargetSearchParams) {
  return request<Api.Crm.TargetList>({
    url: crmBaseUrl('/targets/list-all'),
    method: 'get',
    params
  });
}
// ***** 删除目标客户 *****
export function fetchDeleteTarget(params: { id: number }) {
  return request<Api.Common.ApiNull, 'json'>({
    url: crmBaseUrl('/targets/mark'),
    method: 'delete',
    params
  });
}
// ***** 还原目标客户 *****
export function fetchRestoreTarget(params: { id: number }) {
  return request<Api.Common.ApiNull, 'json'>({
    url: crmBaseUrl('/targets/restore'),
    method: 'delete',
    params
  });
}

export function fetchGetTargetSelect(params?: Api.Crm.TargetSearchParams) {
  return request<Api.Crm.TargetSelect>({
    url: crmBaseUrl('/targets/list'),
    method: 'get',
    params
  });
}

export function fetchGetTargetDetail(id: number) {
  return request<Api.Crm.Target>({
    url: crmBaseUrl(`/targets/${id}`),
    method: 'get'
  });
}

export function fetchAddTarget(data: Api.Crm.TargetFormParams) {
  return request<Api.Common.ApiNull, 'json'>({
    url: crmBaseUrl('/targets/create'),
    method: 'post',
    data
  });
}

export function fetchUpdateTarget(data: Api.Crm.TargetFormParams) {
  return request<Api.Common.ApiNull, 'json'>({
    url: crmBaseUrl('/targets/update'),
    method: 'put',
    data
  });
}

// ***** 项目接口 *****
export function fetchGetProjectList(params?: Api.Crm.ProjectSearchParams) {
  return request<Api.Crm.ProjectList>({
    url: crmBaseUrl('/projects/page'),
    method: 'get',
    params
  });
}

export function fetchGetProjectSelect(params?: Api.Crm.ProjectSearchParams) {
  return request<Api.Crm.ProjectSelect>({
    url: crmBaseUrl('/projects/list'),
    method: 'get',
    params
  });
}
// 项目交接
export function fetchPuProjectTransfer(data: Api.Crm.TransferModel) {
  return request<Api.Common.ApiNull, 'json'>({
    url: crmBaseUrl('/projects/transfer'),
    method: 'put',
    data
  });
}
// 项目状态
export function fetchGetProjectState(params: Api.Crm.ProjectStateSearchParams) {
  return request<Array<Api.Crm.ProjectState>>({
    url: crmBaseUrl(`/status/list`),
    method: 'get',
    params
  });
}

export function fetchGetProjectDetail(id: number) {
  return request<Api.Crm.Project>({
    url: crmBaseUrl(`/projects/get`),
    method: 'get',
    params: { id }
  });
}

export function fetchAddProject(data: Api.Crm.ProjectFormParams) {
  return request<Api.Crm.ProjectResponse, 'json'>({
    url: crmBaseUrl('/projects/create'),
    method: 'post',
    data
  });
}

// 创建立项
export function fetchPostTiggerMarkersProject(data: Api.Crm.MarkerTigger) {
  return request<Api.Crm.ProjectResponse, 'json'>({
    url: crmBaseUrl('/markers/create'),
    method: 'post',
    data
  });
}
// 更新立项
export function fetchPutTiggerMarkersProject(data: Api.Crm.MarkerTigger) {
  return request<Api.Common.ApiNull, 'json'>({
    url: crmBaseUrl('/markers/update'),
    method: 'put',
    data
  });
}
// 立项评审
export function fetchPutReviewMarkersProject(data: Api.Crm.MarkerReview) {
  return request<Api.Common.ApiNull, 'json'>({
    url: crmBaseUrl('/markers/review'),
    method: 'put',
    data
  });
}

// 查询立项详情
export function fetchGetMarkersProject(params: Api.Crm.MarkerSearchParams) {
  return request<Api.Crm.Marker, 'json'>({
    url: crmBaseUrl('/markers/get'),
    method: 'get',
    params
  });
}

export function fetchUpdateProject(data: Api.Crm.ProjectFormParams) {
  return request<Api.Common.ApiNull, 'json'>({
    url: crmBaseUrl(`/projects/update`),
    method: 'put',
    data
  });
}

export function fetchDeleteProject(data: Api.Common.CommonDeleteParams) {
  return request<Api.Common.ApiNull, 'json'>({
    url: crmBaseUrl(`/projects/${data.id}`),
    method: 'delete'
  });
}

// ***** 联络人接口 *****
export function fetchGetCustomerList(params?: Api.Crm.CustomerSearchParams) {
  return request<Api.Crm.CustomerList>({
    url: crmBaseUrl('/customers/page'),
    method: 'get',
    params
  });
}

export function fetchGetCustomerSelect(params?: Api.Crm.CustomerSearchParams) {
  return request<Api.Crm.CustomerSelect>({
    url: crmBaseUrl('/customers/list'),
    method: 'get',
    params
  });
}

export function fetchGetCustomerDetail(id: number) {
  return request<Api.Crm.Customer>({
    url: crmBaseUrl('/customers/get'),
    method: 'get',
    params: {
      id
    }
  });
}

export function fetchAddCustomer(data: Api.Crm.CustomerFormParams) {
  return request<Api.Common.ApiNull, 'json'>({
    url: crmBaseUrl('/customers/create'),
    method: 'post',
    data
  });
}

export function fetchUpdateCustomer(data: Api.Crm.CustomerFormParams) {
  return request<Api.Common.ApiNull, 'json'>({
    url: crmBaseUrl('/customers/update'),
    method: 'put',
    data
  });
}

export function fetchDeleteCustomer(data: Api.Common.CommonDeleteParams) {
  return request<Api.Common.ApiNull, 'json'>({
    url: crmBaseUrl(`/customers/${data.id}`),
    method: 'delete'
  });
}

// ***** 拜访接口 *****
export function fetchGetFollowList(params?: Api.Crm.FollowSearchParams) {
  return request<Api.Crm.FollowList>({
    url: crmBaseUrl('/visits/page'),
    method: 'get',
    params
  });
}

export function fetchGetFollowDetail(id: number) {
  return request<Api.Crm.FollowDetail>({
    url: crmBaseUrl('/visits/get'),
    method: 'get',
    params: {
      id
    }
  });
}
// 获得拜访记录
export function fetchGetVisits(planId: number) {
  return request<Api.Crm.FollowDetail>({
    url: crmBaseUrl('/visits/get-by-plan-id'),
    method: 'get',
    params: {
      planId
    }
  });
}

export function fetchAddFollow(data: Api.Crm.FollowAddParams) {
  return request<Api.Common.ApiNull, 'json'>({
    url: crmBaseUrl('/visits/create'),
    method: 'post',
    data
  });
}

export function fetchUpdateFollow(data: Api.Crm.FollowFormParams) {
  return request<Api.Common.ApiNull, 'json'>({
    url: crmBaseUrl('/visits/update'),
    method: 'put',
    data
  });
}

// 获取最新的一次评审记录
export function fetchGetFollowReview(id: number) {
  return request<Api.Crm.Review, 'json'>({
    url: crmBaseUrl('/visits/review/get'),
    method: 'get',
    params: { visitId: id }
  });
}

// 提交评审
export function fetchUpdateFollowReview(data: Api.Crm.FollowReviewParams) {
  return request<Api.Common.ApiNull, 'json'>({
    url: crmBaseUrl('/visits/review'),
    method: 'put',
    data
  });
}

// ***** 拜访计划 *****
export function fetchGetPlanList(params?: Api.Crm.PlanSearchParams) {
  return request<Api.Crm.PlanList>({
    url: crmBaseUrl('/plans/all-list'),
    method: 'get',
    params
  });
}

export function fetchGetPlanSelect(params?: Api.Crm.PlanSearchParams) {
  return request<Api.Crm.PlanSelect>({
    url: crmBaseUrl('/plans/list'),
    method: 'get',
    params
  });
}

export function fetchGetPlanDetail(id: number) {
  return request<Api.Crm.Plan>({
    url: crmBaseUrl('/plans/get'),
    method: 'get',
    params: { id }
  });
}

export function fetchAddPlan(data: Api.Crm.PlanFormParams) {
  return request<Api.Common.ApiNull, 'json'>({
    url: crmBaseUrl('/plans/create'),
    method: 'post',
    data
  });
}

export function fetchUpdatePlan(data: Api.Crm.PlanFormParams) {
  return request<Api.Common.ApiNull, 'json'>({
    url: crmBaseUrl('/plans/update'),
    method: 'put',
    data
  });
}

// 取消计划
export function fetchCancelPlan(data: Api.Crm.PlanCancelParams) {
  return request<Api.Common.ApiNull, 'json'>({
    url: crmBaseUrl('/plans/cancel'),
    method: 'put',
    data
  });
}

export function fetchDeletePlan(data: Api.Common.CommonDeleteParams) {
  return request<Api.Common.ApiNull, 'json'>({
    url: crmBaseUrl(`/plans/${data.id}`),
    method: 'delete'
  });
}
