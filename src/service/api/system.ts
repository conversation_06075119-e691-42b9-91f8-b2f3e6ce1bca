import { adminBaseUrl, appApiBaseUrl, request } from '../request';

/** get user list */
export function fetchGetUserList(params?: Api.SystemManage.UserSearchParams) {
  return request<Api.SystemManage.UserList>({
    url: adminBaseUrl('/users'),
    method: 'get',
    params
  });
}

/** get user select */
export function fetchGetUserSelect(params?: Api.SystemManage.UserSearchParams) {
  return request<Api.SystemManage.UserSelect>({
    url: adminBaseUrl('/system/user/list-all-username-simple'),
    method: 'get',
    params
  });
}
// 获取角色代码下用户搜索下拉列表
export function fetchGetUserListByRole(params?: Api.SystemManage.UserSearchParams) {
  return request<Api.SystemManage.UserSelect>({
    url: adminBaseUrl('/system/user/user-list-by-role'),
    method: 'get',
    params
  });
}

/** 获取钉钉用户列表 */
export function fetchGetDingtalkUserSelect(params?: Api.SystemManage.UserSearchParams) {
  return request<Array<Api.SystemManage.DingTalkUser>>({
    url: adminBaseUrl('/users/ding_users'),
    method: 'get',
    params
  });
}

/** add user */
export function fetchAddUser(data?: Api.SystemManage.UserAddParams) {
  return request<Api.SystemManage.UserList, 'json'>({
    url: adminBaseUrl('/users'),
    method: 'post',
    data
  });
}

/** update user */
export function fetchUpdateUser(data?: Api.SystemManage.UserUpdateParams) {
  return request<Api.SystemManage.UserList, 'json'>({
    url: adminBaseUrl(`/users/${data?.id}`),
    method: 'put',
    data
  });
}

/** delete user */
export function fetchDeleteUser(data?: Api.SystemManage.CommonDeleteParams) {
  return request<Api.SystemManage.UserList>({
    url: adminBaseUrl(`/users/${data?.id}`),
    method: 'delete'
  });
}

export function fetchBatchDeleteUser(data?: Api.SystemManage.CommonBatchDeleteParams) {
  return request<Api.SystemManage.UserList>({
    url: adminBaseUrl('/system-manage/users'),
    method: 'delete',
    params: { ids: data?.ids.join(',') }
  });
}

/** get role list */
export function fetchGetRoleList(params?: Api.SystemManage.RoleSearchParams) {
  return request<Api.SystemManage.RoleAllList>({
    url: adminBaseUrl('/roles/list'),
    method: 'get',
    params
  });
}

/** 修改密码 */
export function fetchUpdatePassword(data?: Api.System.ChangePasswordParams) {
  return request<Api.Common.ApiNull, 'json'>({
    url: adminBaseUrl('/system/user/profile/update-nova-password'),
    method: 'put',
    data
  });
}
/** 发送验证码 */
export function fetchSendCaptcha(params?: { id: number }) {
  return request<Api.Common.ApiNull, 'json'>({
    url: adminBaseUrl('/system/dingtalk/send-captcha'),
    method: 'get',
    params
  });
}

/** 获取字典 */
export function getDictDataOptionsApi(params: { keys: string }) {
  return request<Api.System.OptionObject>({
    url: appApiBaseUrl('/system/dict-data/options'),
    method: 'get',
    params
  });
}
/** 获取部门下用户列表 */
export function getUserListByDeptApi(params: { nickname: string; pageNo: number }) {
  return request<Api.System.UserSimpleRespVO[]>({
    url: adminBaseUrl('/system/user/user-list-by-dept'),
    method: 'get',
    params
  });
}
