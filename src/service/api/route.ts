import { adminBaseUrl, request } from '../request';

/** get constant routes */
export function fetchGetConstantRoutes() {
  return request<Api.Route.MenuRoute[]>({ url: adminBaseUrl('/route/getConstantRoutes') });
}

/** get user routes */
export function fetchGetUserRoutes() {
  return request<Api.Route.UserRoute>({ url: adminBaseUrl('/route/getUserRoutes') });
}

/**
 * whether the route is exist
 *
 * @param routeName route name
 */
export function fetchIsRouteExist(routeName: string) {
  return request<boolean>({ url: adminBaseUrl('/route/isRouteExist'), params: { routeName } });
}
