import { adminBaseUrl, request } from '@/service/request';

// ***** 获取所有部门 *****
export function fetchGetDeptList(params?: Api.Kip.DeptSelectForm) {
  return request<Api.Kip.DeptSelectList>({
    url: adminBaseUrl('/system/dept/list'),
    method: 'get',
    params
  });
}
// ***** 更新部门 *****
export function fetchPutDept(id?: number, data?: { description: string }) {
  return request<Api.Common.ApiNull, 'json'>({
    url: adminBaseUrl(`/system/dept/update/description/${id}`),
    method: 'put',
    data
  });
}
// ***** 获得部门信息 *****
export function fetchGetDept(id?: number) {
  return request<Api.Common.ApiNull, 'json'>({
    url: adminBaseUrl('/system/dept/get'),
    method: 'get',
    params: {
      id
    }
  });
}
// ***** 获得部门信息 *****
export function fetchPutJobNumber(id?: number, jobNumberPrefix?: string, jobNumberSuffix?: string) {
  return request<Api.Common.ApiNull, 'json'>({
    url: adminBaseUrl(`/system/dept/update/${id}/${jobNumberPrefix}/${jobNumberSuffix}`),
    method: 'put'
  });
}
// ***** 获取指定工号前缀的部门名称 *****
export function fetchGetDeptNameList(prefix?: string) {
  return request<string[]>({
    url: adminBaseUrl(`/system/dept/name-list/${prefix}`),
    method: 'get'
  });
}
