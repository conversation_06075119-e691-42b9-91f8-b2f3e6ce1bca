import { adminBaseUrl, coreBaseUrl, request } from '@/service/request';

// ***** 获取所有关键事项 *****
export function fetchGetKeyIssuesList(params?: Api.Kip.KeyIssuesFormParams) {
  return request<Api.Kip.KeyIssuesList>({
    url: coreBaseUrl('/key-issues'),
    method: 'get',
    params
  });
}
// ***** 获取关键事项详情 *****
export function fetchGetKeyIssuesDetail(id?: Api.Kip.KeyIssuesDetail) {
  return request<Api.Kip.KeyIssuesFormParams>({
    url: coreBaseUrl(`/key-issues/${id}`),
    method: 'get'
  });
}
// ***** 新增关键事项详情 *****
export function fetchPostKeyIssuesDetail(data?: Api.Kip.KeyIssuesFormParams) {
  return request<Api.Common.ApiNull, 'json'>({
    url: coreBaseUrl(`/key-issues`),
    method: 'post',
    data
  });
}
// ***** 编辑关键事项详情 *****
export function fetchPutKeyIssuesDetail(data: Api.Kip.KeyIssuesFormParams) {
  return request<Api.Common.ApiNull, 'json'>({
    url: coreBaseUrl(`/key-issues/${data.id}`),
    method: 'put',
    data
  });
}
// ***** 获取部门列表 *****
export function fetchGetDeptSimpleList(params: { name: string }) {
  return request<Api.Kip.DeptList>({
    url: adminBaseUrl('/system/dept/list'),
    method: 'get',
    params
  });
}
