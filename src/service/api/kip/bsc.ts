import { coreBaseUrl, request } from '@/service/request';

// ***** bsc列表 *****
export function fetchGetBscList(params: Api.Kip.BscParams) {
  return request<Api.Kip.BscList>({
    url: coreBaseUrl('/metrics'),
    method: 'get',
    params
  });
}

// ***** bsc详情 *****
export function fetchGetBsc(id?: number) {
  return request<Api.Kip.BscParams>({
    url: coreBaseUrl(`/metrics/${id}`),
    method: 'get'
  });
}

// ***** 创建bsc *****
export function fetchPostBsc(data?: Api.Kip.BscFormParams) {
  return request<Api.Common.ApiNull, 'json'>({
    url: coreBaseUrl(`/metrics`),
    method: 'post',
    data
  });
}

// ***** 更新bsc *****
export function fetchPutBsc(data: Api.Kip.BscFormParams) {
  return request<Api.Common.ApiNull, 'json'>({
    url: coreBaseUrl(`/metrics/${data.id}`),
    method: 'put',
    data
  });
}

// 获取bsc指标详情
export function fetchGetBscMetric(parentId: number) {
  return request<Api.Kip.MetricInfoRespInfo>({
    url: coreBaseUrl(`/metrics/info/getByParent/${parentId}`),
    method: 'get'
  });
}
// 删除BSC指标
export function fetchDelBscMetric(
  id: number,
  data?: Array<{
    keyIssueId: number;
    metricId: number | null;
  }>
) {
  return request<Api.Common.ApiNull, 'json'>({
    url: coreBaseUrl(`/metrics/${id}`),
    method: 'delete',
    data
  });
}
// 更新截至本月的数据
export function fetchUpdateYtd(id: number, data: Api.Kip.UpdateYtd) {
  return request<Api.Common.ApiNull, 'json'>({
    url: coreBaseUrl(`/metrics/updateYtd/${id}`),
    method: 'put',
    data
  });
}
// 更新
export function fetchPutBscMetric(data: Api.Kip.KipMetricsInfo) {
  return request<Api.Common.ApiNull, 'json'>({
    url: coreBaseUrl(`/metrics/info/updateByParent/${data.parentId}`),
    method: 'put',
    data
  });
}
//* ********************* bsc指标 ***************************
// 获取实际数据
export function fetchGetByParent(parentId: Api.Kip.KipSubMetricsInfo['id']) {
  return request<Api.Kip.ActualMetricsDataRespVo>({
    url: coreBaseUrl(`/metrics/actual/getByParent/${parentId}`),
    method: 'get'
  });
}
// 更新实际数据
export function fetchUpdateByParent(parentId: number, data: Api.Kip.ActualDataInfoRespVoList) {
  return request<Api.Common.ApiNull, 'json'>({
    url: coreBaseUrl(`/metrics/actual/updateByParent/${parentId}`),
    method: 'put',
    data
  });
}
// 根据指标名称和年份查询指标名称和id
export function fetchMetricsOptions(params: { name: string; year?: number }) {
  return request<
    Array<{
      id: number;
      name: string;
      sort: string;
    }>
  >({
    url: coreBaseUrl(`/metrics/metricsOptions`),
    method: 'get',
    params
  });
}
// 根据指标名称和年份查询战略目标名称和id
export function fetchTargetOptions(params: { name: string; year?: number }) {
  return request<
    Array<{
      id: number;
      name: string;
      sort: string;
    }>
  >({
    url: coreBaseUrl(`/metrics/targetOptions`),
    method: 'get',
    params
  });
}
// 查询指标的关键事项
export function fetchGetWithKeyIssues(metricsId: number) {
  return request<Api.Kip.MetricsWithKeyIssuesRespVo>({
    url: coreBaseUrl(`/metrics/withKeyIssues/${metricsId}`),
    method: 'get'
  });
}
// 拆解关键事项
export function fetchPostWithKeyIssues(data: Api.Kip.MetricsWithKeyIssuesRespVo) {
  return request<Api.Common.ApiNull, 'json'>({
    url: coreBaseUrl('/metrics/split'),
    method: 'post',
    data
  });
}
// 更新关键事项的排序号
export function fetchPutSort(
  data: Array<{
    id: number;
    sort: string;
  }>
) {
  return request<Api.Common.ApiNull, 'json'>({
    url: coreBaseUrl('/key-issues/sort'),
    method: 'put',
    data
  });
}
// 恢复
export function fetchRestoreBsc(id: number) {
  return request<Api.Common.ApiNull, 'json'>({
    url: coreBaseUrl(`/metrics/recover/${id}`),
    method: 'put'
  });
}
