import { coreBaseUrl, request } from '@/service/request';

// ***** 分页查询待办事项列表 *****
export function fetchGetTodoList(params?: Api.Kip.TaskCenterSearchParams) {
  return request<Api.Kip.TaskCenterList>({
    url: coreBaseUrl('/todo'),
    method: 'get',
    params
  });
}
// ***** 新增待办事项 *****
export function fetchPostTodoList(data: Api.Kip.TaskCenterSearchParams) {
  return request<Api.Common.ApiNull, 'json'>({
    url: coreBaseUrl('/todo'),
    method: 'post',
    data
  });
}
// ***** 编辑待办事项 *****
export function fetchPutTodoList(id: number, data: Api.Kip.TaskCenterPutParams) {
  return request<Api.Common.ApiNull, 'json'>({
    url: coreBaseUrl(`/todo/${id}`),
    method: 'put',
    data
  });
}
// ***** 获取待办事项详情 *****
export function fetchGetTodoDetail(id: number) {
  return request<Api.Kip.TodoListDetailRespVO>({
    url: coreBaseUrl(`/todo/${id}`),
    method: 'get'
  });
}
// ***** 删除待办事项 *****
export function fetchDeleteTodoList(id: number) {
  return request<Api.Common.ApiNull, 'json'>({
    url: coreBaseUrl(`/todo/${id}`),
    method: 'delete'
  });
}
// ***** 承办人确认 *****
export function fetchPutTodoProcess(id: number, data: Api.Kip.TaskCenterPutProcessParams) {
  return request<Api.Common.ApiNull, 'json'>({
    url: coreBaseUrl(`/todo/${id}/process`),
    method: 'put',
    data
  });
}
