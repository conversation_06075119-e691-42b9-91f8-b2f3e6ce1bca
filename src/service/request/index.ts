import { BACKEND_ERROR_CODE, createFlatRequest, createRequest } from '@sa/axios';
import type { AxiosResponse } from 'axios';
import { $t } from '@/locales';
import { useAuthStore } from '@/store/modules/auth';
import { getServiceBaseURL } from '@/utils/service';
import { localStg } from '@/utils/storage';
import { useLoadingStore } from '@/store/modules/app/loading';
import { getAuthorization, handleExpiredRequest, showErrorMsg } from './shared';
import type { RequestInstanceState } from './type';

const isHttpProxy = import.meta.env.DEV && import.meta.env.VITE_HTTP_PROXY === 'Y';
const { baseURL, otherBaseURL } = getServiceBaseURL(import.meta.env, isHttpProxy);
export const request = createFlatRequest<App.Service.Response, RequestInstanceState>(
  {
    baseURL,
    headers: {
      timeout: 10000
    }
  },
  {
    async onRequest(config) {
      const loadingStore = useLoadingStore();
      loadingStore.setLoading(true);
      const Authorization = getAuthorization();
      Object.assign(config.headers, { Authorization });
      return config;
    },
    isBackendSuccess(response) {
      return String(response.data.code) === import.meta.env.VITE_SERVICE_SUCCESS_CODE;
    },
    async onBackendFail(response, instance) {
      const loadingStore = useLoadingStore();
      const authStore = useAuthStore();
      const responseCode = String(response.data.code);

      function handleLogout() {
        window.$modal?.destroyAll();
        authStore.resetStore();
      }

      function logoutAndCleanup() {
        handleLogout();
        window.removeEventListener('beforeunload', handleLogout);
        request.state.errMsgStack = request.state.errMsgStack.filter(msg => msg !== response.data.msg);
      }

      const logoutCodes = import.meta.env.VITE_SERVICE_LOGOUT_CODES?.split(',') || [];
      if (logoutCodes.includes(responseCode)) {
        handleLogout();
        return null;
      }

      const modalLogoutCodes = import.meta.env.VITE_SERVICE_MODAL_LOGOUT_CODES?.split(',') || [];
      if (modalLogoutCodes.includes(responseCode) && !request.state.errMsgStack?.includes(response.data.msg)) {
        request.state.errMsgStack = [...(request.state.errMsgStack || []), response.data.msg];
        window.addEventListener('beforeunload', handleLogout);
        loadingStore.setLoading(false);

        window.$dialog?.error({
          title: $t('common.error'),
          content: response.data.msg,
          positiveText: $t('common.confirm'),
          maskClosable: false,
          closeOnEsc: false,
          onPositiveClick() {
            logoutAndCleanup();
          },
          onClose() {
            logoutAndCleanup();
          }
        });

        return null;
      }

      const expiredTokenCodes = import.meta.env.VITE_SERVICE_EXPIRED_TOKEN_CODES?.split(',') || [];
      if (expiredTokenCodes.includes(responseCode)) {
        const success = await handleExpiredRequest(request.state);
        if (success) {
          const Authorization = getAuthorization();
          Object.assign(response.config.headers, { Authorization });

          return instance.request(response.config) as Promise<AxiosResponse>;
        }
      }

      return null;
    },
    transformBackendResponse(response) {
      const loadingStore = useLoadingStore();
      setTimeout(() => {
        loadingStore.setLoading(false);
      }, 200);
      return response.data.data;
    },
    onError(error) {
      const loadingStore = useLoadingStore();
      setTimeout(() => {
        loadingStore.setLoading(false);
      }, 200);
      let message = error.response?.data.msg || error.message;
      let backendErrorCode = '';

      if (error.code === BACKEND_ERROR_CODE) {
        message = error.response?.data?.msg || message;
        backendErrorCode = String(error.response?.data?.code || '');
      }

      const modalLogoutCodes = import.meta.env.VITE_SERVICE_MODAL_LOGOUT_CODES?.split(',') || [];
      if (modalLogoutCodes.includes(backendErrorCode)) {
        return;
      }

      const expiredTokenCodes = import.meta.env.VITE_SERVICE_EXPIRED_TOKEN_CODES?.split(',') || [];
      if (expiredTokenCodes.includes(backendErrorCode)) {
        return;
      }

      if (error.status === 422) {
        interface FormValidationErrors {
          field: string;
          msg: string;
        }

        const errors = error.response?.data.data as FormValidationErrors[];
        message = errors.map(err => `${err.field}: ${err.msg}`).join(',');
        showErrorMsg(request.state, message, true);
        return;
      }

      showErrorMsg(request.state, message);
    }
  }
);

export const demoRequest = createRequest<App.Service.DemoResponse>(
  {
    baseURL: otherBaseURL.demo
  },
  {
    async onRequest(config) {
      const { headers } = config;

      // set token
      const token = localStg.get('accessToken');
      const Authorization = token ? `Bearer ${token}` : null;
      Object.assign(headers, { Authorization });

      return config;
    },
    isBackendSuccess(response) {
      // when the backend response code is "200", it means the request is success
      // you can change this logic by yourself
      return response.data.status === '200';
    },
    async onBackendFail(_response) {
      // when the backend response code is not "200", it means the request is fail
      // for example: the token is expired, refresh token and retry request
    },
    transformBackendResponse(response) {
      return response.data.result;
    },
    onError(error) {
      // when the request is fail, you can show error message

      let message = error.message;

      // show backend error message
      if (error.code === BACKEND_ERROR_CODE) {
        message = error.response?.data?.message || message;
      }

      window.$message?.error(message);
    }
  }
);

/**
 * 拼接请求地址
 *
 * @param url
 * @param subUrl subUrl 需要以 / 开头
 * @returns string
 */
const buildBaseUrl = (url: string, subUrl: string): string => {
  return `/admin-api/${url}${subUrl}`;
};

/**
 * 默认请求地址
 *
 * @param subUrl subUrl 需要以 / 开头
 * @returns string
 */
export const adminBaseUrl = (subUrl: string): string => {
  return `/admin-api${subUrl}`;
};
/**
 * 公共请求Api
 *
 * @param subUrl subUrl 需要以 / 开头
 * @returns string
 */
export const appApiBaseUrl = (subUrl: string): string => {
  return `/app-api${subUrl}`;
};

/**
 * crm请求地址
 *
 * @param subUrl subUrl 需要以 / 开头
 * @returns string
 */
export const crmBaseUrl = (subUrl: string): string => {
  return buildBaseUrl('crm', subUrl);
};

/**
 * 关键事项请求地址
 *
 * @param subUrl subUrl 需要以 / 开头
 * @returns string
 */
export const coreBaseUrl = (subUrl: string): string => {
  return buildBaseUrl('core-minder', subUrl);
};
/**
 * 人事请求地址
 *
 * @param subUrl subUrl 需要以 / 开头
 * @returns string
 */
export const hrmBaseUrl = (subUrl: string): string => {
  return buildBaseUrl('hrm-server', subUrl);
};
