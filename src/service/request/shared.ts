import type { MessageOptions } from 'naive-ui/es/message/src/types';

import { fetchRefreshToken } from '../api';

import type { RequestInstanceState } from './type';

import { useAuthStore } from '@/store/modules/auth';
import { localStg } from '@/utils/storage';

export function getAuthorization() {
  const token = localStg.get('accessToken');

  return token ? `Bearer ${token}` : null;
}

/** refresh token */
async function handleRefreshToken() {
  const { resetStore } = useAuthStore();

  const rToken = localStg.get('refreshToken') || '';
  const { error, data } = await fetchRefreshToken(rToken);
  if (!error) {
    localStg.set('accessToken', data.accessToken);
    localStg.set('refreshToken', data.refreshToken);
    return true;
  }

  resetStore();

  return false;
}

export async function handleExpiredRequest(state: RequestInstanceState) {
  if (!state.refreshTokenFn) {
    state.refreshTokenFn = handleRefreshToken();
  }

  const success = await state.refreshTokenFn;

  setTimeout(() => {
    state.refreshTokenFn = null;
  }, 1000);

  return success;
}

export function showErrorMsg(state: RequestInstanceState, message: string, warning_msg = false) {
  if (!state.errMsgStack?.length) {
    state.errMsgStack = [];
  }

  const isExist = state.errMsgStack.includes(message);

  if (!isExist) {
    state.errMsgStack.push(message);
    const options: MessageOptions = {
      onLeave: () => {
        state.errMsgStack = state.errMsgStack.filter(msg => msg !== message);

        setTimeout(() => {
          state.errMsgStack = [];
        }, 5000);
      },
      duration: warning_msg ? 5000 : 0,
      closable: !warning_msg
    };

    if (warning_msg) {
      window.$message?.warning(message, options);
    } else {
      window.$message?.error(message, options);
    }
  }
}
