import { useDictStore } from '@/store/modules/dict';
export interface DictItem<T = any> {
  key: string | number;
  value: string;
  data?: T;
}
type DictMap = Record<string, DictItem[]>;

export function useDict<const K extends string>(keys: readonly K[]) {
  const dictStore = useDictStore();
  const dictMap = shallowReactive<DictMap>({});

  const loadDict = async () => {
    const missing = keys.filter(key => !dictStore.hasDict(key));
    if (missing.length > 0) await dictStore.fetchDicts(missing);

    keys.forEach(key => {
      dictMap[key] = dictStore.getDict(key);
    });
  };

  watchEffect(() => {
    loadDict();
  });

  const getOptions = (code: string) =>
    computed(() => {
      return (dictMap[code] || []).map(item => ({
        label: item.value,
        value: item.key,
        data: item.data
      }));
    });

  const getRecord = (code: string) =>
    computed(() => {
      return (dictMap[code] || []).reduce(
        (acc, cur) => {
          acc[cur.key] = cur.value;
          return acc;
        },
        {} as Record<string | number, string>
      );
    });

  const dicts = {} as Record<K, ReturnType<typeof getOptions>>;
  keys.forEach(key => {
    dicts[key] = getOptions(key);
  });

  return {
    dictMap,
    getOptions,
    getRecord,
    ...dicts
  };
}
