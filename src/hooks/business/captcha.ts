import { computed, onMounted, onUnmounted } from 'vue';
import { useCountDown, useLoading } from '@sa/hooks';
import { $t } from '@/locales';
import { fetchSendCaptcha } from '@/service/api';
import { useAuthStore } from '@/store/modules/auth';

interface CaptchaStorageData {
  endTime: number;
  phone: string;
}

export function useCaptcha(storageKey = 'captcha_countdown') {
  const { loading, startLoading, endLoading } = useLoading();
  const { count, start, stop, isCounting } = useCountDown(60);
  const authStore = useAuthStore();

  let intervalId: number | null = null;

  const label = computed(() => {
    let text = $t('page.login.codeLogin.getCode');

    const countingLabel = $t('page.login.codeLogin.reGetCode', { time: count.value });

    if (loading.value) {
      text = '';
    }

    if (isCounting.value) {
      text = countingLabel;
    }

    return text;
  });

  // 保存倒计时状态到localStorage
  function saveCaptchaState(phone: string) {
    const endTime = Date.now() + 60 * 1000; // 60秒后结束
    const data: CaptchaStorageData = {
      endTime,
      phone
    };
    localStorage.setItem(storageKey, JSON.stringify(data));
  }

  // 从localStorage恢复倒计时状态
  function restoreCaptchaState() {
    try {
      const stored = localStorage.getItem(storageKey);
      if (!stored) return false;

      const data: CaptchaStorageData = JSON.parse(stored);
      const now = Date.now();

      if (now >= data.endTime) {
        // 倒计时已结束，清除存储
        localStorage.removeItem(storageKey);
        return false;
      }

      // 计算剩余时间
      const remainingTime = Math.ceil((data.endTime - now) / 1000);
      if (remainingTime > 0) {
        start(remainingTime);
        return true;
      }
    } catch (error) {
      console.error('Failed to restore captcha state:', error);
      localStorage.removeItem(storageKey);
    }
    return false;
  }

  // 清除倒计时状态
  function clearCaptchaState() {
    localStorage.removeItem(storageKey);
    stop();
  }

  async function getCaptcha() {
    if (loading.value || isCounting.value) {
      return;
    }

    startLoading();

    try {
      const { error } = await fetchSendCaptcha({ id: authStore.userInfo.id });
      if (error) {
        return;
      }

      window.$message?.success?.($t('page.login.codeLogin.sendCodeSuccess'));

      // 开始倒计时并保存状态
      start();
      saveCaptchaState(Math.random(9999999).toString());
    } catch (error) {
      console.error('Failed to send captcha:', error);
    } finally {
      endLoading();
    }
  }

  // 监听倒计时结束，自动清除存储
  function setupCountdownWatcher() {
    intervalId = window.setInterval(() => {
      if (!isCounting.value) {
        clearCaptchaState();
        if (intervalId) {
          clearInterval(intervalId);
          intervalId = null;
        }
      }
    }, 1000);
  }

  // 组件挂载时恢复状态
  onMounted(() => {
    restoreCaptchaState();
    setupCountdownWatcher();
  });

  // 组件卸载时清理定时器
  onUnmounted(() => {
    if (intervalId) {
      clearInterval(intervalId);
      intervalId = null;
    }
  });

  return {
    label,
    start,
    stop,
    isCounting,
    loading,
    getCaptcha,
    clearCaptchaState,
    restoreCaptchaState
  };
}
