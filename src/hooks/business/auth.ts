import { useAuthStore } from '@/store/modules/auth';

export function useAuth() {
  const authStore = useAuthStore();

  function hasAuth(codes: string | string[]) {
    if (!authStore.isLogin) {
      return false;
    }

    if (typeof codes === 'string') {
      return authStore.permissions?.includes(codes);
    }

    return codes.some(code => authStore.permissions?.includes(code));
  }

  return {
    hasAuth
  };
}
