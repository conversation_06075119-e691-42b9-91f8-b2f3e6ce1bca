import { useUrlSearchParams } from '@vueuse/core';
import { ref, watch } from 'vue';

export interface UseUrlSearchSyncOptions {
  /** 是否立即同步，默认 true */
  immediate?: boolean;
  /** 需要排除的参数键名，默认排除分页参数 */
  excludeKeys?: string[];
  /** 是否移除空值，默认 true */
  removeNullishValues?: boolean;
  /** 是否移除假值，默认 false */
  removeFalsyValues?: boolean;
  /** 是否启用监听器，默认 true */
  enableWatchers?: boolean;
  /** 防抖延迟时间（毫秒），默认 300 */
  debounceDelay?: number;
}

/**
 * URL搜索参数同步hook
 * 用于将搜索参数与URL查询参数进行双向同步
 */
export function useUrlSearchSync<T extends Record<string, any>>(
  searchParams: T,
  options: UseUrlSearchSyncOptions = {}
) {
  const {
    immediate = true,
    excludeKeys = ['pageNo', 'pageSize'],
    removeNullishValues = true,
    removeFalsyValues = false,
    enableWatchers = true,
    debounceDelay = 300
  } = options;

  // 使用 VueUse 的 URL 参数管理
  const urlParams = useUrlSearchParams('history', {
    removeNullishValues,
    removeFalsyValues,
    write: true
  });

  // 控制监听器是否启用的标志
  const isWatchingEnabled = ref(true);

  // 防抖函数
  function debounce<F extends (...args: any[]) => any>(func: F, delay: number): F {
    let timeoutId: ReturnType<typeof setTimeout>;
    return ((...args: any[]) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func.apply(null, args), delay);
    }) as F;
  }

  /**
   * 从URL恢复搜索参数
   */
  function restoreFromUrl() {
    Object.keys(searchParams).forEach(key => {
      if (excludeKeys.includes(key)) return;

      if (urlParams[key] !== undefined) {
        let value = urlParams[key];

        // 类型转换处理
        if (typeof value === 'string') {
          // 尝试解析JSON数组
           if (value.startsWith('[') && value.endsWith(']')) {
            try {
              value = JSON.parse(value);
            } catch {
              // 解析失败，保持原值
            }
          }
        }

        (searchParams as any)[key] = value;
      }
    });
  }

  /**
   * 将搜索参数同步到URL
   */
  const syncToUrl = debounce(() => {
    Object.keys(searchParams).forEach(key => {
      if (excludeKeys.includes(key)) return;

      const value = (searchParams as any)[key];
      if (value !== undefined && value !== null && value !== '') {
        // 序列化复杂类型
        if (typeof value === 'object') {
          urlParams[key] = JSON.stringify(value);
        } else {
          urlParams[key] = String(value);
        }
      } else {
        delete urlParams[key];
      }
    });
  }, debounceDelay);

  /**
   * 清除URL参数
   */
  function clearUrlParams() {
    Object.keys(searchParams).forEach(key => {
      if (excludeKeys.includes(key)) return;
      delete urlParams[key];
    });
  }

  /**
   * 清除所有URL参数（包括排除的参数）
   */
  function clearAllUrlParams() {
    Object.keys(searchParams).forEach(key => {
      delete urlParams[key];
    });
  }

  /**
   * 暂时禁用监听器
   */
  function disableSync() {
    isWatchingEnabled.value = false;
  }

  /**
   * 重新启用监听器
   */
  function enableSync() {
    isWatchingEnabled.value = true;
  }

  /**
   * 获取初始化的API参数（用于useTable等）
   */
  function getInitialApiParams(defaultParams: Partial<T>): Partial<T> {
    const result = { ...defaultParams };

    Object.keys(defaultParams).forEach(key => {
      if (excludeKeys.includes(key)) return;

      const urlValue = urlParams[key];
      if (urlValue !== undefined) {
        let value: any = urlValue;

        // 类型转换
        if (typeof value === 'string') {
          // 数字转换
          if (/^\d+$/.test(value)) {
            value = Number(value);
          }
          // JSON数组转换
          else if (value.startsWith('[') && value.endsWith(']')) {
            try {
              value = JSON.parse(value);
            } catch {
              // 解析失败，保持原值
            }
          }
        }

        (result as any)[key] = value;
      }
    });

    return result;
  }

  /**
   * 重置搜索参数并清除URL
   */
  async function resetWithUrl(resetValues: Partial<T>, updateSearchParams: (params: Partial<T>) => void) {
    // 暂时禁用监听器
    isWatchingEnabled.value = false;

    // 清除URL参数
    clearAllUrlParams();

    // 等待URL清除完成
    await nextTick();

    // 更新搜索参数
    updateSearchParams(resetValues);

    // 等待搜索参数更新完成
    await nextTick();

    // 重新启用监听器
    isWatchingEnabled.value = true;
  }

  // 立即恢复URL参数
  if (immediate) {
    restoreFromUrl();
  }

  // 只有启用监听器时才设置watch
  if (enableWatchers) {
    // 监听搜索参数变化，同步到URL
    watch(
      () => searchParams,
      () => {
        if (isWatchingEnabled.value) {
          syncToUrl();
        }
      },
      { deep: true, immediate: false }
    );

    // 监听URL参数变化，同步到搜索参数
    watch(
      () => urlParams,
      () => {
        if (isWatchingEnabled.value) {
          restoreFromUrl();
        }
      },
      { deep: true }
    );
  }

  return {
    /** URL参数对象 */
    urlParams,
    /** 从URL恢复搜索参数 */
    restoreFromUrl,
    /** 将搜索参数同步到URL */
    syncToUrl,
    /** 清除URL参数（排除excludeKeys） */
    clearUrlParams,
    /** 清除所有URL参数 */
    clearAllUrlParams,
    /** 获取初始化的API参数 */
    getInitialApiParams,
    /** 暂时禁用同步 */
    disableSync,
    /** 重新启用同步 */
    enableSync,
    /** 重置搜索参数并清除URL */
    resetWithUrl
  };
}
