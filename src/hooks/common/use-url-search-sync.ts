import { useUrlSearchParams } from '@vueuse/core';
import { watch } from 'vue';

export interface UseUrlSearchSyncOptions {
  /** 是否立即同步，默认 true */
  immediate?: boolean;
  /** 需要排除的参数键名，默认为空数组 */
  excludeKeys?: string[];
  /** 是否移除空值，默认 true */
  removeNullishValues?: boolean;
  /** 是否移除假值，默认 false */
  removeFalsyValues?: boolean;
  /** 是否启用监听器，默认 true */
  enableWatchers?: boolean;
}

/**
 * URL搜索参数同步hook
 * 用于将搜索参数与URL查询参数进行双向同步
 */
export function useUrlSearchSync<T extends Record<string, any>>(
  searchParams: T,
  options: UseUrlSearchSyncOptions = {}
) {
  const {
    immediate = true,
    excludeKeys = [],
    removeNullishValues = true,
    removeFalsyValues = false,
    enableWatchers = true
  } = options;

  // 使用 VueUse 的 URL 参数管理
  const urlParams = useUrlSearchParams('history', {
    removeNullishValues,
    removeFalsyValues,
    write: true
  });

  /**
   * 从URL恢复搜索参数
   */
  function restoreFromUrl() {
    Object.keys(searchParams).forEach(key => {
      if (excludeKeys.includes(key)) return;

      if (urlParams[key] !== undefined) {
        let value = urlParams[key];

        // 类型转换处理
        // if (typeof value === 'string') {
        //   // 尝试解析数字
        //   if (/^\d+$/.test(value)) {
        //     value = Number(value);
        //   }
        //   // 尝试解析JSON数组
        //   else if (value.startsWith('[') && value.endsWith(']')) {
        //     try {
        //       value = JSON.parse(value);
        //     } catch {
        //       // 解析失败，保持原值
        //     }
        //   }
        // }

        (searchParams as any)[key] = value;
      }
    });
  }

  /**
   * 将搜索参数同步到URL
   */
  function syncToUrl() {
    Object.keys(searchParams).forEach(key => {
      if (excludeKeys.includes(key)) return;

      const value = (searchParams as any)[key];
      if (value !== undefined && value !== null && value !== '') {
        urlParams[key] = value;
      } else {
        delete urlParams[key];
      }
    });
  }

  /**
   * 清除URL参数
   */
  function clearUrlParams() {
    Object.keys(searchParams).forEach(key => {
      if (excludeKeys.includes(key)) return;
      delete urlParams[key];
    });
  }

  /**
   * 清除所有URL参数（包括排除的参数）
   */
  function clearAllUrlParams() {
    Object.keys(searchParams).forEach(key => {
      delete urlParams[key];
    });
  }

  /**
   * 获取初始化的API参数（用于useTable等）
   */
  function getInitialApiParams(defaultParams: Partial<T>): Partial<T> {
    const result = { ...defaultParams };

    Object.keys(defaultParams).forEach(key => {
      if (excludeKeys.includes(key)) return;

      const urlValue = urlParams[key];
      if (urlValue !== undefined) {
        let value = urlValue;

        // 类型转换
        if (typeof value === 'string') {
          // 数字转换
          if (/^\d+$/.test(value)) {
            value = Number(value);
          }
          // JSON数组转换
          else if (value.startsWith('[') && value.endsWith(']')) {
            try {
              value = JSON.parse(value);
            } catch {
              // 解析失败，保持原值
            }
          }
        }

        (result as any)[key] = value;
      }
    });

    return result;
  }

  // 立即恢复URL参数
  if (immediate) {
    restoreFromUrl();
  }

  // 只有启用监听器时才设置watch
  if (enableWatchers) {
    // 监听搜索参数变化，同步到URL
    watch(
      () => searchParams,
      () => {
        syncToUrl();
      },
      { deep: true, immediate: false }
    );

    // 监听URL参数变化，同步到搜索参数
    watch(
      () => urlParams,
      () => {
        restoreFromUrl();
      },
      { deep: true }
    );
  }

  return {
    /** URL参数对象 */
    urlParams,
    /** 从URL恢复搜索参数 */
    restoreFromUrl,
    /** 将搜索参数同步到URL */
    syncToUrl,
    /** 清除URL参数（排除excludeKeys） */
    clearUrlParams,
    /** 清除所有URL参数 */
    clearAllUrlParams,
    /** 获取初始化的API参数 */
    getInitialApiParams
  };
}
