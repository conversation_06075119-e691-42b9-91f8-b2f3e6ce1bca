import type { ProSearchFormColumns } from 'pro-naive-ui';
import { createProSearchForm } from 'pro-naive-ui';
import { ref } from 'vue';

import { getAllPositionOptions } from '@/utils/async-functions';

interface UseEmployeeSearchFormOptions<T> {
  searchParams: Record<string, any>;
  updateSearchParams: (params: Record<string, any>) => void;
  getDataByPage: () => void;
  onReset?: (form?: any) => void; // 自定义重置函数，传入表单实例
  extendColumns?: [];
  shiftIndex?: number;
  newColumns?: ProSearchFormColumns<T>;
}

export function useEmployeeSearchForm<T = any>(options: UseEmployeeSearchFormOptions<T>) {
  const {
    searchParams,
    updateSearchParams,
    getDataByPage,
    onReset,
    extendColumns = [],
    shiftIndex = null,
    newColumns = []
  } = options;

  let defaultColumns: ProSearchFormColumns<T> = [
    {
      title: '员工姓名',
      path: 'name'
    },
    {
      title: '员工工号',
      path: 'jobNumber'
    },
    {
      title: '员工职位',
      path: 'positionId',
      field: 'select-with-search',
      fieldProps: {
        apiFunc: getAllPositionOptions,
        selectedOptions: [],
        pageSize: 0,
        placeholder: '职位'
      }
    },
    {
      title: '所属部门',
      path: 'deptId',
      field: 'dept-tree-select',
      fieldProps: {
        placeholder: '请选择部门'
      }
    }
  ];

  if (shiftIndex !== null) {
    defaultColumns.splice(shiftIndex, 0, ...extendColumns);
  } else {
    defaultColumns.push(...extendColumns);
  }
  if (newColumns.length > 0) {
    defaultColumns = [...newColumns];
  }
  const searchColumns = ref<ProSearchFormColumns<T>>(defaultColumns);

  const form = createProSearchForm({
    defaultCollapsed: true,
    initialValues: searchParams,
    onReset: () => {
      if (onReset) {
        // 使用自定义重置函数，传入表单实例
        onReset(form);
      } else {
        // 默认重置逻辑
        updateSearchParams({
          pageNo: 1,
          pageSize: searchParams.pageSize || 10,
          positionId: null,
          deptId: null,
          jobNumber: null,
          name: null,
          subStatus: null,
          positionProperty: null
        });
      }
      getDataByPage();
    },
    onSubmit: () => {
      updateSearchParams({
        pageNo: 1,
        ...form.fieldsValue.value
      });
      getDataByPage();
    }
  });

  return {
    form,
    searchColumns
  };
}
