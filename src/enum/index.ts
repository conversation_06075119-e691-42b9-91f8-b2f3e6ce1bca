import type { SelectBaseOption } from 'naive-ui/es/select/src/interface';

export enum SetupStoreId {
  App = 'app-store',
  Theme = 'theme-store',
  Auth = 'auth-store',
  Route = 'route-store',
  Tab = 'tab-store',
  Dict = 'dict-store'
}
export enum LoginType {
  Dingtalk = 20
}
// 枚举
export interface EnumOption extends SelectBaseOption {
  label: string;
  value: number | string;
  type?: NaiveUI.ThemeColor;
}

export function getEnumOptionInfo(
  enumOptions: EnumOption[],
  value: number | string
): Omit<EnumOption, 'value'> | undefined {
  const one = enumOptions.find(option => option.value === value);

  return one ?? undefined;
}

// ******* 业务类型 *******
enum BusinessType {
  Telemarketing = 'telemarketing',
  CustomerService = 'customer_service',
  PhoneCollection = 'phone_collection'
}

export const businessTypeOptions: EnumOption[] = [
  { label: '电销', value: BusinessType.Telemarketing, type: 'info' },
  { label: '客服', value: BusinessType.CustomerService, type: 'info' },
  { label: '电催', value: BusinessType.PhoneCollection, type: 'info' }
];

// ******* 中标率 *******
enum WinningProbability {
  Level1 = '0',
  Level2 = '1',
  Level3 = '2',
  Level4 = '3'
}

export const winningProbabilityOptions: EnumOption[] = [
  { label: '30%以下', value: WinningProbability.Level1, type: 'info' },
  { label: '30%', value: WinningProbability.Level2, type: 'info' },
  { label: '50%', value: WinningProbability.Level3, type: 'info' },
  { label: '70%', value: WinningProbability.Level4, type: 'info' }
];

// ******* 用户性别 *******
enum UserGender {
  Unknown,
  Female,
  Male
}

export const userGenderOptions: EnumOption[] = [
  { label: '未知', value: UserGender.Unknown, type: 'info' },
  { label: '女', value: UserGender.Female, type: 'warning' },
  { label: '男', value: UserGender.Male, type: 'warning' }
];

// ******* 用户状态 *******
enum UserStatus {
  Disable,
  Enable
}

export const userStatusOptions: EnumOption[] = [
  { label: '启用', value: UserStatus.Enable, type: 'success' },
  { label: '禁用', value: UserStatus.Disable, type: 'warning' }
];

// ******* 项目状态 *******
export enum ProjectStatus {
  Clue = 0,
  Launch = 3,
  FollowUp = 2,
  Bid = 4,
  Win = 5,
  Start = 6,
  End = 7
}

export const projectStatusOptions: EnumOption[] = [
  { label: '线索', value: ProjectStatus.Clue, type: 'info' },
  { label: '立项', value: ProjectStatus.Launch, type: 'warning' },
  { label: '拜访中', value: ProjectStatus.FollowUp, type: 'warning' },
  { label: '竞标', value: ProjectStatus.Bid, type: 'warning' },
  { label: '已中标', value: ProjectStatus.Win, type: 'warning' },
  { label: '项目启动', value: ProjectStatus.Start, type: 'warning' },
  { label: '结束', value: ProjectStatus.End, type: 'warning' }
];

// ******* 评审状态 *******
export enum ReviewStatus {
  Reviewing = 0,
  Passed = 1,
  Failed = 2
}

export const reviewStatusOptions: EnumOption[] = [
  { label: '评审中', value: ReviewStatus.Reviewing, type: 'info' },
  { label: '评审通过', value: ReviewStatus.Passed, type: 'success' },
  { label: '评审未通过', value: ReviewStatus.Failed, type: 'error' }
];

// ******* 项目里程碑 *******
export enum ProjectMarker {
  Approval = 1,
  CompetitiveBidding = 2,
  WinTheBidding = 3,
  Start = 4
}

export const projectMarkerOptions: EnumOption[] = [
  { label: '立项', value: ProjectMarker.Approval, type: 'info' },
  { label: '竟标', value: ProjectMarker.CompetitiveBidding, type: 'info' },
  { label: '中标', value: ProjectMarker.WinTheBidding, type: 'info' },
  { label: '项目启动', value: ProjectMarker.Start, type: 'info' }
];

// ******* 拜访计划状态 *******
export enum PlanStatus {
  WaitVisit = 0,
  Visited = 1,
  Canceled = 2
}

export const planStatusOptions: EnumOption[] = [
  { label: '待拜访', value: PlanStatus.WaitVisit, type: 'info' },
  { label: '已拜访', value: PlanStatus.Visited, type: 'success' },
  { label: '已取消', value: PlanStatus.Canceled, type: 'error' }
];

// ******* bsc层面 *******
export enum BscStatus {
  Financial = 1, // 财务层面
  Customer = 2, // 客户层面
  Internal = 3, // 内部流程层面
  Learn = 4 // 学习成长层面
}

export const BscStatusOptions: EnumOption[] = [
  { label: '财务层面', value: BscStatus.Financial },
  { label: '客户层面', value: BscStatus.Customer },
  { label: '内部流程层面', value: BscStatus.Internal },
  { label: '学习成长层面', value: BscStatus.Learn }
];
export enum StatisticCycleStatus {
  Week = 1, // 周度
  Month = 2 // 月度
}

export const StatisticCycleOptions: EnumOption[] = [
  { label: '周度', value: StatisticCycleStatus.Week },
  { label: '月度', value: StatisticCycleStatus.Month }
];

export const BscOptions: EnumOption[] = [
  { label: '万', value: 1 },
  { label: '%', value: 2 },
  { label: '人', value: 3 }
];
// 是否拆解关键事项
// export enum IsSplitKeyIssues {
//   Yes = true,
//   No = false
// }
// export const IsSplitKeyIssuesOptions: EnumOption[] = [
//   { label: '是', value: IsSplitKeyIssues.Yes },
//   { label: '否', value: IsSplitKeyIssues.No }
// ];

// 是否达成预期结果
export enum IsExceptedResult {
  Incomplete = 1,
  Yes = 2,
  No = 3
}

export const IsExceptedResultOptions: EnumOption[] = [
  { label: '进行中', value: IsExceptedResult.Incomplete },
  { label: '已完成达成预期', value: IsExceptedResult.Yes },
  { label: '已完成未达预期', value: IsExceptedResult.No }
];

// 银行类型
export enum BankType {
  Headquarters = 1, // 总行
  Branches = 2, // 分行
  CityCommercialBank = 3, // 城商行
  Other = 4 // 其他
}

export const BankTypeOptions: EnumOption[] = [
  { label: '总行', value: BankType.Headquarters, type: 'info' },
  { label: '分行', value: BankType.Branches, type: 'success' },
  { label: '城商行', value: BankType.CityCommercialBank, type: 'error' },
  { label: '其他', value: BankType.Other, type: 'warning' }
];
export enum HrmStepType {
  BaseInfo = 1, // 身份与基础信息
  Education = 2, // 教育背景
  Work = 3, // 职业与工作经历
  Skill = 4, // 技能与资质证明
  Law = 5, // 法律与合规文件
  Finance = 6, // 财务与银行信息
  Family = 7 // 家庭信息
}
export const HrmStepTypeOptions: EnumOption[] = [
  { label: '身份与基础信息', value: HrmStepType.BaseInfo },
  { label: '教育背景', value: HrmStepType.Education },
  { label: '职业与工作经历', value: HrmStepType.Work },
  { label: '技能与资质证明', value: HrmStepType.Skill },
  { label: '法律与合规文件', value: HrmStepType.Law },
  { label: '财务与银行信息', value: HrmStepType.Finance },
  { label: '家庭信息', value: HrmStepType.Family }
];
// 员工状态
export enum EmployeeStatus {
  EntryPending = 1, // 待入职
  Probation = 2, // 试用期
  OnJob = 3, // 在职
  Leaving = 4, // 离职中
  Left = 5, // 离职
  Abnormal = 6, // 异常
  Retired = 7 // 退休
}
// 异常状态
export enum AbnormalStatus {
  JoinDirectly = 1, // 直接加入钉钉
  ExitDingTalk = 2, // 主动退出钉钉组织
  AttendanceException = 3, // 考勤连续异常
  GiveUpEntry = 4 // 放弃入职
}
// 子状态（如调岗）
export enum EmployeeSubStatus {
  Transfer = 2 // 调岗
}
export enum PreEntryStatus {
  Inputting = 1, // 个人资料录入中
  Reviewing = 2, // 个人资料审核中
  WaitingForSign = 3, // 待签署合同
  Rejected = 4, // 个人资料审核不通过
  PendingContracts = 5, // 待发起合同
  WaitingForContract = 6 // 已完成签署
}

// 枚举对应的选项（label + value）
export const PreEntryStatusOptions: EnumOption[] = [
  { label: '个人资料录入中', value: PreEntryStatus.Inputting, type: 'info' },
  { label: '个人资料审核中', value: PreEntryStatus.Reviewing, type: 'warning' },
  { label: '待签署合同', value: PreEntryStatus.WaitingForSign, type: 'success' },
  { label: '个人资料审核不通过', value: PreEntryStatus.Rejected, type: 'error' },
  { label: '待发起合同', value: PreEntryStatus.PendingContracts, type: 'success' },
  { label: '已完成签署', value: PreEntryStatus.WaitingForContract, type: 'success' }
];
// 晋升状态枚举
export enum PromotionStatus {
  Approved = 7, // 晋升审批通过
  Rejected = 8, // 晋升审批不通过
  Withdrawn = 9 // 晋升审批撤回
}

// 枚举对应的选项（label + value）
export const PromotionStatusOptions: EnumOption[] = [
  { label: '晋升审批通过', value: PromotionStatus.Approved, type: 'success' },
  { label: '晋升审批不通过', value: PromotionStatus.Rejected, type: 'error' },
  { label: '晋升审批撤回', value: PromotionStatus.Withdrawn, type: 'warning' }
];

export enum TransferPostStatus {
  Approved = 3, // 转岗审批通过
  Rejected = 4, // 转岗审批不通过
  Withdrawn = 5 // 转岗审批撤回
}

// 枚举对应的选项（label + value）
export const TransferPostStatusOptions: EnumOption[] = [
  { label: '转岗审批通过', value: TransferPostStatus.Approved, type: 'success' },
  { label: '转岗审批不通过', value: TransferPostStatus.Rejected, type: 'error' },
  { label: '转岗审批撤回', value: TransferPostStatus.Withdrawn, type: 'warning' }
];
export enum DataScopeType {
  All = 1, // 全部数据权限
  Custom = 2, // 指定部门数据权限
  DeptOnly = 3, // 本部门数据权限
  DeptAndBelow = 4, // 本部门及以下数据权限
  SelfOnly = 5 // 仅本人数据权限
}

export const DataScopeTypeOptions: EnumOption[] = [
  { label: '全部数据权限', value: DataScopeType.All, type: 'info' },
  { label: '指定部门数据权限', value: DataScopeType.Custom, type: 'success' },
  { label: '本部门数据权限', value: DataScopeType.DeptOnly, type: 'warning' },
  { label: '本部门及以下数据权限', value: DataScopeType.DeptAndBelow, type: 'error' },
  { label: '仅本人数据权限', value: DataScopeType.SelfOnly, type: 'default' }
];
export enum RoleType {
  Internal = 1, // 内置
  Customize = 2 // 自定义
}
// 菜单类型
export const RoleTypeOptions: EnumOption[] = [
  { label: '内置', value: RoleType.Internal, type: 'info' },
  { label: '自定义', value: RoleType.Customize, type: 'success' }
];
export enum Initial {
  Password = 'ba59abbe56e057f'
}
// ******* 字典状态 *******
enum DictStatus {
  Open = 0,
  Close = 1
}

export const DictStatusOptions: EnumOption[] = [
  { label: '启用', value: DictStatus.Open, type: 'success' },
  { label: '禁用', value: DictStatus.Close, type: 'warning' }
];

/**
 * id
 *
 * 完成结果状态名称
 *
 * 评审结果状态
 *
 * 评审结果状态名称
 */
export enum CompletionStatus {
  Finished = 'FINISHED',
  Processing = 'PROCESSING',
  Unfinished = 'UNFINISHED'
}
export const CompletionStatusOptions: EnumOption[] = [
  { label: '处理中', value: CompletionStatus.Processing, type: 'warning' },
  { label: '未完成', value: CompletionStatus.Unfinished, type: 'error' },
  { label: '已完成', value: CompletionStatus.Finished, type: 'success' }
];
/** 审核状态 */
export enum ReviewStatusSub {
  Pass = 'PASS',
  Reject = 'REJECT'
}

export enum AccountIdentifierType {
  Mobile = 'MOBILE',
  Email = 'EMAIL',
  EmployeeId = 'EMPLOYEEID',
  EmployeeNo = 'NUMBER'
}

// 枚举选项定义
export const AccountIdentifierTypeOptions: EnumOption[] = [
  { label: '手机号', value: AccountIdentifierType.Mobile },
  { label: '邮箱', value: AccountIdentifierType.Email },
  { label: '员工ID', value: AccountIdentifierType.EmployeeId },
  { label: '员工编号', value: AccountIdentifierType.EmployeeNo }
];
// 合同状态
export enum FileStatus {
  Draft = 'DRAFT',
  Recalled = 'RECALLED',
  Signing = 'SIGNING',
  Rejected = 'REJECTED',
  Complete = 'COMPLETE',
  Expired = 'EXPIRED',
  Filling = 'FILLING',
  Failed = 'FAILED',
  Invaliding = 'INVALIDING',
  Invalided = 'INVALIDED',
  End = 'END'
}
export const FileStatusOptions: EnumOption[] = [
  { label: '草稿', value: FileStatus.Draft },
  { label: '已撤回', value: FileStatus.Recalled },
  { label: '签署中', value: FileStatus.Signing },
  { label: '已退回', value: FileStatus.Rejected },
  { label: '已完成', value: FileStatus.Complete },
  { label: '已过期', value: FileStatus.Expired },
  { label: '拟定中', value: FileStatus.Filling },
  { label: '签署失败', value: FileStatus.Failed },
  { label: '作废中', value: FileStatus.Invaliding },
  { label: '已作废', value: FileStatus.Invalided },
  { label: '强制结束', value: FileStatus.End }
];
export enum PublishStatus {
  WAIT_PUBLISH = 1,
  CANCELLED = 2,
  TESTING = 3,
  PUBLISHED = 4
}
export const PublishStatusOptions: EnumOption[] = [
  { label: '待发布', value: PublishStatus.WAIT_PUBLISH, type: 'info' },
  { label: '已取消', value: PublishStatus.CANCELLED, type: 'error' },
  { label: '测试中', value: PublishStatus.TESTING, type: 'warning' },
  { label: '已发布', value: PublishStatus.PUBLISHED, type: 'success' }
];
export enum NotificationType {
  PreUpdate = 1, // 预更新通知
  Countdown = 2, // 更新倒计时通知
  Completed = 3 // 完成通知
}
export const NotificationTypeOptions: EnumOption[] = [
  { label: '预更新通知', value: NotificationType.PreUpdate },
  { label: '更新倒计时通知', value: NotificationType.Countdown },
  { label: '完成通知', value: NotificationType.Completed }
];
