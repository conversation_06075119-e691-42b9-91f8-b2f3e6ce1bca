<script setup lang="ts">
defineOptions({
  name: 'VersionModel'
});

defineProps<{
  version: Api.System.VersionRespVO;
}>();
</script>

<template>
  <div>
    <div class="mb-4px text-15px font-semibold">版本信息</div>
    <div class="mb-4px ml-5 text-15px">v{{ version.versionNo }}</div>
    <div class="mb-4px text-15px font-semibold">发布时间</div>
    <div class="mb-4px ml-5 text-15px">{{ version.actualReleaseAt }}</div>
    <div class="mb-4px text-15px font-semibold">更新说明</div>
    <div class="ml-5 text-14px text-[#666] leading-6">
      <!-- 先遍历大分类 -->
      <div v-for="(group, groupIndex) in version.detailList" :key="groupIndex" class="mb-3">
        <div class="mb-1 text-14px font-semibold">{{ group.key }}</div>
        <!-- 再遍历每个分类下的列表 -->
        <div v-for="(detail, index) in group.list || []" :key="detail.id" class="mb-1 flex items-start">
          <div class="mx-2 shrink-0 font-semibold">
            {{ index + 1 }}. {{ detail.menuName ? `【${detail.menuName}】` : '' }}
          </div>
          <div class="flex-1 break-words">{{ detail.content }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
