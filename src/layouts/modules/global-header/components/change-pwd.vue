<script setup lang="ts">
import { onMounted, reactive } from 'vue';
import type { FormItemRule, FormRules } from 'naive-ui';
import { MD5 } from 'crypto-js';
import { useNaiveForm } from '@/hooks/common/form';
import { fetchUpdatePassword } from '@/service/api';
import { $t } from '@/locales';
import { useAuthStore } from '@/store/modules/auth';
import { Initial } from '@/enum';
import { useCaptcha } from '@/hooks/business/captcha';
import { localStg } from '@/utils/storage';

const authStore = useAuthStore();
const { formRef, validate, restoreValidation } = useNaiveForm();
const { label, isCounting, loading, getCaptcha } = useCaptcha('captcha');
const model = reactive<Api.System.ChangePasswordParams>({
  newPassword: '',
  oldPassword: authStore.userInfo.isOriginalPassword && localStg.get('loginType') === 'pwd' ? Initial.Password : '',
  confirmPassword: '',
  captcha: []
});
const rules = reactive<FormRules>({
  oldPassword: [
    {
      required: true,
      message: '请输入旧密码',
      trigger: 'blur'
    }
  ],
  captcha: [
    {
      required: true,
      message: '请输入验证码',
      trigger: 'blur',
      type: 'array'
    }
  ],
  newPassword: [
    {
      required: true,
      message: '请输入新密码',
      trigger: 'blur'
    },
    {
      min: 6,
      max: 30,
      message: '密码长度在 6 到 30 个字符',
      trigger: 'blur'
    }
  ],
  confirmPassword: [
    {
      required: true,
      message: '请输入确认密码',
      trigger: 'blur'
    },
    {
      trigger: 'blur',
      validator(_rule: FormItemRule, value: string) {
        if (value !== model.newPassword) {
          return new Error('两次输入密码不一致');
        }
        return true;
      }
    },
    {
      min: 6,
      max: 30,
      message: '密码长度在 6 到 30 个字符',
      trigger: 'blur'
    }
  ]
});
async function handleSubmit() {
  await validate();

  const { oldPassword, newPassword } = model;
  const hashedOld = authStore.userInfo.isOriginalPassword
    ? Initial.Password
    : MD5(oldPassword).toString().slice(10, 25);
  const hashedNew = MD5(newPassword).toString().slice(10, 25);
  if (hashedOld === hashedNew) {
    window?.$message?.error('新密码不能与旧密码相同');
    return false;
  }

  const { error } = await fetchUpdatePassword({
    oldPassword: hashedOld,
    password: hashedNew,
    captcha: model.captcha.join('')
  });

  if (error) return false;

  window?.$message?.success($t('common.modifySuccess'));
  await authStore.resetStore();
  window.$modal?.destroyAll();
  return true;
}
function cancel() {
  if (authStore.userInfo.isOriginalPassword && localStg.get('loginType') === 'pwd') {
    authStore.resetStore();
  }
  window.$modal?.destroyAll();
}
defineExpose({
  handleSubmit
});
onMounted(() => {
  restoreValidation();
});
</script>

<template>
  <NForm ref="formRef" :model="model" :rules="rules">
    <NFormItem v-if="!authStore.userInfo.isOriginalPassword" label="旧密码" path="oldPassword">
      <NInput v-model:value="model.oldPassword" clearable type="password" show-password-toggle />
    </NFormItem>
    <NFormItem label="新密码" path="newPassword">
      <NInput v-model:value="model.newPassword" clearable type="password" show-password-toggle />
    </NFormItem>
    <NFormItem label="确认密码" path="confirmPassword">
      <NInput v-model:value="model.confirmPassword" clearable type="password" show-password-toggle />
    </NFormItem>
    <NFormItem label="验证码" path="captcha">
      <NInputOtp v-model:value="model.captcha" size="small" class="mr-16px" />
      <NButton type="primary" ghost :disabled="isCounting" :loading="loading" @click="getCaptcha">
        {{ label }}
      </NButton>
    </NFormItem>
  </NForm>
  <NSpace :size="16" justify="end">
    <NButton @click="cancel">{{ $t('common.cancel') }}</NButton>
    <NButton type="primary" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
  </NSpace>
</template>

<style scoped></style>
