<script lang="ts" setup>
import type { IDomEditor, IEditorConfig, IToolbarConfig } from '@wangeditor/editor';
import { Boot } from '@wangeditor/editor';
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
import markdownModule from '@wangeditor/plugin-md';
import attachmentModule from '@wangeditor/plugin-upload-attachment';
import { useFieldUtils, useInjectProForm } from 'pro-naive-ui';

import { fetchUploadFile } from '@/service/api/system/upload';
import { isNumber } from '@/utils/common';
import { getFileUrl } from '@/utils/get-file-url';
import { withEmptyHtmlPlugin } from '@/utils/wange-editor';
defineOptions({ name: 'EditorWange' });

type InsertFnType = (url: string, alt: string, href: string) => void;

interface Props {
  editorId?: string;
  height?: number | string;
  editorConfig?: Partial<IEditorConfig>;
  readonly?: boolean;
  modelValue?: string;
  toolbarConfig?: Partial<IToolbarConfig>;
  uploadType: 'COMPANY' | 'STAFF';
}
Boot.registerModule(markdownModule);
Boot.registerPlugin(withEmptyHtmlPlugin);
if (!Boot.__hasAttachmentModule) {
  Boot.registerModule(attachmentModule);
  Boot.__hasAttachmentModule = true;
}
const props = withDefaults(defineProps<Props>(), {
  editorId: 'wangeEditor-1',
  height: '500px',
  editorConfig: () => ({}),
  readonly: false,
  modelValue: '',
  toolbarConfig: () => ({})
});
const isLoading = ref(false);
const emit = defineEmits<{
  (e: 'change', editor: IDomEditor): void;
  (e: 'update:modelValue', value: string): void;
}>();

// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef<IDomEditor>();
const form = useInjectProForm();
let formValue: null | Ref<any> = null;
const valueHtml = ref('');

if (form) {
  const { value } = useFieldUtils();
  formValue = value;
}

watch(
  () => props.modelValue,
  (val: string) => {
    if (val === unref(valueHtml)) return;
    valueHtml.value = val;
  },
  {
    immediate: true
  }
);

// 监听
watch(
  () => valueHtml.value,
  (val: string) => {
    emit('update:modelValue', val);
    if (isRef(formValue)) {
      // 支持单独使用 wangeditor或者接入pro-naive-ui表单组件
      formValue.value = val;
    }
  }
);

const handleCreated = (editor: IDomEditor) => {
  editorRef.value = editor;
};

// 工具栏配置
const toolbarConfig = computed(
  (): IToolbarConfig => ({
    ...props.toolbarConfig,
    insertKeys: {
      index: 0,
      keys: ['uploadAttachment']
    }
  })
);

// 通用上传工具函数：返回上传后的文件信息
const uploadFileAndGetInfo = async (file: File): Promise<{ name: string; url: string } | null> => {
  if (file.size > 100 * 1024 * 1024) {
    window.$message?.error('文件大小不能超过100M');
    return null;
  }
  isLoading.value = true;
  const formData = new FormData();
  formData.append('file', file);

  try {
    const { error, data } = await fetchUploadFile(props.uploadType, formData);
    if (error) {
      window.$message?.error(error.message);
      return null;
    }

    const fileId = data?.id;
    if (!fileId) {
      window.$message?.error('未获取到文件 ID');
      return null;
    }

    return {
      name: file.name,
      url: getFileUrl(fileId)
    };
  } catch (err) {
    console.error('上传失败', err);
    window.$message?.error('上传失败');
    return null;
  } finally {
    isLoading.value = false;
  }
};

// 编辑器配置
const editorConfig = computed((): IEditorConfig => {
  return {
    placeholder: '请输入内容...(回车换行)',
    readOnly: props.readonly,

    hoverbarKeys: {
      attachment: {
        menuKeys: ['downloadAttachment']
      },
      image: {
        menuKeys: ['editImage', 'deleteImage']
      },
      video: {
        menuKeys: ['viewLink']
      }
    },
    customAlert: (msg: string, type: string) => {
      const messageFn = window.$message?.[type as 'success' | 'info' | 'warning' | 'error'] || window.$message?.info;
      messageFn?.(msg);
    },
    autoFocus: false,
    scroll: true,
    MENU_CONF: {
      uploadImage: {
        customUpload: async (file: File, insertFn: InsertFnType) => {
          const fileInfo = await uploadFileAndGetInfo(file);
          if (fileInfo) insertFn(fileInfo.url, fileInfo.url, fileInfo.name); // 插入图片地址
        }
      },
      uploadVideo: {
        customUpload: async (file: File, insertFn: InsertFnType) => {
          const fileInfo = await uploadFileAndGetInfo(file);
          if (fileInfo) insertFn(fileInfo.url, fileInfo.url, fileInfo.name); // 插入视频地址
        }
      },
      uploadAttachment: {
        customUpload: async (file: File, insertFn: InsertFnType) => {
          const fileInfo = await uploadFileAndGetInfo(file);
          if (fileInfo) insertFn(fileInfo.name, fileInfo.url, fileInfo.url); // 插入附件显示名称 + 链接
        }
      }
    },
    ...(props.editorConfig || {})
  };
});

// 编辑器样式
const editorStyle = computed(() => {
  return {
    height: isNumber(props.height) ? `${props.height}px` : props.height
  };
});
const imgRegEx = /<img.*?>/gi;
// 回调函数
const handleChange = (editor: IDomEditor) => {
  const richText = valueHtml.value;
  valueHtml.value = richText.replace(imgRegEx, match => {
    console.log(match);
    return match.replace(/<img/, '<img style="width:30%;"');
  });
  emit('change', editor);
};

// 组件销毁时，及时销毁编辑器
onBeforeUnmount(() => {
  const editor = unref(editorRef.value);
  if (editor === null) return;
  // 销毁，并移除 editor
  editor?.destroy();
});
function handleKeyDown(event: KeyboardEvent) {
  if (event.key === 'Enter') {
    event.preventDefault();
    editorRef.value?.insertText('\n');
  }
}

const getEditorRef = async (): Promise<IDomEditor> => {
  await nextTick();
  return unref(editorRef.value) as IDomEditor;
};

defineExpose({
  getEditorRef
});
</script>

<template>
  <div class="z-10 border-1 border-[var(--tags-view-border-color)] border-solid">
    <!-- 工具栏 -->
    <Toolbar
      :editor="editorRef"
      :editor-id="editorId"
      :default-config="toolbarConfig"
      class="border-0 b-b-1 border-[var(--tags-view-border-color)] border-solid"
    />
    <!-- 编辑器 -->
    <NSpin :show="isLoading">
      <Editor
        v-model="valueHtml"
        :default-config="editorConfig"
        :editor-id="editorId"
        :style="editorStyle"
        @on-change="handleChange"
        @on-created="handleCreated"
        @keydown.enter="handleKeyDown"
      />
    </NSpin>
  </div>
</template>

<style src="@wangeditor/editor/dist/css/style.css"></style>
