<script setup lang="ts">
import { NCard, NImage, NSpin } from 'naive-ui';
import { computed, ref, watch } from 'vue';

import { renderModal } from '../re-modal';

import PreviewFile from './preview-file.vue';

import ExcelIcon from '@/assets/imgs/excel.png';
import PdfIcon from '@/assets/imgs/pdf.png';
import PptIcon from '@/assets/imgs/ppt.png';
import UnknownIcon from '@/assets/imgs/unknown.png';
import WordIcon from '@/assets/imgs/word.png';
import { formatFileSize, getFileUrl, urlToFile } from '@/utils/get-file-url';

interface IProps {
  fileId: number;
  label?: string;
}

const props = defineProps<IProps>();

const file = ref<File | null>(null);
const isLoading = ref(true);

// 映射文件类型到图标
const fileIconMap = {
  pdf: PdfIcon,
  word: WordIcon,
  excel: ExcelIcon,
  ppt: PptIcon,
  other: UnknownIcon
};

// 获取文件类型
const fileType = computed(() => {
  const type = file.value?.type || '';
  const name = file.value?.name || '';

  if (type.startsWith('image')) return 'image';
  if (type === 'application/pdf' || /\.pdf$/i.test(name)) return 'pdf';
  if (
    type === 'application/msword' ||
    type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
    /\.(docx?)$/i.test(name)
  )
    return 'word';
  if (
    type === 'application/vnd.ms-excel' ||
    type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
    /\.(xlsx?)$/i.test(name)
  )
    return 'excel';
  if (
    type === 'application/vnd.ms-powerpoint' ||
    type === 'application/vnd.openxmlformats-officedocument.presentationml.presentation' ||
    /\.(pptx?)$/i.test(name)
  )
    return 'ppt';

  return 'other';
});

const fileIcon = computed(() => fileIconMap[fileType.value as keyof typeof fileIconMap] || UnknownIcon);

const openFile = () => {
  const url = getFileUrl(props.fileId);
  renderModal(
    PreviewFile,
    {
      url,
      fileName: file.value?.name || '未知文件'
    },
    {
      title: file.value?.name,
      contentStyle: {
        width: '100%',
        height: '85vh'
      },
      style: {
        width: '80%'
      }
    }
  );
};

// 文件变化时获取文件内容
watch(
  () => props.fileId,
  async newVal => {
    isLoading.value = true;
    file.value = null;
    if (!newVal) {
      isLoading.value = false;
      return;
    }

    try {
      file.value = await urlToFile(getFileUrl(newVal));
    } catch {
      file.value = null;
    } finally {
      isLoading.value = false;
    }
  },
  { immediate: true }
);
</script>

<template>
  <Transition name="fade" mode="out-in">
    <div v-if="isLoading" key="loading" class="h-[120px] flex items-center justify-center">
      <NSpin size="small" />
    </div>

    <div v-else key="loaded">
      <NCard :title="label" size="small" content-class="!p-2">
        <div class="flex items-center justify-center">
          <!-- 图片预览 -->
          <div>
            <div class="h-[220px] w-[220px] flex items-center justify-center">
              <NImage v-if="fileType === 'image'" :src="getFileUrl(fileId)" object-fit="contain" />
              <img
                v-else
                :src="fileIcon"
                class="h-[150px] w-[150px] cursor-pointer object-contain"
                :alt="`文件类型：${fileType}`"
                @click="openFile"
              />
            </div>
            <NDivider class="!my-2" />
          </div>
        </div>

        <template #footer>
          <div class="w-[220px] text-xs text-gray-500">
            <div class="text-center font-bold">{{ file?.name || '未知文件' }}</div>
            <div class="text-center">{{ formatFileSize(file?.size || 0) }}</div>
          </div>
        </template>
      </NCard>
    </div>
  </Transition>
</template>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
