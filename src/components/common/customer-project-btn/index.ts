import ProjectStateModal from '@/views/crm/project/modules/project-state.vue';
import CustomerInfoModal from '@/views/crm/customer/modules/customer-info.vue';
import { renderModal } from '@/components/re-modal';

export function showInfo(type: 'project' | 'customer', dataId: number = 0) {
  let title = '';
  let id = 0;
  let modal = null;
  id = dataId;
  if (type === 'project') {
    title = '项目详情';
    modal = ProjectStateModal;
  } else if (type === 'customer') {
    title = '联络人详情';
    modal = CustomerInfoModal;
  } else {
    throw new Error('Invalid type');
  }

  renderModal(
    modal,
    { id },
    {
      title,
      maskClosable: true,
      style: {
        width: '66%'
      }
    }
  );

  if (!id) {
    window.$message?.error('未找到对应联络人或项目');
  }
}
