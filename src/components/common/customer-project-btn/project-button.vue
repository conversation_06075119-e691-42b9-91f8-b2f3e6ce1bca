<script setup lang="ts">
import { useAttrs } from 'vue';
import { showProjectStr } from '@/utils/useful_func';
import { showInfo } from '@/components/common/customer-project-btn/index';

interface IProps {
  project: Api.Crm.Project;
}
defineProps<IProps>();
const attrs = useAttrs();
</script>

<template>
  <NButton type="success" size="tiny" v-bind="attrs" @click="showInfo('project', project.id)">
    {{ showProjectStr(project) }}
  </NButton>
</template>

<style scoped></style>
