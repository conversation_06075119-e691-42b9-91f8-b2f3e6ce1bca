<script setup lang="ts">
import { useAttrs } from 'vue';
import { showCustomerStr } from '@/utils/useful_func';
import { showInfo } from '@/components/common/customer-project-btn/index';

interface IProps {
  customer: Api.Crm.Customer;
}
defineProps<IProps>();
const attrs = useAttrs();
</script>

<template>
  <NButton type="primary" size="tiny" ghost v-bind="attrs" @click="showInfo('customer', customer.id)">
    {{ showCustomerStr(customer) }}
  </NButton>
</template>

<style scoped></style>
