<script setup lang="ts">
import { useTreeTable } from '@/hooks/common/tree-table';
import { fetchGetDeptList } from '@/service/api/system/dept';

type TreeSelectValue = string | number | null | Array<string | number>;
defineOptions({
  inheritAttrs: false
});
const model = defineModel<TreeSelectValue>('value', { required: true, default: null });

const attrs = useAttrs();
const { data, expandedRowKeys, loading } = useTreeTable({
  apiFn: fetchGetDeptList,
  idField: 'id',
  columns: () => []
});
</script>

<template>
  <NTreeSelect
    v-model:value="model"
    v-bind="attrs"
    v-model:expanded-keys="expandedRowKeys"
    :loading="loading"
    :options="data"
    label-field="name"
    key-field="id"
    filterable
    clearable
  />
</template>
