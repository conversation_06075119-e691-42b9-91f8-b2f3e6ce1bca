import { ProField, useMergePlaceholder, useOverrideProps } from 'pro-naive-ui';
import type { SlotsType } from 'vue';
import { defineComponent } from 'vue';

import SelectWithSearch from './components/select-with-search';
import { proSelectWithSearchProps } from './props';
import type { ProSelectWithSearchSlots } from './slots';
const name = 'ProSelectWithSearch';
export default defineComponent({
  name,
  props: proSelectWithSearchProps,
  inheritAttrs: false,
  slots: Object as SlotsType<ProSelectWithSearchSlots>,
  setup(props) {
    const placeholder = props.fieldProps?.placeholder || useMergePlaceholder('ProSelect', props);

    const overridedProps = useOverrideProps(name, props);
    return {
      placeholder,
      overridedProps
    };
  },
  render() {
    return (
      <ProField {...this.overridedProps} placeholder={this.placeholder}>
        {{
          ...this.$slots,
          input: (inputProps: any) => {
            return <SelectWithSearch {...inputProps} v-slots={this.$slots}></SelectWithSearch>;
          }
        }}
      </ProField>
    );
  }
});
