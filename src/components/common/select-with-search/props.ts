import { selectProps } from 'naive-ui';
import type { BaseFieldProps } from 'pro-naive-ui';
import { proFieldSharedProps } from 'pro-naive-ui';
import type { ExtractPublicPropTypes, PropType } from 'vue';

export const selectWithSearchProps = {
  ...selectProps,
  apiFunc: {
    type: Function as PropType<(query: string, page: number) => Promise<CommonType.Option<number>[]>>,
    required: true
  },
  debounce: Number,
  pageSize: Number,
  minFilterLength: Number,
  selectedOptions: Array,
  cache: Boolean,
  placeholder: String
} as const;

export type SelectWithSearchProps = ExtractPublicPropTypes<typeof selectWithSearchProps>;

export const proSelectWithSearchProps = {
  ...proFieldSharedProps,
  fieldProps: Object as PropType<BaseFieldProps<SelectWithSearchProps>>
} as const;

export type ProSelectWithSearchProps = ExtractPublicPropTypes<typeof proSelectWithSearchProps>;
