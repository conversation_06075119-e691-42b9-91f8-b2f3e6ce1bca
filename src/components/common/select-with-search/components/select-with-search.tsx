import { get, isArray, isFunction, noop } from 'lodash-es';
import { NFlex } from 'naive-ui';
import { eachTree } from 'pro-composables';
import { useFieldUtils } from 'pro-naive-ui';
import type { SlotsType, VNodeChild } from 'vue';
import { computed, defineComponent } from 'vue';

import SelectWithSearch from '../../select-with-search.vue';
import { selectWithSearchProps } from '../props';
import type { ProSelectWithSearchSlots } from '../slots';
export default defineComponent({
  name: 'SelectWithSearch',
  props: selectWithSearchProps,
  slots: Object as SlotsType<ProSelectWithSearchSlots>,
  inheritAttrs: false,
  setup(props) {
    const { empty, value, readonly, emptyDom } = useFieldUtils();

    const selectedLabels = computed(() => {
      const {
        renderTag,
        renderLabel,
        options = [],
        labelField = 'label',
        valueField = 'value',
        childrenField = 'children'
      } = props;

      const labels: VNodeChild[] = [];
      const selectedValue = isArray(value.value) ? value.value : [value.value];
      eachTree(
        options,
        item => {
          const value = get(item, valueField);
          if (selectedValue.includes(value)) {
            let label = get(item, labelField) as VNodeChild;
            if (renderTag) {
              label = renderTag({ option: item as any, handleClose: noop });
            }
            if (renderLabel) {
              label = renderLabel(item as any, true);
            }
            if (isFunction(label)) {
              label = label(item, true);
            }
            if (label) {
              labels.push(<span>{label}</span>);
            }
          }
        },
        childrenField
      );
      return labels;
    });

    const nSelectProps = computed(() => {
      return {
        ...props,
        value: props.value ?? null
      };
    });
    return {
      empty,
      readonly,
      emptyDom,
      nSelectProps,
      selectedLabels
    };
  },
  render() {
    let dom: VNodeChild;
    if (this.readonly) {
      dom = this.empty ? this.emptyDom : <NFlex size="small">{this.selectedLabels}</NFlex>;
    } else {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      dom = <SelectWithSearch {...this.$attrs} {...this.nSelectProps.inputProps} v-slots={this.$slots} />;
    }

    return this.$slots.input
      ? this.$slots.input({
          inputDom: dom,
          readonly: this.readonly,
          inputProps: this.nSelectProps
        })
      : dom;
  }
});
