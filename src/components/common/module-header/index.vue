<script setup lang="ts">
const props = defineProps<{
  title: string;
  onClick?: () => void; // 跳转路径
}>();

function handleClick() {
  if (props.onClick) {
    props.onClick();
  }
}
</script>

<template>
  <!-- 标题与按钮 -->
  <div class="mb-4 w-full flex items-center justify-between bg-white pr-2">
    <div class="w-[100px] flex items-center">
      <div class="mr-2 h-4 w-1.5 rounded bg-blue-500"></div>
      <span class="bg-white pr-2 text-base font-semibold">{{ title }}</span>
    </div>
    <NDivider class="flex-1" />
    <div class="w-[50px]">
      <div
        v-if="onClick"
        class="flex cursor-pointer items-center justify-end text-sm text-blue-500 hover:underline"
        @click="handleClick"
      >
        全部
      </div>
    </div>
  </div>
</template>
