<script setup lang="ts">
import { computed, createTextVNode, defineComponent } from 'vue';
import { useDialog, useLoadingBar, useMessage, useModal, useNotification } from 'naive-ui';
import { ProConfigProvider, enUS, zhCN } from 'pro-naive-ui';
import { naiveLocales } from '@/locales/naive';
import { useAppStore } from '@/store/modules/app';
import { propsOverRides } from '@/utils/pro-naive-prop-overrides';

const appStore = useAppStore();

const naiveLocale = computed(() => {
  return naiveLocales[appStore.locale];
});

defineOptions({
  name: 'AppProvider'
});

const ContextHolder = defineComponent({
  name: 'ContextHolder',
  setup() {
    function register() {
      window.$loadingBar = useLoadingBar();
      window.$dialog = useDialog();
      window.$message = useMessage();
      window.$notification = useNotification();
      window.$modal = useModal();
    }

    register();

    return () => createTextVNode();
  }
});
</script>

<template>
  <ProConfigProvider
    :locale="naiveLocale.name === 'zh-CN' ? zhCN : enUS"
    class="h-full"
    :prop-overrides="propsOverRides"
  >
    <NLoadingBarProvider>
      <NModalProvider>
        <NDialogProvider>
          <NNotificationProvider>
            <NMessageProvider>
              <ContextHolder />
              <slot></slot>
            </NMessageProvider>
          </NNotificationProvider>
        </NDialogProvider>
      </NModalProvider>
    </NLoadingBarProvider>
  </ProConfigProvider>
</template>

<style scoped></style>
