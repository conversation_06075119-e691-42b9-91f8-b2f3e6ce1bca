<script setup lang="ts">
import { useAttrs, useId } from 'vue';
import { storeToRefs } from 'pinia';
import { useLoadingStore } from '@/store/modules/app/loading';

import { $t } from '@/locales';

const attrs = useAttrs();
const loadingStore = useLoadingStore();
const id = useId();
const { loading } = storeToRefs(loadingStore);
</script>

<template>
  <NButton v-bind="attrs" :key="id" :loading="loading">
    <slot>{{ $t('common.save') }}</slot>
  </NButton>
</template>

<style scoped></style>
