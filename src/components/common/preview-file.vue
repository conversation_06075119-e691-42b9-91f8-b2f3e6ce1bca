<script lang="ts" name="previewFile" setup>
// 引入VueOfficeExcel组件
import VueOfficeExcel from '@vue-office/excel';
// 引入VueOfficeDocx组件
import VueOfficeDocx from '@vue-office/docx';
// 引入VueOfficePptx 组件
import VueOfficePptx from '@vue-office/pptx';

// 引入相关样式
import { fetchGetFile } from '@/service/api/system/upload';
import '@vue-office/docx/lib/index.css';
import '@vue-office/excel/lib/index.css';

const props = defineProps<{
  url: string;
  fileName: string;
}>();
const state = reactive({
  url: '',
  type: '',
  iframeContent: '', // 存储txt文件内容
  options: {
    xls: true
  },
  loading: false // 加载状态
});

const fileDetail = ref<Api.System.FileRespVO>({
  url: '',
  name: '',
  size: 0,
  type: '',
  createTime: new Date(),
  configId: 0,
  id: 0,
  path: ''
});
const showExcelAlert = computed(() => {
  return (state.type === 'xls' || state.type === 'xlsx') && fileDetail.value.size < 10 * 1024 * 1024;
});
const showOfficeAlert = computed(() => {
  return fileDetail.value.size > 10 * 1024 * 1024;
});

function getFileExtension(filename: string): string {
  const idx = filename.lastIndexOf('.');
  return idx !== -1 ? filename.slice(idx + 1).toLowerCase() : '';
}

// 解析text文件
const parseTextFile = async (filePath: string) => {
  const response = await fetch(filePath);
  const text = await response.text();
  state.iframeContent = `<style>body { margin: 0; background: #f8f8f8;}pre {box-sizing: border-box;margin: 0;padding: 24px 32px; background: #fff;color: #222;border-radius: 8px;
      font-family: 'Fira Mono', 'Consolas', 'Menlo', monospace;font-size: 15px;line-height: 1.7;box-shadow: 0 2px 8px rgba(0,0,0,0.04);overflow: auto;white-space: pre-wrap;
      word-break: break-all;} ::-webkit-scrollbar { width: 8px;background: #f0f0f0;} ::-webkit-scrollbar-thumb {background: #d4d4d4; border-radius: 4px; }</style>
      <pre>${text.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</pre>`;
  state.loading = false;
};
// 打开预览弹窗
const onPreview = async () => {
  state.loading = true;

  state.url = props.url;
  state.type = getFileExtension(props.fileName);
  const fileId = props.url.split('/').pop();

  const { data, error } = await fetchGetFile(Number(fileId));
  if (error) {
    return;
  }
  fileDetail.value = data;
  if (state.type === 'xls') {
    state.options.xls = true;
  }
  if (state.type === 'xlsx') {
    state.options.xls = false;
  }

  // 对于不支持的类型立即结束加载
  if (!['xlsx', 'docx', 'pptx', 'pdf', 'html'].includes(state.type)) {
    state.loading = false;
  }
  if (state.type === 'txt') {
    parseTextFile(state.url);
  }
};

// 文件加载完成处理
const handleLoadComplete = () => {
  state.loading = false;
};

// Office文件加载完成处理
const handleOfficeMounted = () => {
  state.loading = false;
};

const handleDownload = () => {
  const a = document.createElement('a');
  a.href = state.url;
  a.download = props.fileName;
  a.click();
  a.remove();
};
onMounted(() => {
  onPreview();
});
</script>

<template>
  <div class="preview-container">
    <!-- excel   -->
    <NAlert v-if="showExcelAlert" type="warning" class="mb-5px">
      预览Excel文件时，若图片无法显示出现异常空白，建议下载后查看。
    </NAlert>
    <!-- 10m 以上 -->
    <NAlert v-if="showOfficeAlert" type="warning" class="mb-5px">文件过大无法预览，请下载后查看。</NAlert>

    <!-- 预览区域 -->
    <div v-if="!showOfficeAlert" class="preview-body">
      <VueOfficeExcel
        v-if="state.type === 'xlsx'"
        :src="state.url"
        :options="state.options"
        @mounted="handleOfficeMounted"
        @rendered="handleOfficeMounted"
      />
      <VueOfficeDocx
        v-else-if="state.type === 'docx'"
        :src="state.url"
        @mounted="handleOfficeMounted"
        @rendered="handleOfficeMounted"
      />
      <VueOfficePptx
        v-else-if="state.type === 'pptx'"
        :src="state.url"
        @mounted="handleOfficeMounted"
        @rendered="handleOfficeMounted"
      />
      <iframe
        v-else-if="['pdf', 'html', 'txt'].includes(state.type)"
        class="preview-item"
        :src="state.type === 'txt' ? undefined : state.url"
        :srcdoc="state.type === 'txt' ? state.iframeContent : undefined"
        @load="handleLoadComplete"
      />
      <!-- 图片 -->
      <NImage v-else-if="state.type === 'jpg' || state.type === 'png' || state.type === 'jpeg'" :src="state.url" />
    </div>

    <!-- 底部操作栏 -->
    <div class="preview-footer">
      <NButton type="primary" class="download-btn" @click="handleDownload">下载</NButton>
    </div>
  </div>
</template>

<style scoped>
.preview-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 内容撑满剩余高度 */
.preview-body {
  flex: 1;
  overflow: hidden;
  position: relative;
}

/* iframe 尽可能铺满预览区域 */
.preview-item {
  width: 100%;
  height: 100%;
}

/* 下载按钮固定在底部右侧 */
.preview-footer {
  margin-top: 10px;
  text-align: right;
}
</style>

<style>
.pptx-preview-wrapper {
  width: 100% !important;
}
</style>
