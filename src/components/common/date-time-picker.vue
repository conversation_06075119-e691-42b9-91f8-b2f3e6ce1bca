<script setup lang="ts">
import dayjs from 'dayjs';
import { computed, ref } from 'vue';

const time = defineModel<string | number | [string, string] | [number, number] | null | undefined>('time', {
  required: true
});
interface Props {
  label: string;
  useFor?: string;
  clearable?: boolean;
}
const props = withDefaults(defineProps<Props>(), {
  clearable: true
});

const dynamicProps = ref<Record<string, any>>({});

type DatePickerType =
  | 'date'
  | 'datetime'
  | 'daterange'
  | 'datetimerange'
  | 'month'
  | 'monthrange'
  | 'year'
  | 'yearrange'
  | 'quarter'
  | 'quarterrange'
  | 'week';

let type: DatePickerType;
let dayjsFormat: string | [string, string];
let format: string;
const useFor = props.useFor || 'datetime_without_second';

switch (useFor) {
  case 'date':
    type = 'date';
    dayjsFormat = 'YYYY-MM-DD';
    format = 'y年 M月 d日';
    break;
  case 'month_date':
    type = 'month';
    dayjsFormat = 'YYYY-MM-01';
    format = 'y年 M月';
    dynamicProps.value = {
      'year-format': 'y年',
      'month-format': 'M月'
    };
    break;
  case 'month':
    type = 'month';
    dayjsFormat = 'YYYY-MM';
    format = 'y年 M月';
    dynamicProps.value = {
      'year-format': 'y年',
      'month-format': 'M月'
    };
    break;
  case 'year':
    type = 'year';
    dayjsFormat = 'YYYY';
    format = 'y年';
    dynamicProps.value = {
      'year-format': 'y年'
    };
    break;
  case 'monthrange':
    type = 'monthrange';
    dayjsFormat = ['YYYY-MM', 'YYYY-MM'];
    format = 'y年 M月';
    dynamicProps.value = {
      'year-format': 'y年',
      'month-format': 'M月'
    };
    break;
  case 'daterange':
    type = 'daterange';
    dayjsFormat = ['YYYY-MM-DD', 'YYYY-MM-DD'];
    format = 'y年 M月 d日';
    break;
  case 'datetime_without_second':
    type = 'datetime';
    dayjsFormat = 'YYYY-MM-DD HH:mm:00';
    format = 'yyyy-MM-dd a h:mm';
    dynamicProps.value = {
      'time-picker-props': { format: 'HH:mm' }
    };
    break;
  case 'date_with_year':
    type = 'year';
    dayjsFormat = 'YYYY';
    format = 'yyyy年';
    break;
  default:
    throw new Error('useFor 参数错误');
}

const timestamp = computed<number | [number, number] | null>({
  get: () => {
    if (!time.value) return null;

    if (Array.isArray(dayjsFormat)) {
      const [start, end] = time.value as [string, string];
      return [dayjs(start).valueOf(), dayjs(end).valueOf()];
    }

    return dayjs(time.value as string).valueOf();
  },
  set: val => {
    if (!val) {
      time.value = null;
    } else if (Array.isArray(val)) {
      time.value = [dayjs(val[0]).format(dayjsFormat[0]), dayjs(val[1]).format(dayjsFormat[1])];
    } else {
      time.value = dayjs(val).format(dayjsFormat as string);
    }
  }
});
</script>

<template>
  <NDatePicker
    v-model:value="timestamp"
    :type="type"
    :format="format"
    :value-format="format"
    :placeholder="'请选择' + label"
    v-bind="dynamicProps"
    :clearable="clearable"
  />
</template>
