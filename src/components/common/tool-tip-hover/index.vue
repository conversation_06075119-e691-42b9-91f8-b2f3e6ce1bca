<script lang="ts" setup>
import { fetchGetFormDesc } from '@/service/api/system/user';

const props = defineProps<{
  label: string;
  code?: string;
  scene?: string;
}>();

const show = ref(false);
const loading = ref(false);
const content = ref('');
const descCache = new Map<string, string>();

const handleShow = async (value: boolean) => {
  if (value) {
    show.value = true;
    const key = `${props.code || props.label}_${props.scene}`;
    if (descCache.has(key)) {
      content.value = descCache.get(key)!;
      loading.value = false;
      return;
    }
    try {
      loading.value = true;
      const { data } = await fetchGetFormDesc({
        code: (props.code || props.label) as string,
        scene: props.scene as string
      });
      content.value = data.description;
      descCache.set(key, data.description);
    } finally {
      loading.value = false;
    }
  }
};
</script>

<template>
  <div class="flex items-center">
    <NTooltip v-model:show="show" trigger="hover" @update:show="handleShow">
      <template #default>
        <span v-if="loading">加载中...</span>
        <span v-else>{{ content }}</span>
      </template>
      <template #trigger>
        <div class="cursor-pointer pr-3px">
          <SvgIcon class="text-15px" icon="material-symbols-light:help" />
        </div>
      </template>
    </NTooltip>
    <div>{{ label }}</div>
  </div>
</template>
