<script setup lang="ts">
import type { ToolbarNames } from 'md-editor-v3';
import { MdEditor } from 'md-editor-v3';
import 'md-editor-v3/lib/style.css';
import { computed, useAttrs } from 'vue';

const defaultToolbars: ToolbarNames[] = [
  'bold',
  'underline',
  'italic',
  'strikeThrough',
  'title',
  'sub',
  'sup',
  'quote',
  'unorderedList',
  'orderedList',
  'task',
  'codeRow',
  'code',
  'link',
  'table',
  'mermaid',
  'katex',
  'revoke',
  'next',
  'save',
  'prettier',
  'pageFullscreen',
  'fullscreen',
  'preview',
  'previewOnly',
  'htmlPreview',
  'catalog',
  'github',
  '-',
  '='
];

const props = defineProps<{
  toolbars?: ToolbarNames[];
}>();

const mergedToolbars = computed<ToolbarNames[]>(() => {
  if (!props.toolbars) return defaultToolbars;
  return Array.from(new Set([...defaultToolbars, ...props.toolbars]));
});

const attrs = useAttrs();
const modelValue = defineModel<string>();
</script>

<template>
  <MdEditor v-model="modelValue" v-bind="attrs" :toolbars="mergedToolbars" />
</template>
