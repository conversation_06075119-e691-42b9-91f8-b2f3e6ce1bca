<script setup lang="ts">
import { ref } from 'vue';
import { NTag } from 'naive-ui';
import { showUserStr } from '@/utils/useful_func';
import CustomerInfoModal from '@/views/crm/customer/modules/customer-info.vue';
import ProjectStateModal from '@/views/crm/project/modules/project-state.vue';
import { renderModal } from '../re-modal';

interface Props {
  maps: Record<string, string>;
  data: Record<string, any>;
  showProject?: boolean;
  showCustomer?: boolean;
  showUser?: boolean;
}

const props = defineProps<Props>();

const showProject = ref(props.showProject || false);
const showCustomer = ref(props.showCustomer || false);
const showUser = ref(props.showUser || false);

// 联络人、项目信息弹窗
function showInfo(type: 'project' | 'customer', dataId: number = 0) {
  let title = '';
  let id = 0;
  let modal = null;
  if (type === 'project') {
    title = '项目详情';
    id = props.data.project?.id || 0;
    modal = ProjectStateModal;
  } else if (type === 'customer') {
    title = '联络人详情';
    id = dataId;
    modal = CustomerInfoModal;
  } else {
    throw new Error('Invalid type');
  }

  renderModal(
    modal,
    { id },
    {
      title,
      maskClosable: true,
      style: {
        width: '66%'
      }
    }
  );

  if (!id) {
    window.$message?.error('未找到对应联络人或项目');
  }
}
</script>

<template>
  <NTable striped single-column>
    <tbody>
      <slot name="top" />
      <tr v-if="showProject">
        <td class="table-info-left-td">项目</td>
        <td>
          <ProjectButton :project="data.project" ghost />
        </td>
      </tr>
      <tr v-if="showCustomer">
        <td class="table-info-left-td">联络人</td>
        <td>
          <CustomerButton v-for="item of data.customers" :key="item.id" :customer="item" class="mr-5px" />
        </td>
      </tr>
      <tr v-if="showUser">
        <td class="table-info-left-td">商务</td>
        <td>
          <NTag type="warning" size="small">{{ showUserStr(data.user) }}</NTag>
        </td>
      </tr>
      <tr v-for="(name, key) in maps" :key="key">
        <td class="table-info-left-td">{{ name }}</td>
        <td>{{ data[key] || '-' }}</td>
      </tr>
      <slot name="bottom" />
    </tbody>
  </NTable>
</template>

<style lang="scss">
.table-info-left-td {
  width: 120px;
  font-weight: bold;
}
</style>
