<script setup lang="ts">
import { useDebounceFn } from '@vueuse/core';
import { computed, onBeforeUnmount, ref } from 'vue';

type ModelValue = number | string | null | (number | string)[];

const modelValue = defineModel<ModelValue>('value', {
  required: false,
  default: null
});

defineOptions({
  inheritAttrs: false
});

const props = withDefaults(
  defineProps<{
    apiFunc: (query: string, page: number) => Promise<CommonType.Option<number>[]>;
    debounce?: number;
    placeholder: string;
    multiple?: boolean;
    clearable?: boolean;
    pageSize?: number; // pageSize为0，表示不分页
    minFilterLength?: number;
    selectedOptions: any[]; // 如果是单选的话，需要把对象变成数组
    valueField?: string; // 用于处理 selectedOptions 中 value 的转换
    labelField?: string; // 用于处理 selectedOptions 中 label 的转换
    cache?: boolean; // 组件不与其他组件联动时缓存设为true可减少查询；联动场景下前置组件更新不影响本组件，缓存需设为false。
  }>(),
  {
    clearable: true,
    debounce: 300,
    pageSize: 10,
    minFilterLength: 1,
    cache: true,
    valueField: 'id',
    labelField: 'name'
  }
);

// 是否开启分页
const withPage = props.pageSize > 0;

const emit = defineEmits<{
  valueChanged: [option: CommonType.Option<number> | null | CommonType.Option<number>[], value: ModelValue];
}>();
// 加载状态
const loading = ref(false);
// 搜索关键词
const searchQuery = ref('');
// 当前分页
const currentPage = ref(1);
// 是否还有更多数据
const hasMore = ref(true);
// 当前搜索得到的选项
const options = ref<CommonType.Option<number>[]>([]);
// 缓存 Map，Key 为搜索关键词，Value 为对应选项列表
const cacheMap = new Map<string, CommonType.Option<number>[]>();
// 已获取的页数
const fetchedPages = new Set<string>();
// 记录上一次请求的关键词，避免重复请求
const lastRequestQuery = ref('');
// 转换后的选中选项
const convertedSelectedOptions = computed<CommonType.Option<number>[]>(() => {
  if (!props.selectedOptions || !props.selectedOptions.length) {
    return [];
  }

  return props.selectedOptions
    .filter(item => item !== undefined && item !== null)
    .map(item => ({
      ...item,
      value: item[props.valueField],
      label: item[props.labelField]
    }));
});
/**
 * computed: 合并选中的选项和搜索列表
 *
 * - 先把所有已选中的放前面
 * - 再拼上搜索结果里不包含的那部分
 */
const mergedOptions = computed(() => {
  if (!convertedSelectedOptions.value.length) {
    return options.value;
  }
  const selectedValues = new Set(convertedSelectedOptions.value.map(o => o.value));
  return [...convertedSelectedOptions.value, ...options.value.filter(o => !selectedValues.has(o.value))];
});
/** 空状态文案 */
const emptyText = computed(() => {
  if (loading.value) {
    return '加载中...';
  }
  if (searchQuery.value.length < props.minFilterLength) {
    return '输入关键字进行搜索';
  }
  return `未查询到相应的${props.placeholder}数据`;
});

/** 获取选项 */
async function fetchOptions(query: string) {
  try {
    loading.value = true;
    lastRequestQuery.value = query;
    const cacheKey = `${query}_${currentPage.value}`;

    const pageKey = `${query}_${currentPage.value}`;
    if (fetchedPages.has(pageKey)) return;
    fetchedPages.add(pageKey);

    if (props.cache && cacheMap.has(cacheKey)) {
      const cachedData = cacheMap.get(cacheKey) || [];
      options.value = currentPage.value === 1 ? cachedData : [...options.value, ...cachedData];
      hasMore.value = cachedData.length >= props.pageSize;
      return;
    }

    const res = await props.apiFunc(query, currentPage.value);
    if (lastRequestQuery.value !== query) return;

    hasMore.value = res.length >= props.pageSize;
    options.value = currentPage.value === 1 ? res : [...options.value, ...res];

    if (props.cache) {
      cacheMap.set(cacheKey, res);
    }
  } catch (error) {
    window.$message?.error(`请求失败: ${error}`);
    options.value = [];
  } finally {
    setTimeout(() => {
      loading.value = false;
    }, 200);
  }
}

/** 防抖搜索方法 */
const handleSearch = useDebounceFn(async (query: string) => {
  if (query.length < props.minFilterLength) {
    currentPage.value = 1;
    hasMore.value = false;
    options.value = [];
    searchQuery.value = query;
    return;
  }

  currentPage.value = 1;
  hasMore.value = withPage;
  options.value = [];
  fetchedPages.clear();
  searchQuery.value = query;
  await fetchOptions(query);
}, props.debounce);

/** 下拉滚动加载更多 */
function handleScroll(e: Event) {
  if (!withPage) return;
  const { scrollTop, clientHeight, scrollHeight } = e.target as HTMLElement;
  if (scrollHeight - (scrollTop + clientHeight) < 50 && hasMore.value && !loading.value) {
    currentPage.value += 1;
    fetchOptions(searchQuery.value);
  }
}

// 重置
function reset() {
  modelValue.value = null;
  searchQuery.value = '';
  currentPage.value = 1;
  options.value = [];
  cacheMap.clear();
  fetchedPages.clear();
  fetchOptions('');
}

defineExpose({
  reset
});

// 聚焦时搜索
function initEmptySearch() {
  if (searchQuery.value.length === 0) {
    fetchOptions('');
  }
}

onBeforeUnmount(() => {
  cacheMap.clear();
  fetchedPages.clear();
});

function valueChange(value: ModelValue, option: CommonType.Option<number> | null | CommonType.Option<number>[]) {
  emit('valueChanged', option, value);
}
</script>

<template>
  <NSelect
    v-bind="$attrs"
    v-model:value="modelValue"
    filterable
    remote
    :options="mergedOptions"
    :loading="loading"
    :clearable="clearable"
    :multiple="multiple"
    :placeholder="`请选择${placeholder}`"
    @search="handleSearch"
    @scroll="handleScroll"
    @update:value="valueChange"
    @focus="initEmptySearch"
  >
    <template #empty>
      {{ emptyText }}
    </template>
  </NSelect>
</template>
