import type { Component } from 'vue';
import { h, ref } from 'vue';
import type { DialogOptions } from 'naive-ui';
import { storeToRefs } from 'pinia';
import { $t } from '@/locales';
import { useLoadingStore } from '@/store/modules/app/loading';
type ModalProps = {
  func?: () => void;
} & DialogOptions;
// 不带按钮Modal 使用是需要向这里抛出提交函数 如果是使用useTable的话需要使用submitted触发getData方法
function renderModal(component: Component, props: object, modalProps: ModalProps) {
  const instance = ref();
  const loadingStore = useLoadingStore();
  const { loading } = storeToRefs(loadingStore);
  const modal = window?.$modal?.create({
    preset: 'dialog',
    showIcon: false,
    loading,
    maskClosable: false,
    // 关闭弹窗事件
    onClose() {
      return true;
    },
    // 点击取消按钮
    onNegativeClick() {
      return true;
    },
    // 确认按钮事件
    async onPositiveClick() {
      try {
        loadingStore.setLoading(true);
        const success = await instance.value?.handleSubmit?.();
        if (success === undefined || success === false) {
          return false;
        }
        if (modalProps.func) {
          modalProps.func();
        }
        return true;
      } finally {
        loadingStore.setLoading(false);
      }
    },
    content: () =>
      h(component, {
        ref: instance,
        ...props,
        // 点击表单自带的确定按钮（提交表单）
        onBtnSubmit: btnSubmit,
        // 点击表单自带的取消按钮（取消并关闭弹框）
        onBtnClose: btnClose
      }),
    ...modalProps
  });

  // 点击表单自带的确定按钮（提交表单）
  function btnSubmit() {
    modal?.destroy();
    if (modalProps.func) {
      modalProps.func();
    }
  }

  // 点击表单自带的取消按钮（取消并关闭弹框）
  function btnClose() {
    modal?.destroy();
  }
}
// 带按钮Modal 如果使用此方法需要在handleSubmit中try return true \ catch return false
function renderModalBtn(component: Component, props: object, modalProps: ModalProps) {
  renderModal(component, props, {
    negativeButtonProps: {
      size: 'medium'
    },
    positiveButtonProps: {
      size: 'medium'
    },
    positiveText: $t('common.confirm'),
    negativeText: $t('common.cancel'),
    ...modalProps
  });
}
export { renderModal, renderModalBtn };
