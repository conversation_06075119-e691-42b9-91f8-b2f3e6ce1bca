import {
  ProDate,
  ProDateRange,
  ProDateTime,
  ProDateTimeRange,
  ProInput,
  ProRate,
  ProSelect,
  create
} from 'pro-naive-ui';
import type { App } from 'vue';

import DeptSelect from '@/components/common/pro-dept-select';
import SelectWithSearch from '@/components/common/select-with-search/select-with-search';
/** pro-naive-ui 支持配置表单的按需加载，所以需要注册 */
export function setupProNaiveComponents(app: App) {
  const proNaive = create({
    components: [
      ProInput,
      ProDate,
      ProDateTime,
      ProRate,
      ProSelect,
      ProDateRange,
      SelectWithSearch,
      DeptSelect,
      ProDateTimeRange
    ]
  });
  app.use(proNaive);
}
