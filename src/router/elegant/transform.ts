/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { RouteRecordRaw, RouteComponent } from 'vue-router';
import type { ElegantConstRoute } from '@elegant-router/vue';
import type { RouteMap, RouteKey, RoutePath } from '@elegant-router/types';

/**
 * transform elegant const routes to vue routes
 * @param routes elegant const routes
 * @param layouts layout components
 * @param views view components
 */
export function transformElegantRoutesToVueRoutes(
  routes: ElegantConstRoute[],
  layouts: Record<string, RouteComponent | (() => Promise<RouteComponent>)>,
  views: Record<string, RouteComponent | (() => Promise<RouteComponent>)>
) {
  return routes.flatMap(route => transformElegantRouteToVueRoute(route, layouts, views));
}

/**
 * transform elegant route to vue route
 * @param route elegant const route
 * @param layouts layout components
 * @param views view components
 */
function transformElegantRouteToVueRoute(
  route: ElegantConstRoute,
  layouts: Record<string, RouteComponent | (() => Promise<RouteComponent>)>,
  views: Record<string, RouteComponent | (() => Promise<RouteComponent>)>
) {
  const LAYOUT_PREFIX = 'layout.';
  const VIEW_PREFIX = 'view.';
  const ROUTE_DEGREE_SPLITTER = '_';
  const FIRST_LEVEL_ROUTE_COMPONENT_SPLIT = '$';

  function isLayout(component: string) {
    return component.startsWith(LAYOUT_PREFIX);
  }

  function getLayoutName(component: string) {
    const layout = component.replace(LAYOUT_PREFIX, '');

    if(!layouts[layout]) {
      throw new Error(`Layout component "${layout}" not found`);
    }

    return layout;
  }

  function isView(component: string) {
    return component.startsWith(VIEW_PREFIX);
  }

  function getViewName(component: string) {
    const view = component.replace(VIEW_PREFIX, '');

    if(!views[view]) {
      throw new Error(`View component "${view}" not found`);
    }

    return view;
  }

  function isFirstLevelRoute(item: ElegantConstRoute) {
    return !item.name.includes(ROUTE_DEGREE_SPLITTER);
  }

  function isSingleLevelRoute(item: ElegantConstRoute) {
    return isFirstLevelRoute(item) && !item.children?.length;
  }

  function getSingleLevelRouteComponent(component: string) {
    const [layout, view] = component.split(FIRST_LEVEL_ROUTE_COMPONENT_SPLIT);

    return {
      layout: getLayoutName(layout),
      view: getViewName(view)
    };
  }

  const vueRoutes: RouteRecordRaw[] = [];

  // add props: true to route
  if (route.path.includes(':') && !route.props) {
    route.props = true;
  }

  const { name, path, component, children, ...rest } = route;

  const vueRoute = { name, path, ...rest } as RouteRecordRaw;

  try {
    if (component) {
      if (isSingleLevelRoute(route)) {
        const { layout, view } = getSingleLevelRouteComponent(component);

        const singleLevelRoute: RouteRecordRaw = {
          path,
          component: layouts[layout],
          meta: {
            title: route.meta?.title || ''
          },
          children: [
            {
              name,
              path: '',
              component: views[view],
              ...rest
            } as RouteRecordRaw
          ]
        };

        return [singleLevelRoute];
      }

      if (isLayout(component)) {
        const layoutName = getLayoutName(component);

        vueRoute.component = layouts[layoutName];
      }

      if (isView(component)) {
        const viewName = getViewName(component);

        vueRoute.component = views[viewName];
      }

    }
  } catch (error: any) {
    console.error(`Error transforming route "${route.name}": ${error.toString()}`);
    return [];
  }

  // add redirect to child
  if (children?.length && !vueRoute.redirect) {
    vueRoute.redirect = {
      name: children[0].name
    };
  }

  if (children?.length) {
    const childRoutes = children.flatMap(child => transformElegantRouteToVueRoute(child, layouts, views));

    if(isFirstLevelRoute(route)) {
      vueRoute.children = childRoutes;
    } else {
      vueRoutes.push(...childRoutes);
    }
  }

  vueRoutes.unshift(vueRoute);

  return vueRoutes;
}

/**
 * map of route name and route path
 */
const routeMap: RouteMap = {
  "root": "/",
  "not-found": "/:pathMatch(.*)*",
  "403": "/403",
  "404": "/404",
  "500": "/500",
  "crm": "/crm",
  "crm_customer": "/crm/customer",
  "crm_follow": "/crm/follow",
  "crm_plan": "/crm/plan",
  "crm_project": "/crm/project",
  "crm_target": "/crm/target",
  "dingtalk-callback": "/dingtalk-callback",
  "home": "/home",
  "hrm": "/hrm",
  "hrm_company-management": "/hrm/company-management",
  "hrm_file-management": "/hrm/file-management",
  "hrm_file-management_contract": "/hrm/file-management/contract",
  "hrm_roster": "/hrm/roster",
  "hrm_roster_abnormal": "/hrm/roster/abnormal",
  "hrm_roster_admission-conditions": "/hrm/roster/admission-conditions",
  "hrm_roster_black": "/hrm/roster/black",
  "hrm_roster_detail": "/hrm/roster/detail",
  "hrm_roster_entry-pending": "/hrm/roster/entry-pending",
  "hrm_roster_left": "/hrm/roster/left",
  "hrm_roster_left-company": "/hrm/roster/left-company",
  "hrm_roster_left-history": "/hrm/roster/left-history",
  "hrm_roster_on-job": "/hrm/roster/on-job",
  "hrm_roster_on-job-contract": "/hrm/roster/on-job-contract",
  "hrm_roster_probation": "/hrm/roster/probation",
  "hrm_roster_profile": "/hrm/roster/profile",
  "hrm_roster_promoted": "/hrm/roster/promoted",
  "hrm_roster_qrcode": "/hrm/roster/qrcode",
  "hrm_roster_resign-contract": "/hrm/roster/resign-contract",
  "hrm_roster_resume-launch": "/hrm/roster/resume-launch",
  "hrm_roster_review": "/hrm/roster/review",
  "hrm_roster_transfer": "/hrm/roster/transfer",
  "hrm_roster_user": "/hrm/roster/user",
  "hrm_signature-account": "/hrm/signature-account",
  "hrm_work-number": "/hrm/work-number",
  "iframe-page": "/iframe-page/:url",
  "kip": "/kip",
  "kip_bsc": "/kip/bsc",
  "kip_key-issues": "/kip/key-issues",
  "kip_task-center": "/kip/task-center",
  "login": "/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat)?",
  "system": "/system",
  "system_dept": "/system/dept",
  "system_dict": "/system/dict",
  "system_dict_dict-type": "/system/dict/dict-type",
  "system_menu": "/system/menu",
  "system_role": "/system/role",
  "system_user": "/system/user",
  "system_version": "/system/version"
};

/**
 * get route path by route name
 * @param name route name
 */
export function getRoutePath<T extends RouteKey>(name: T) {
  return routeMap[name];
}

/**
 * get route name by route path
 * @param path route path
 */
export function getRouteName(path: RoutePath) {
  const routeEntries = Object.entries(routeMap) as [RouteKey, RoutePath][];

  const routeName: RouteKey | null = routeEntries.find(([, routePath]) => routePath === path)?.[0] || null;

  return routeName;
}
