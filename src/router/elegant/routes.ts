/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type {GeneratedRoute} from '@elegant-router/types';

export const generatedRoutes: GeneratedRoute[] = [
  {
    name: '403',
    path: '/403',
    component: 'layout.blank$view.403',
    meta: {
      title: '403',
      i18nKey: 'route.403',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '404',
    path: '/404',
    component: 'layout.blank$view.404',
    meta: {
      title: '404',
      i18nKey: 'route.404',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '500',
    path: '/500',
    component: 'layout.blank$view.500',
    meta: {
      title: '500',
      i18nKey: 'route.500',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'crm',
    path: '/crm',
    component: 'layout.base',
    meta: {
      title: 'crm',
      i18nKey: 'route.crm',
      icon: 'mdi:account-star',
      order: 2,
      roles: ['crm', 'crm_admin', 'crm_common']
    },
    children: [
      {
        name: 'crm_customer',
        path: '/crm/customer',
        component: 'view.crm_customer',
        meta: {
          title: 'crm_customer',
          i18nKey: 'route.crm_customer',
          icon: 'mdi:account-multiple',
          order: 3
        }
      },
      {
        name: 'crm_follow',
        path: '/crm/follow',
        component: 'view.crm_follow',
        meta: {
          title: 'crm_follow',
          i18nKey: 'route.crm_follow',
          icon: 'mingcute:user-follow-2-line',
          order: 5
        }
      },
      {
        name: 'crm_plan',
        path: '/crm/plan',
        component: 'view.crm_plan',
        meta: {
          title: 'crm_plan',
          i18nKey: 'route.crm_plan',
          icon: 'icon-park-outline:plan',
          order: 4
        }
      },
      {
        name: 'crm_project',
        path: '/crm/project',
        component: 'view.crm_project',
        meta: {
          title: 'crm_project',
          i18nKey: 'route.crm_project',
          icon: 'mdi:projector-screen',
          order: 2
        }
      },
      {
        name: 'crm_target',
        path: '/crm/target',
        component: 'view.crm_target',
        meta: {
          title: 'crm_target',
          i18nKey: 'route.crm_target',
          icon: 'mdi:bank',
          order: 1
        }
      }
    ]
  },
  {
    name: 'dingtalk-callback',
    path: '/dingtalk-callback',
    component: 'layout.blank$view.dingtalk-callback',
    meta: {
      title: 'dingtalk-callback',
      i18nKey: 'route.dingtalk-callback',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'home',
    path: '/home',
    component: 'layout.base$view.home',
    meta: {
      title: 'home',
      i18nKey: 'route.home',
      icon: 'mdi:monitor-dashboard',
      order: 1
    }
  },
  {
    name: 'hrm',
    path: '/hrm',
    component: 'layout.base',
    meta: {
      title: 'hrm',
      i18nKey: 'route.hrm',
      order: 4
    },
    children: [
      {
        name: 'hrm_company-management',
        path: '/hrm/company-management',
        component: 'view.hrm_company-management',
        meta: {
          title: 'hrm_company-management',
          i18nKey: 'route.hrm_company-management'
        }
      },
      {
        name: 'hrm_file-management',
        path: '/hrm/file-management',
        meta: {
          title: 'hrm_file-management',
          i18nKey: 'route.hrm_file-management'
        },
        children: [
          {
            name: 'hrm_file-management_contract',
            path: '/hrm/file-management/contract',
            component: 'view.hrm_file-management_contract',
            meta: {
              title: 'hrm_file-management_contract',
              i18nKey: 'route.hrm_file-management_contract'
            }
          }
        ]
      },
      {
        name: 'hrm_roster',
        path: '/hrm/roster',
        meta: {
          title: 'hrm_roster',
          i18nKey: 'route.hrm_roster',
          icon: 'mdi:account-group'
        },
        children: [
          {
            name: 'hrm_roster_abnormal',
            path: '/hrm/roster/abnormal',
            component: 'view.hrm_roster_abnormal',
            meta: {
              title: 'hrm_roster_abnormal',
              i18nKey: 'route.hrm_roster_abnormal'
            }
          },
          {
            name: 'hrm_roster_admission-conditions',
            path: '/hrm/roster/admission-conditions',
            component: 'view.hrm_roster_admission-conditions',
            meta: {
              title: 'hrm_roster_admission-conditions',
              i18nKey: 'route.hrm_roster_admission-conditions',
              order: 1,
              icon: 'mdi:file-document-check'
            }
          },
          {
            name: 'hrm_roster_black',
            path: '/hrm/roster/black',
            component: 'view.hrm_roster_black',
            meta: {
              title: 'hrm_roster_black',
              i18nKey: 'route.hrm_roster_black',
              order: 4,
              icon: 'mdi:account-cancel'
            }
          },
          {
            name: 'hrm_roster_detail',
            path: '/hrm/roster/detail',
            component: 'view.hrm_roster_detail',
            meta: {
              title: 'hrm_roster_detail',
              hideInMenu: true,
              i18nKey: 'route.hrm_roster_detail',
              activeMenu: 'hrm_roster_user'
            }
          },
          {
            name: 'hrm_roster_entry-pending',
            path: '/hrm/roster/entry-pending',
            component: 'view.hrm_roster_entry-pending',
            meta: {
              title: 'hrm_roster_entry-pending',
              i18nKey: 'route.hrm_roster_entry-pending',
              order: 5,
              icon: 'mdi:account-clock'
            }
          },
          {
            name: 'hrm_roster_left',
            path: '/hrm/roster/left',
            component: 'view.hrm_roster_left',
            meta: {
              title: 'hrm_roster_left',
              i18nKey: 'route.hrm_roster_left',
              order: 8,
              icon: 'mdi:account-remove'
            }
          },
          {
            name: 'hrm_roster_left-company',
            path: '/hrm/roster/left-company',
            component: 'view.hrm_roster_left-company',
            meta: {
              title: 'hrm_roster_left-company',
              i18nKey: 'route.hrm_roster_left-company',
              order: 9,
              icon: 'mdi:account-off'
            }
          },
          {
            name: 'hrm_roster_left-history',
            path: '/hrm/roster/left-history',
            component: 'view.hrm_roster_left-history',
            meta: {
              title: 'hrm_roster_left-history',
              i18nKey: 'route.hrm_roster_left-history'
            }
          },
          {
            name: 'hrm_roster_on-job',
            path: '/hrm/roster/on-job',
            component: 'view.hrm_roster_on-job',
            meta: {
              title: 'hrm_roster_on-job',
              i18nKey: 'route.hrm_roster_on-job',
              order: 6,
              icon: 'mdi:account-check'
            }
          },
          {
            name: 'hrm_roster_on-job-contract',
            path: '/hrm/roster/on-job-contract',
            component: 'view.hrm_roster_on-job-contract',
            meta: {
              title: 'hrm_roster_on-job-contract',
              i18nKey: 'route.hrm_roster_on-job-contract',
              hideInMenu: true,
              activeMenu: 'hrm_roster_on-job'
            }
          },
          {
            name: 'hrm_roster_probation',
            path: '/hrm/roster/probation',
            component: 'view.hrm_roster_probation',
            meta: {
              title: 'hrm_roster_probation',
              i18nKey: 'route.hrm_roster_probation',
              order: 7,
              icon: 'mdi:account-question'
            }
          },
          {
            name: 'hrm_roster_profile',
            path: '/hrm/roster/profile',
            component: 'view.hrm_roster_profile',
            meta: {
              title: 'hrm_roster_profile',
              i18nKey: 'route.hrm_roster_profile'
            }
          },
          {
            name: 'hrm_roster_promoted',
            path: '/hrm/roster/promoted',
            component: 'view.hrm_roster_promoted',
            meta: {
              title: 'hrm_roster_promoted',
              i18nKey: 'route.hrm_roster_promoted',
              order: 11,
              icon: 'mdi:account-arrow-up'
            }
          },
          {
            name: 'hrm_roster_qrcode',
            path: '/hrm/roster/qrcode',
            component: 'view.hrm_roster_qrcode',
            meta: {
              title: 'hrm_roster_qrcode',
              i18nKey: 'route.hrm_roster_qrcode',
              order: 2,
              icon: 'mdi:link-variant'
            }
          },
          {
            name: 'hrm_roster_resign-contract',
            path: '/hrm/roster/resign-contract',
            component: 'view.hrm_roster_resign-contract',
            meta: {
              title: 'hrm_roster_resign-contract',
              i18nKey: 'route.hrm_roster_resign-contract'
            }
          },
          {
            name: 'hrm_roster_resume-launch',
            path: '/hrm/roster/resume-launch',
            component: 'view.hrm_roster_resume-launch',
            meta: {
              title: 'hrm_roster_resume-launch',
              i18nKey: 'route.hrm_roster_resume-launch'
            }
          },
          {
            name: 'hrm_roster_review',
            path: '/hrm/roster/review',
            component: 'view.hrm_roster_review',
            meta: {
              title: 'hrm_roster_review',
              hideInMenu: true,
              activeMenu: 'hrm_roster_entry-pending',
              i18nKey: 'route.hrm_roster_review'
            }
          },
          {
            name: 'hrm_roster_transfer',
            path: '/hrm/roster/transfer',
            component: 'view.hrm_roster_transfer',
            meta: {
              title: 'hrm_roster_transfer',
              i18nKey: 'route.hrm_roster_transfer',
              order: 10,
              icon: 'mdi:account-switch'
            }
          },
          {
            name: 'hrm_roster_user',
            path: '/hrm/roster/user',
            component: 'view.hrm_roster_user',
            meta: {
              title: 'hrm_roster_user',
              i18nKey: 'route.hrm_roster_user',
              order: 3,
              icon: 'mdi:human-male-female'
            }
          }
        ]
      },
      {
        name: 'hrm_signature-account',
        path: '/hrm/signature-account',
        component: 'view.hrm_signature-account',
        meta: {
          title: 'hrm_signature-account',
          i18nKey: 'route.hrm_signature-account'
        }
      },
      {
        name: 'hrm_work-number',
        path: '/hrm/work-number',
        component: 'view.hrm_work-number',
        meta: {
          title: 'hrm_work-number',
          i18nKey: 'route.hrm_work-number'
        }
      }
    ]
  },
  {
    name: 'iframe-page',
    path: '/iframe-page/:url',
    component: 'layout.base$view.iframe-page',
    props: true,
    meta: {
      title: 'iframe-page',
      i18nKey: 'route.iframe-page',
      constant: true,
      hideInMenu: true,
      keepAlive: true
    }
  },
  {
    name: 'kip',
    path: '/kip',
    component: 'layout.base',
    meta: {
      order: 3,
      title: 'kip',
      i18nKey: 'route.kip',
      icon: 'mdi:alert-circle',
      roles: ['kip']
    },
    children: [
      {
        name: 'kip_bsc',
        path: '/kip/bsc',
        component: 'view.kip_bsc',
        meta: {
          title: 'kip_bsc',
          i18nKey: 'route.kip_bsc',
          icon: 'ic:sharp-group-work'
        }
      },
      {
        name: 'kip_key-issues',
        path: '/kip/key-issues',
        component: 'view.kip_key-issues',
        meta: {
          order: 1,
          icon: 'pajamas:issue-type-feature-flag',
          title: 'kip_key-issues',
          i18nKey: 'route.kip_key-issues'
        }
      },
      {
        name: 'kip_task-center',
        path: '/kip/task-center',
        component: 'view.kip_task-center',
        meta: {
          title: 'kip_task-center',
          i18nKey: 'route.kip_task-center'
        }
      }
    ]
  },
  {
    name: 'login',
    path: '/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat)?',
    component: 'layout.blank$view.login',
    props: true,
    meta: {
      title: 'login',
      i18nKey: 'route.login',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'system',
    path: '/system',
    component: 'layout.base',
    meta: {
      title: 'system',
      i18nKey: 'route.system'
    },
    children: [
      {
        name: 'system_dept',
        path: '/system/dept',
        component: 'view.system_dept',
        meta: {
          title: 'system_dept',
          i18nKey: 'route.system_dept'
        }
      },
      {
        name: 'system_dict',
        path: '/system/dict',
        component: 'view.system_dict',
        meta: {
          title: 'system_dict',
          i18nKey: 'route.system_dict'
        },
        children: [
          {
            name: 'system_dict_dict-type',
            path: '/system/dict/dict-type',
            component: 'view.system_dict_dict-type',
            meta: {
              title: 'system_dict_dict-type',
              i18nKey: 'route.system_dict_dict-type'
            }
          }
        ]
      },
      {
        name: 'system_menu',
        path: '/system/menu',
        component: 'view.system_menu',
        meta: {
          title: 'system_menu',
          i18nKey: 'route.system_menu'
        }
      },
      {
        name: 'system_role',
        path: '/system/role',
        component: 'view.system_role',
        meta: {
          title: 'system_role',
          i18nKey: 'route.system_role'
        }
      },
      {
        name: 'system_user',
        path: '/system/user',
        component: 'view.system_user',
        meta: {
          title: 'system_user',
          i18nKey: 'route.system_user'
        }
      },
      {
        name: 'system_version',
        path: '/system/version',
        component: 'view.system_version',
        meta: {
          title: 'system_version',
          i18nKey: 'route.system_version'
        }
      }
    ]
  }
];
