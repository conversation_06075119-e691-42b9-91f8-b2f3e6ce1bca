/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { RouteComponent } from "vue-router";
import type { LastLevelRouteKey, RouteLayout } from "@elegant-router/types";

import BaseLayout from "@/layouts/base-layout/index.vue";
import BlankLayout from "@/layouts/blank-layout/index.vue";

export const layouts: Record<RouteLayout, RouteComponent | (() => Promise<RouteComponent>)> = {
  base: BaseLayout,
  blank: BlankLayout,
};

export const views: Record<LastLevelRouteKey, RouteComponent | (() => Promise<RouteComponent>)> = {
  403: () => import("@/views/_builtin/403/index.vue"),
  404: () => import("@/views/_builtin/404/index.vue"),
  500: () => import("@/views/_builtin/500/index.vue"),
  "dingtalk-callback": () => import("@/views/_builtin/dingtalk-callback/index.vue"),
  "iframe-page": () => import("@/views/_builtin/iframe-page/[url].vue"),
  login: () => import("@/views/_builtin/login/index.vue"),
  crm_customer: () => import("@/views/crm/customer/index.vue"),
  crm_follow: () => import("@/views/crm/follow/index.vue"),
  crm_plan: () => import("@/views/crm/plan/index.vue"),
  crm_project: () => import("@/views/crm/project/index.vue"),
  crm_target: () => import("@/views/crm/target/index.vue"),
  home: () => import("@/views/home/<USER>"),
  "hrm_company-management": () => import("@/views/hrm/company-management/index.vue"),
  "hrm_file-management_contract": () => import("@/views/hrm/file-management/contract/index.vue"),
  hrm_roster_abnormal: () => import("@/views/hrm/roster_abnormal/index.vue"),
  "hrm_roster_admission-conditions": () => import("@/views/hrm/roster_admission-conditions/index.vue"),
  hrm_roster_black: () => import("@/views/hrm/roster_black/index.vue"),
  hrm_roster_detail: () => import("@/views/hrm/roster_detail/index.vue"),
  "hrm_roster_entry-pending": () => import("@/views/hrm/roster_entry-pending/index.vue"),
  hrm_roster_export: () => import("@/views/hrm/roster_export/index.vue"),
  "hrm_roster_left-company": () => import("@/views/hrm/roster_left-company/index.vue"),
  "hrm_roster_left-history": () => import("@/views/hrm/roster_left-history/index.vue"),
  hrm_roster_left: () => import("@/views/hrm/roster_left/index.vue"),
  "hrm_roster_on-job-contract": () => import("@/views/hrm/roster_on-job-contract/index.vue"),
  "hrm_roster_on-job": () => import("@/views/hrm/roster_on-job/index.vue"),
  hrm_roster_probation: () => import("@/views/hrm/roster_probation/index.vue"),
  hrm_roster_profile: () => import("@/views/hrm/roster_profile/index.vue"),
  hrm_roster_promoted: () => import("@/views/hrm/roster_promoted/index.vue"),
  hrm_roster_qrcode: () => import("@/views/hrm/roster_qrcode/index.vue"),
  "hrm_roster_resign-contract": () => import("@/views/hrm/roster_resign-contract/index.vue"),
  "hrm_roster_resume-launch": () => import("@/views/hrm/roster_resume-launch/index.vue"),
  hrm_roster_review: () => import("@/views/hrm/roster_review/index.vue"),
  hrm_roster_transfer: () => import("@/views/hrm/roster_transfer/index.vue"),
  hrm_roster_user: () => import("@/views/hrm/roster_user/index.vue"),
  "hrm_signature-account": () => import("@/views/hrm/signature-account/index.vue"),
  "hrm_work-number": () => import("@/views/hrm/work-number/index.vue"),
  kip_bsc: () => import("@/views/kip/bsc/index.vue"),
  "kip_key-issues": () => import("@/views/kip/key-issues/index.vue"),
  "kip_task-center": () => import("@/views/kip/task-center/index.vue"),
  system_dept: () => import("@/views/system/dept/index.vue"),
  "system_dict_dict-type": () => import("@/views/system/dict/dict-type/index.vue"),
  system_dict: () => import("@/views/system/dict/index.vue"),
  system_menu: () => import("@/views/system/menu/index.vue"),
  system_role: () => import("@/views/system/role/index.vue"),
  system_user: () => import("@/views/system/user/index.vue"),
  system_version: () => import("@/views/system/version/index.vue"),
};
