import dayjs from 'dayjs';

/**
 * 根据传入的月份字符串（格式 "YYYY-MM"）分割成 4 个区间
 *
 * - 前三周固定为 7 天
 * - 最后一周从最后一周开始日期延伸到当月最后一天
 *
 * @param monthStr 月份字符串，例如 "2025-05"
 * @returns 每个区间包含 week、start 和 end 属性
 */
export function splitMonthIntoWeeks(monthStr: string) {
  // 拼接为月份的第一天
  const startDate = dayjs(`${monthStr}-01`);
  // 当月最后一天
  const monthEnd = startDate.endOf('month');
  const weeks: Array<{ week: number; start: string; end: string }> = [];

  let currentWeekStart = startDate;
  // 前三周固定7天
  for (let i = 0; i < 3; i++) {
    const currentWeekEnd = currentWeekStart.add(6, 'day');
    weeks.push({
      start: currentWeekStart.format('YYYY-MM-DD'),
      end: currentWeekEnd.format('YYYY-MM-DD')
    });
    currentWeekStart = currentWeekEnd.add(1, 'day');
  }
  // 最后一周从当前开始日期延伸到当月最后一天
  weeks.push({
    start: currentWeekStart.format('YYYY-MM-DD'),
    end: monthEnd.format('YYYY-MM-DD')
  });

  return weeks;
}

export function formatTimestampRange(range?: [number, number]): [string | undefined, string | undefined] {
  if (!Array.isArray(range) || range.length !== 2) {
    return [undefined, undefined];
  }

  const [start, end] = range;
  const startStr = dayjs(start).startOf('day').format('YYYY-MM-DD HH:mm:ss');
  const endStr = dayjs(end).endOf('day').format('YYYY-MM-DD HH:mm:ss');
  return [startStr, endStr];
}

export function calculateTrialEndDate(entry: string, probation: number | string | null): string | null {
  if (!entry || isNaN(probation)) return null;

  const entryDay = dayjs(entry);
  const specialProbation = [2, 3, 4, 8];

  if (!specialProbation.includes(probation)) {
    // 通用规则
    return entryDay.add(probation - 1, 'month').format('YYYY-MM-DD');
  }

  const dayOfMonth = entryDay.date();

  // 特殊规则
  const adjustedDate = entryDay.add(probation - 1, 'month');

  if (dayOfMonth <= 15) {
    return adjustedDate.set('date', 15).format('YYYY-MM-DD');
  }

  return adjustedDate.endOf('month').format('YYYY-MM-DD');
}
