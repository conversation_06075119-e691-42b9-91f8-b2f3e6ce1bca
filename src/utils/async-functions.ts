import { businessTypeOptions } from '@/enum';
import {
  fetchGetCustomerSelect,
  fetchGetPlanSelect,
  fetchGetProjectList,
  fetchGetTargetSelect,
  fetchGetUserListByRole,
  fetchGetUserSelect,
  getDictDataOptionsApi,
  getUserListByDeptApi
} from '@/service/api';
import { fetchPutCompanyOptions } from '@/service/api/hrm/company-management';
import { fetchGetPositionOptions } from '@/service/api/hrm/roster_qrcode';
import { fetchMetricsOptions, fetchTargetOptions } from '@/service/api/kip/bsc';
import { fetchGetDeptSimpleList } from '@/service/api/kip/key-issues';
import { fetchGetRoleList } from '@/service/api/system/role';
import { filterEmptyParams, getEnumValue, showProjectStr, showUserStr } from '@/utils/useful_func';

// 项目列表
export async function getProjectOptions(param: string, page: number = 1): Promise<CommonType.Option<number>[]> {
  const params = { bankName: param, pageNo: page, all: false };
  const { error, data } = await fetchGetProjectList(params);
  if (error) {
    return [];
  }

  return data.list.map((item: Api.Crm.Project) => ({
    label: showProjectStr(item),
    value: item.id
  }));
}

// 部门列表
export async function getDeptOptions(param: string): Promise<CommonType.Option<number>[]> {
  const params = { name: param };
  const { error, data } = await fetchGetDeptSimpleList(params);
  if (error) {
    return [];
  }
  return data.map((item: Api.Kip.Dept) => ({
    label: item.name,
    value: item.id
  }));
}
// 战略指标列表
export async function getBscOptions(param: string): Promise<CommonType.Option<number>[]> {
  const { error, data } = await fetchMetricsOptions({ name: param });

  if (error) {
    return [];
  }
  return data.map(item => ({
    label: `${item.sort ?? ''}${item.name}`,
    value: item.id
  }));
}
// 战略指标列表
export async function getBscTargetOptions(param: string, year: number): Promise<CommonType.Option<number>[]> {
  const { error, data } = await fetchTargetOptions({ name: param, year });

  if (error) {
    return [];
  }
  return data.map(item => ({
    label: item,
    value: item
  }));
}
// 联络人列表
// eslint-disable-next-line max-params
export async function getCustomerOptions(
  param: string,
  projectId: number,
  planId?: number,
  page: number = 1
): Promise<CommonType.Option<number>[]> {
  const params: Api.Crm.CustomerSearchParams = { name: param, projectId, pageNo: page };
  if (planId) {
    params.planId = planId;
  }
  const { error, data } = await fetchGetCustomerSelect(filterEmptyParams(params));
  if (error) {
    return [];
  }
  return data.map((item: Api.Crm.Customer) => ({
    label: item.name,
    value: item.id
  }));
}

// 所有用户列表
export async function getUserOptions(param: string, page: number = 1): Promise<CommonType.Option<number>[]> {
  const { error, data } = await fetchGetUserSelect(filterEmptyParams({ nickname: param, pageNo: page }));
  if (error) {
    return [];
  }
  return (data || []).map((item: Api.SystemManage.User) => ({
    label: showUserStr(item),
    value: item.id
  }));
}
// 所有用户列表后缀带部门
export async function getUserOptionsWithDept(param: string, page: number = 1): Promise<CommonType.Option<number>[]> {
  const { error, data } = await fetchGetUserSelect(filterEmptyParams({ nickname: param, pageNo: page }));
  if (error) {
    return [];
  }
  return (data || []).map((item: Api.SystemManage.User) => ({
    label: showUserStr(item),
    value: item.id
  }));
}

// 通过角色获取用户列表
export async function getUserByRoleOptions(param: string, page: number = 1): Promise<CommonType.Option<number>[]> {
  const { error, data } = await fetchGetUserListByRole(filterEmptyParams({ nickname: param, pageNo: page }));
  if (error) {
    return [];
  }
  return (data || []).map((item: Api.SystemManage.User) => ({
    label: showUserStr(item),
    value: item.id
  }));
}

// 计划列表
export async function getPlanOptions(
  param: string,
  projectId: number,
  page: number = 1
): Promise<CommonType.Option<number>[]> {
  const { error, data } = await fetchGetPlanSelect(filterEmptyParams({ planComment: param, projectId, pageNo: page }));
  if (error) {
    return [];
  }
  return data.map((item: Api.Crm.Plan) => ({
    label: `${item.planComment}(${item.planDate})`,
    value: item.id,
    customers: item.customers
  }));
}

// 目标客户
export async function getTargetCustomerOptions(param: string, page: number = 1): Promise<CommonType.Option<number>[]> {
  const { error, data } = await fetchGetTargetSelect(filterEmptyParams({ name: param, pageNo: page }));
  if (error) {
    return [];
  }
  return data.map((item: Api.Crm.Target) => {
    const businessType = getEnumValue(businessTypeOptions, item.businessType);
    return {
      label: `${item.name}(${businessType})`,
      value: item.id,
      name: item.name,
      businessType
    };
  });
}
export async function getFormatDictData(params: {
  keys: string;
}): Promise<{ data: Record<string, Api.System.FormattedOption[]> }> {
  const { error, data } = await getDictDataOptionsApi(params);

  if (error) {
    return { data: {} };
  }

  const result: Record<string, Api.System.FormattedOption[]> = {};

  for (const key in data) {
    if (Array.isArray(data[key])) {
      result[key] = data[key].map((item: Api.System.OptionItem) => ({
        label: item.value,
        value: item.key
      }));
    }
  }

  return { data: result };
}
// 获取职位下拉框
export async function getAllPositionOptions(param: string): Promise<CommonType.Option<number>[]> {
  const { error, data } = await fetchGetPositionOptions({ name: param });
  if (error) {
    return [];
  }
  return (data || []).map((item: Api.Hrm.IdNameLongObject) => ({
    label: item.name,
    value: item.id
  }));
}
// 部门下的员工
export async function getUserListByDeptOptions(param: string, page: number = 1): Promise<CommonType.Option<number>[]> {
  const { error, data } = await getUserListByDeptApi({ nickname: param, pageNo: page });
  if (error) {
    return [];
  }
  return (data.list || []).map((item: Api.System.UserSimpleRespVO) => ({
    label: item.nickname,
    deptName: item.depts.map(dept => dept.deptName).join(','),
    avatar: item.avatar,
    value: item.id,
    externalUserId: item.externalUserId
  }));
}
// 获取角色列表
export async function getRoleListOptions(param: string, page: number = 1): Promise<CommonType.Option<number>[]> {
  const { error, data } = await fetchGetRoleList({ name: param || null, pageNo: page });
  if (error) {
    return [];
  }
  return (data.list || []).map((item: Api.SystemManage.Role) => ({
    label: item.name,
    value: item.id
  }));
}

// 钉钉部门列表
export async function getDeptByDeptOptions(param: string): Promise<CommonType.Option<number>[]> {
  const params = { name: param };
  const { error, data } = await fetchGetDeptSimpleList(params);
  if (error) {
    return [];
  }
  return data.map((item: Api.Kip.Dept) => ({
    label: item.name,
    value: item.id,
    externalDeptId: item.externalDeptId
  }));
}
// 获取公司下拉选项
export async function getCompanyOptions(): Promise<CommonType.Option<number>[]> {
  const { error, data } = await fetchPutCompanyOptions();
  if (error) {
    return [];
  }
  return (data || []).map((item: Api.Hrm.IdNameLongObject) => ({
    label: item.name,
    value: item.id
  }));
}
