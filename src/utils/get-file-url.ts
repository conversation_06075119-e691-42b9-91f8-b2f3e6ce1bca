// 直接使用后台接口地址
export function getProxyUrl(url: string): string {
  return `${import.meta.env.MODE === 'dev' ? '/api' : ''}${url}`;
}

export function getFileUrl(fileId: number): string {
  return getProxyUrl(`/admin-api/infra/file/${fileId}`);
}

export async function urlToFile(url: string): Promise<File> {
  const response = await fetch(url);
  const blob = await response.blob();

  // 优先从响应头中获取文件名
  let filename = 'unknown';
  const disposition = response.headers.get('Content-Disposition');

  if (disposition) {
    const match = disposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
    if (match != null) {
      filename = decodeURIComponent(match[1].replace(/['"]/g, ''));
    }
  }

  // 回退：尝试从 URL 中获取文件名
  if (filename === 'unknown') {
    try {
      const pathname = new URL(url).pathname;
      filename = decodeURIComponent(pathname.substring(pathname.lastIndexOf('/') + 1)) || 'unknown';
    } catch {}
  }

  return new File([blob], filename, { type: blob.type });
}

// 获取文件大小
export function formatFileSize(bytes: number, decimals = 2): string {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));
  const size = Number.parseFloat((bytes / k ** i).toFixed(dm));
  return `${size} ${sizes[i]}`;
}
