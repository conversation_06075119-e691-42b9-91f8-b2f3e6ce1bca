export function isPC() {
  const agents = ['Android', 'iPhone', 'webOS', 'BlackBerry', 'SymbianOS', 'Windows Phone', 'iPad', 'iPod'];

  const isMobile = agents.some(agent => window.navigator.userAgent.includes(agent));

  return !isMobile;
}

// 工具函数：通过路径获取嵌套值
function getValueByPath<T>(obj: T, path: string): any {
  return path.split('.').reduce((acc, key) => acc?.[key], obj);
}
// eslint-disable-next-line max-params
export function rowSpan<T>(key: string, rowIndex: number, _rowData: T, tableData: T[]): number {
  const currentValue = getValueByPath(tableData[rowIndex], key);
  const prevValue = getValueByPath(tableData[rowIndex - 1], key);

  if (rowIndex > 0 && currentValue === prevValue) {
    return 0;
  }

  let count = 1;
  for (let i = rowIndex + 1; i < tableData.length; i++) {
    if (getValueByPath(tableData[i], key) === currentValue) {
      count++;
    } else {
      break;
    }
  }

  return count;
}
