import type { PaginationInfo } from 'naive-ui';

import { $t } from '@/locales';

export const propsOverRides = {
  ProSearchForm: {
    labelPlacement: 'left',
    cols: '1 s:3 l:4'
  },
  ProDataTable: {
    pagination: {
      pageSizes: [10, 20, 30, 40, 50],
      showSizePicker: true,
      prefix: (info: PaginationInfo) => $t('datatable.itemCount', { total: info.itemCount })
    }
  },
  ProModalForm: {
    draggable: false
  }
};
