import type { EnumOption } from '@/enum';
import { getEnumOptionInfo } from '@/enum';
type DictItem = {
  label: string;
  value: string | number;
  data?: any;
};
export function showUserStr(info?: Api.SystemManage.User | null): string {
  let name = info?.nickname;
  if (info?.deleted) {
    name += '(已离职)';
  }
  return name || '-';
}

export function showCustomerStr(info?: Api.Crm.Customer): string {
  return info?.name || '-';
}

export function showProjectStr(info?: Api.Crm.Project): string {
  const name = `${info?.bankName}(${info?.businessType})`;
  return name || '-';
}

/**
 * 过滤空参数
 *
 * @param params
 */
export function filterEmptyParams(params: object): object {
  return Object.fromEntries(Object.entries(params).filter(([_, v]) => v));
}

// 获取枚举选项的标签类型
export function getEnumTagType(enumObj: EnumOption[], status: number | string): NaiveUI.ThemeColor {
  const one = getEnumOptionInfo(enumObj, status);
  if (one) {
    return one.type as NaiveUI.ThemeColor;
  }
  return 'default';
}

// 获取枚举选项的标签文字
export function getEnumValue(enumObj: EnumOption[], status: number | string): string {
  const one = getEnumOptionInfo(enumObj, status);
  if (one) {
    return String(one.label);
  }
  return '';
}
// 钉钉跳转登录
export function handleDingtalkLogin() {
  const url = `https://login.dingtalk.com/oauth2/auth?redirect_uri=${encodeURIComponent(window.location.origin + import.meta.env.VITE_LOGIN_CALLBACK_URI)}&response_type=code&client_id=${import.meta.env.VITE_CLIENT_ID}&scope=openid%20corpid&prompt=consent&corpId=${import.meta.env.VITE_CORP_ID}`;
  window.open(url, '_self');
}

export function getAppTitle() {
  return import.meta.env.VITE_APP_TITLE;
}

export function getLabelByValue(dict: DictItem[], value: string | number | undefined): string {
  if (value === undefined || value === null) return '';
  const item = dict.find(i => i.value == value);
  return item?.label ?? '';
}
