import { NAvatar, NText, type SelectRenderLabel, type SelectRenderTag } from 'naive-ui';

import Avatar from '@/assets/imgs/avatar.png';

const renderSingleSelectTag: SelectRenderTag = ({ option }) => {
  return h(
    'div',
    {
      style: {
        display: 'flex',
        alignItems: 'center'
      }
    },
    [
      h(NAvatar, {
        src: option.avatar ? (option.avatar as string) : Avatar,
        round: true,
        size: 24,
        style: {
          marginRight: '12px'
        }
      }),
      option.label as string
    ]
  );
};

const renderLabel: SelectRenderLabel = option => {
  return h(
    'div',
    {
      style: {
        display: 'flex',
        alignItems: 'center',
        overflow: 'hidden'
      }
    },
    [
      h(NAvatar, {
        src: option.avatar ? (option.avatar as string) : Avatar,
        round: true,
        size: 'small',
        style: {
          flex: 'none'
        }
      }),
      h(
        'div',
        {
          style: {
            marginLeft: '12px',
            overflow: 'hidden'
          }
        },
        [
          h(
            'div',
            {
              style: {
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis'
              }
            },
            [option.label as string]
          ),
          h(
            NText,
            {
              depth: 3,
              tag: 'div',
              style: {
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                fontSize: '12px'
              }
            },
            {
              default: () => option.deptName
            }
          )
        ]
      )
    ]
  );
};
export { renderLabel, renderSingleSelectTag };
