import { defineStore } from 'pinia';
import { reactive, ref } from 'vue';
import { fetchGetMarkersProject } from '@/service/api';
import { ReviewStatus } from '@/enum';

export const useProjectMarkerStore = defineStore('project-marker-store', () => {
  // 里程碑（立项）
  const info = reactive<CommonType.RecordNullable<Api.Crm.Marker>>({
    id: null,
    scene: null,
    confirmComment: null,
    confirmResult: null,
    projectId: null,
    tiggerComment: null
  });

  const confirmResult = ref(ReviewStatus.Reviewing);
  const isMarker = ref(false);
  // 初始化
  function init() {
    Object.keys(info).forEach((key: string) => {
      info[key as keyof typeof info] = null;
    });
  }

  function setProjectId(id: number) {
    info.projectId = id;
  }

  // 展示结果
  function showResult() {
    return confirmResult.value !== ReviewStatus.Reviewing && info.id;
  }

  // 获取里程碑详情
  async function getInfo() {
    const { error, data } = await fetchGetMarkersProject({ projectId: Number(info.projectId), scene: 1 });
    if (error) {
      return;
    }
    Object.assign(info, data);
    confirmResult.value = data?.confirmResult;
  }

  // 更改是否立项
  function setIsMarker(value: boolean) {
    isMarker.value = value;
  }
  return {
    init,
    isMarker,
    setIsMarker,
    info,
    getInfo,
    showResult,
    setProjectId,
    confirmResult
  };
});
