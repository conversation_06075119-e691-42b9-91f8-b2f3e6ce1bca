import { defineStore } from 'pinia';
import { ref } from 'vue';
import { useProjectStore } from '@/store/modules/project';

export const useVerifyStore = defineStore('verify-store', () => {
  const verify = ref({
    bankName: '合作方基本信息-银行名称',
    city: '合作方基本信息-城市',
    businessType: '合作方基本信息-业务类型',
    totalScale: '合作方基本信息-总规模',
    workplaceType: '合作方基本信息-职场类型',
    baseMode: '合作方基本信息-基地规模',
    requirement: '合作方基本信息-职场城市要求',
    avgRevenueMonth: '结算相关确认-本业务现有供应商月人均营收',
    reconSettleCycle: '结算相关确认-对账及结算周期'
  });

  const isShowVerify = ref(false);
  const verifyText = ref('');

  async function validateFields(showMessage = false) {
    const projectStore = useProjectStore();
    await projectStore.getInfo();

    for (const key in verify.value) {
      if (Object.hasOwn(verify.value, key)) {
        const typedKey = key as keyof Api.Crm.ProjectDetail;
        const fieldValue = projectStore.info[typedKey];

        if (fieldValue === null || fieldValue === undefined || fieldValue === '') {
          isShowVerify.value = true;
          verifyText.value = `发起立项时 ${verify.value[typedKey]} 不能为空`;

          if (showMessage) {
            window?.$message?.error?.(verifyText.value);
          }
          return;
        }
      }
    }
    isShowVerify.value = false;
    verifyText.value = '';
  }

  function init() {
    isShowVerify.value = false;
    verifyText.value = '';
  }

  return {
    init,
    isShowVerify,
    verifyText,
    setShowVerify: () => validateFields(false),
    clickShowVerify: () => validateFields(true)
  };
});
