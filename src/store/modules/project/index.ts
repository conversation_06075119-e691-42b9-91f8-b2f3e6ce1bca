import { defineStore } from 'pinia';
import { reactive, ref } from 'vue';
import { fetchGetProjectDetail } from '@/service/api';

export const useProjectStore = defineStore('project-store', () => {
  const info = reactive<CommonType.RecordNullable<Api.Crm.ProjectDetail>>({
    // 项目基本信息
    targetCustomerId: null,
    averageRevenue: null,
    bankName: null,
    baseMode: null,
    biddingTime: null,
    businessType: null,
    middleman: null,
    city: null,
    contractEndTime: null,
    entryCountries: null,
    entryCountriesHost: null,
    entryCountriesPrepare: null,
    expectedStartTime: null,
    partnerDuration: null,
    singleEstimatedValue: null,
    status: null,
    totalScale: null,
    ownerUserId: null,
    winningProbability: null,
    // 服务内容确定
    workplaceType: null,
    requirement: null,
    supplierInfo: null,
    firstBatchSeats: null,
    projectStartDate: null,
    projectOnlineDate: null,
    schedulingReq: null,
    trainingCycle: null,
    trainingSupport: null,

    // 数据确认
    listAllocRule: null,
    merchantDistRule: null,

    //  团队配置要求
    mgmtStaffRatio: null,

    // 职场配置
    exitWorkplaceCost: null,

    // 招标确认
    biddingProcess: null,
    supplyQty: null,
    biddingKeyPoints: null,
    contractYears: null,

    // 结算相关确认
    settlementRule: null,
    listQuantity: null,
    connectRate: null,
    callMethodLimit: null,
    connectQty: null,
    avgSuccessCnt: null,
    refundRate: null,
    avgOrder: null,
    avgPerfMonth: null,
    avgRevenueMonth: null,
    settlementFactor: null,
    settlementStd: null,
    penaltyRisk: null,
    supportPolicy: null,
    systemCost: null,
    callCost: null,
    workplaceCost: null,
    rankingRule: null,
    reconSettleCycle: null,
    invoiceType: null,
    competitorProfit: null,
    otherNotes: null,
    remark: null,
    connectConv: null
  });

  const isCreate = ref(false);

  // 初始化
  function init() {
    Object.keys(info).forEach((key: string) => {
      info[key as keyof typeof info] = null;
    });
    isCreate.value = true;
  }

  // 获取项目详情
  async function getInfo() {
    if (!info.projectId) {
      return;
    }

    const { error, data } = await fetchGetProjectDetail(info.projectId);
    if (error) {
      return;
    }
    if (Number(data.bankType) === 0) {
      data.bankType = null;
    }
    Object.assign(info, data);
  }

  // 设置项目id，用于判断是否为新建项目
  function setProjectId(id: number) {
    info.projectId = id;
    info.id = id;
    isCreate.value = Boolean(!id);
  }

  // 是否为新建项目
  function getIsCreate(): boolean {
    return isCreate.value;
  }

  // 提交表单前检查
  function submitCheck() {
    if (!info.projectId) {
      window.$message?.error('请先完善项目基本信息');
      throw new Error('数据不合法');
    }
  }

  return {
    info,
    init,
    getInfo,
    getIsCreate,
    setProjectId,
    submitCheck
  };
});
