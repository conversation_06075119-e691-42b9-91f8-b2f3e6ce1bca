import { defineStore } from 'pinia';
import { reactive } from 'vue';
import { fetchGetPlanDetail } from '@/service/api';
import { jsonClone } from '~/packages/utils/src/klona';

export const usePlanStore = defineStore('plan-store', () => {
  const info = reactive<CommonType.RecordNullable<Api.Crm.Plan>>({
    planComment: '',
    planDate: '',
    planStatus: 0,
    cancelReason: '',
    userId: null,
    user: null,
    customers: null,
    customerIds: null,
    projectId: null,
    project: null
  });

  const initInfo = jsonClone({ ...info });

  function init() {
    Object.assign(info, initInfo);
  }

  async function getInfo(id: number) {
    const { error, data } = await fetchGetPlanDetail(id);
    if (error) {
      return;
    }
    Object.assign(info, data);
  }

  return {
    info,
    init,
    getInfo
  };
});
