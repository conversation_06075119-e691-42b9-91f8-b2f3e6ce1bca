// store/modules/dict.ts
import { defineStore } from 'pinia';

import { SetupStoreId } from '@/enum';
import { getDictDataOptionsApi } from '@/service/api'; // 你已有的接口
export interface DictItem<T = any> {
  key: string | number;
  value: string;
  data?: T; // 可选字段，支持扩展元信息（如是否禁用、排序等）
}
type DictMap = Record<string, DictItem[]>;

export const useDictStore = defineStore(SetupStoreId.Dict, {
  state: (): { dictMap: DictMap } => ({
    dictMap: {}
  }),
  actions: {
    hasDict(key: string) {
      return Boolean(this.dictMap[key]);
    },
    getDict(key: string) {
      return this.dictMap[key] || [];
    },
    async fetchDicts(keys: string[]) {
      const { data } = await getDictDataOptionsApi({ keys: keys.join(',') });
      if (data) {
        Object.assign(this.dictMap, data);
      }
    }
  }
});
