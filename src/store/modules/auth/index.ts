import { useLoading } from '@sa/hooks';
import { defineStore } from 'pinia';
import { computed, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';

import { useRouteStore } from '../route';
import { useTabStore } from '../tab';

import { clearAuthStorage, getToken } from './shared';

import { SetupStoreId } from '@/enum';
import { useRouterPush } from '@/hooks/common/router';
import { $t } from '@/locales';
import { fetchGetPermissionInfo, fetchGetUserInfo, fetchLogin, socialLogin } from '@/service/api';
import { localStg } from '@/utils/storage';

export const useAuthStore = defineStore(SetupStoreId.Auth, () => {
  const route = useRoute();
  const routeStore = useRouteStore();
  const tabStore = useTabStore();
  const { toLogin, redirectFromLogin } = useRouterPush(false);
  const { loading: loginLoading, startLoading, endLoading } = useLoading();

  const token = ref(getToken());

  const userInfo = reactive<Api.SystemManage.UserProfileRespVO>(
    <Api.SystemManage.UserProfileRespVO>localStg.get('userInfo') || {
      avatar: '',
      createTime: undefined,
      dept: undefined,
      email: '',
      id: 0,
      loginDate: undefined,
      loginIp: '',
      mobile: '',
      nickname: '',
      posts: [],
      roles: [],
      sex: 0,
      socialUsers: [],
      username: ''
    }
  );
  const defaultPermissions: Api.SystemManage.AuthPermissionInfoRespVO = reactive(
    localStg.get('defaultPermissions') || {
      menus: [],
      permissions: [],
      roles: [],
      user: {
        avatar: '',
        deptId: 0,
        id: 0,
        nickname: ''
      }
    }
  );
  const permissions = computed(() => defaultPermissions.permissions);
  const roles = computed(() => defaultPermissions.roles);
  const menus = computed(() => defaultPermissions.menus);
  /** is super role in static route */
  const isStaticSuper = computed(() => {
    const { VITE_AUTH_ROUTE_MODE, VITE_STATIC_SUPER_ROLE } = import.meta.env;
    return VITE_AUTH_ROUTE_MODE === 'static' && roles.value.includes(VITE_STATIC_SUPER_ROLE);
  });
  /** Is login */
  const isLogin = computed(() => Boolean(token.value));

  /** Reset auth store */
  async function resetStore() {
    const authStore = useAuthStore();

    clearAuthStorage();

    authStore.$reset();

    if (!route.meta.constant) {
      await toLogin();
    }

    tabStore.cacheTabs();
    routeStore.resetStore();
  }

  /**
   * Login
   *
   * @param userName User name
   * @param password Password
   * @param captchaVerification Captcha verification
   * @param captchaEnable Captcha enable
   * @param [redirect=true] Whether to redirect after login. Default is `true`
   */
  // eslint-disable-next-line max-params
  async function login(
    userName: string,
    password: string,
    captchaVerification: string,
    captchaEnable: string,
    redirect = true
  ) {
    startLoading();

    const { data: loginToken, error } = await fetchLogin(userName, password, captchaVerification, captchaEnable);
    if (!error) {
      localStg.set('loginType', 'pwd');
      const pass = await loginByToken(loginToken);
      if (pass) {
        await routeStore.initAuthRoute();

        await redirectFromLogin(redirect);

        if (routeStore.isInitAuthRoute) {
          window.$notification?.success({
            title: $t('page.login.common.loginSuccess'),
            content: $t('page.login.common.welcomeBack', { userName: userInfo.nickname }),
            duration: 2000
          });
        }
      }
    } else {
      resetStore();
    }

    endLoading();
  }

  async function handleSocialLogin(param: { code: string }) {
    startLoading();
    const { data: loginToken, error } = await socialLogin(param);
    if (!error) {
      localStg.set('loginType', 'dingtalk');
      const pass = await loginByToken(loginToken);
      if (pass) {
        await routeStore.initAuthRoute();

        await redirectFromLogin(true);

        if (routeStore.isInitAuthRoute) {
          window.$notification?.success({
            title: $t('page.login.common.loginSuccess'),
            content: $t('page.login.common.welcomeBack', { userName: userInfo.nickname }),
            duration: 2000
          });
        }
      }
    } else {
      resetStore();
    }

    endLoading();
  }

  async function loginByToken(loginToken: Api.Auth.LoginToken) {
    // 1. stored in the localStorage, the later requests need it in headers
    localStg.set('accessToken', loginToken.accessToken);
    localStg.set('refreshToken', loginToken.refreshToken);

    // 2. get user info
    const pass = await getUserInfo();

    if (pass) {
      token.value = loginToken.accessToken;

      return true;
    }

    return false;
  }

  async function getUserInfo() {
    const { data: info, error } = await fetchGetUserInfo();
    if (!error) {
      Object.assign(userInfo, info);
      localStg.set('userInfo', userInfo);
      return true;
    }

    return false;
  }

  async function getPermissionInfo() {
    const { data, error } = await fetchGetPermissionInfo();
    if (!error) {
      Object.assign(defaultPermissions, data);
      localStg.set('defaultPermissions', data);
      return true;
    }
    return false;
  }

  async function initUserInfo() {
    const hasToken = getToken();

    if (hasToken) {
      const pass = await getUserInfo();
      if (!pass) {
        resetStore();
      }
    }
  }

  return {
    token,
    userInfo,
    roles,
    menus,
    permissions,
    isStaticSuper,
    isLogin,
    loginLoading,
    resetStore,
    login,
    handleSocialLogin,
    getPermissionInfo,
    initUserInfo
  };
});
