import dayjs from 'dayjs';
import { defineStore } from 'pinia';
import { reactive, ref, shallowRef } from 'vue';

import { PreEntryStatus } from '@/enum';
import { fetchGetCompanyInfo } from '@/service/api/hrm/company-management';
import { fetchGetContractHistory } from '@/service/api/hrm/contract_info';
import { fetchSimilarBlackList } from '@/service/api/hrm/roster_black';
import { fetchGetResigningHistoryCount } from '@/service/api/hrm/roster_left';
import {
  getEmployeeStatus,
  getFamilyMember,
  getFinancialBankInfo,
  getHrmBasicInfo,
  getHrmEducationExperience,
  getHrmLegalCompliance,
  getHrmTrainCertificate,
  getHrmWorkExperience,
  getReviewDetail
} from '@/service/api/hrm/roster_user';
import { getFormatDictData } from '@/utils/async-functions';
import BankInfoForm from '@/views/hrm/roster_detail/components/modules/bank-info-form.vue';
import BaseInfoForm from '@/views/hrm/roster_detail/components/modules/base-info-form.vue';
import EducationInfoForm from '@/views/hrm/roster_detail/components/modules/education-info-form.vue';
import FamilyInfoForm from '@/views/hrm/roster_detail/components/modules/family-info-form.vue';
import LegalInfoForm from '@/views/hrm/roster_detail/components/modules/legal-info-form.vue';
import TrainInfoForm from '@/views/hrm/roster_detail/components/modules/train-info-form.vue';
import WorkInfoForm from '@/views/hrm/roster_detail/components/modules/work-info-form.vue';
import ContractInfoFormWithActions from '@/views/hrm/roster_review/modules/contract-info-form-with-actions.vue';
import ReviewInfoForm from '@/views/hrm/roster_review/modules/review-info-form.vue';

export const useRosterStore = defineStore('roster-store', () => {
  const maritalStatusOptions = ref<Api.System.FormattedOption[]>([]);
  const politicalStatusOptions = ref<Api.System.FormattedOption[]>([]);
  const workClothesSizeOptions = ref<Api.System.FormattedOption[]>([]);
  const ethnicityOptions = ref<Api.System.FormattedOption[]>([]);
  const genderOptions = ref<Api.System.FormattedOption[]>([]);
  const educationLevelOptions = ref<Api.System.FormattedOption[]>([]);
  const educationModeColumns = ref<Api.System.FormattedOption[]>([]);
  const educationStatusOptions = ref<Api.System.FormattedOption[]>([]);
  const englishOptions = ref<Api.System.FormattedOption[]>([]);
  const driverOptions = ref<Api.System.FormattedOption[]>([]);
  const certificateOptions = ref<Api.System.FormattedOption[]>([]);
  const computerOptions = ref<Api.System.FormattedOption[]>([]);
  const certificateLevelOptions = ref<Api.System.FormattedOption[]>([]);
  const cardTypeOptions = ref<Api.System.FormattedOption[]>([]);
  const relationshipOptions = ref<Api.System.FormattedOption[]>([]);
  const userId = ref<string>();
  // 离职员工数量
  const resignedNumber = ref<number>(0);
  // 合同历史
  const contractHistory = ref<Api.Hrm.SimpleContractRespVO[]>([]);
  // 审核信息
  const reviewInfo = ref<Api.Hrm.EmployeeReviewRespVO>({
    reviewStatus: '',
    reviewer: undefined,
    reviewerId: 0,
    reviewTime: '',
    rejectionReason: '',
    errorStep: 0
  });
  // 菜单
  const sections = ref([
    { id: 1, title: '身份信息', component: shallowRef(BaseInfoForm) },
    { id: 2, title: '教育背景', component: shallowRef(EducationInfoForm) },
    { id: 3, title: '工作经历', component: shallowRef(WorkInfoForm) },
    { id: 4, title: '技能资质', component: shallowRef(TrainInfoForm) },
    { id: 5, title: '合规文件', component: shallowRef(LegalInfoForm) },
    { id: 6, title: '银行信息', component: shallowRef(BankInfoForm) },
    { id: 7, title: '家庭成员', component: shallowRef(FamilyInfoForm) }
  ]);
  // 状态
  const statusInfo = reactive<Api.Hrm.EmployeeStatusRespVO>({
    errorStep: 0,
    eventTypeSubStatus: 0,
    id: 0,
    lastStep: 0,
    subStatusId: 0,
    reviewStatus: '',
    name: '',
    rejectionReason: '',
    reviewTime: '',
    reviewer: {
      data: {},
      id: 0,
      name: ''
    }
  });
  // 信息
  const baseInfo = reactive<Api.Hrm.IdAndBasicInfoRespVO>({
    backIdCardId: 0,
    birthday: '',
    currentResidence: '',
    electronicPhotoFile: '',
    electronicPhotoId: 0,
    email: '',
    ethnicity: 0,
    ethnicityText: '',
    frontIdCardId: 0,
    gender: 0,
    genderText: '',
    hobbies: '',
    householdRegistration: '',
    id: 0,
    idBackImg: '',
    idFrontImg: '',
    idNumber: '',
    maritalStatus: 0,
    maritalStatusText: '',
    name: '',
    phoneNumber: '',
    politicalStatus: 0,
    politicalStatusText: '',
    workClothesSize: '',
    workClothesSizeText: '',
    entryPosition: {},

    avatar: '',
    entryDate: '',
    deptName: '',
    companyEntity: '',
    // 岗位属性
    positionProperty: '',
    // 司龄
    workDay: undefined,
    status: undefined,
    subStatus: undefined,
    position: {
      id: '',
      name: ''
    }
  });
  // 教育背景
  const educationInfo = reactive<Api.Hrm.FormItem[]>([]);
  // 工作经历
  const workInfo = reactive<Api.Hrm.Request>({
    year: null!,
    description: null!,
    details: [
      {
        time: null!,
        begin: null!,
        end: null!,
        companyName: null!,
        performanceSummary: null!,
        position: null!,
        projectName: null!,
        projectScale: null!
      }
    ]
  });
  // 技能资质
  const trainInfo = reactive<Api.Hrm.UpdateTrainingAndCertificatesReqVO[]>([]);
  // 合规文件
  const legalInfo = reactive<Api.Hrm.UpdateLegalComplianceReqVO[]>([
    {
      label: '离职证明',
      files: [],
      hasFile: false,
      kind: 9,
      missingReason: null!,
      expectedProvideDate: '',
      fileIds: []
    },
    {
      label: '无犯罪记录证明',
      files: [],
      hasFile: false,
      kind: 5,
      missingReason: null!,
      expectedProvideDate: '',
      fileIds: []
    },
    {
      label: '个人征信报告',
      files: [],
      hasFile: false,
      kind: 10,
      missingReason: null!,
      expectedProvideDate: '',
      fileIds: []
    },
    {
      label: '体检证明',
      files: [],
      hasFile: false,
      kind: 11,
      missingReason: null!,
      expectedProvideDate: '',
      fileIds: []
    }
  ]);
  // 银行信息
  const bankInfo = reactive<Api.Hrm.BankCardInfoRespVO>({
    bankAccountNumber: '',
    bankBranchName: '',
    bankCard: 0,
    bankCardFile: [],
    bankName: '',
    bankStatementPhotos: [],
    cardType: '',
    cardTypeText: '',
    expireDate: ''
  });
  // 家庭成员
  const familyInfo = reactive<Api.Hrm.UpdateFamilyMemberDetailRequest>({
    details: [],
    emergencyContact: '',
    emergencyContactPhone: '',
    familyOrFriendName: '',
    hasFamilyOrFriendInCompany: false,
    familyOrFriendPositionName: ''
  });
  // 公司信息
  const companyInfo = reactive<Api.Hrm.CompanyListRespVO>({
    address: '',
    contactWay: '',
    id: 0,
    legalPersonName: '',
    name: ''
  });
  // 黑名单
  const blackList = ref<Api.Hrm.BlackListArray>([]);

  // 获取相似黑名单
  async function handleFetchSimilarBlackList(id) {
    try {
      blackList.value = [];
      const { data, error } = await fetchSimilarBlackList(id);
      if (error || !data) return;
      blackList.value = data;
    } catch (e) {
      console.log(e);
    }
  }

  // 获取身份信息
  async function handleGetHrmBasicInfo(id: string) {
    try {
      Object.assign(baseInfo, {});
      const { data, error } = await getHrmBasicInfo(id);
      if (error || !data) {
        return;
      }

      Object.assign(data, {
        ethnicityText: ethnicityOptions.value.find(i => Number(i.value) === data.ethnicity)?.label || '',
        genderText: genderOptions.value.find(i => Number(i.value) === data.gender)?.label || '',
        maritalStatusText: maritalStatusOptions.value.find(i => Number(i.value) === data.maritalStatus)?.label || '',
        politicalStatusText:
          politicalStatusOptions.value.find(i => Number(i.value) === data.politicalStatus)?.label || '',
        workClothesSizeText: workClothesSizeOptions.value.find(i => i.value === data.workClothesSize)?.label || ''
      });
      Object.assign(baseInfo, data);
    } catch (e) {
      console.log(e);
    }
  }

  // 教育背景
  async function handleGetHrmEducationExperience(id: string) {
    try {
      educationInfo.length = 0;
      const { data } = await getHrmEducationExperience(id);
      if (!data || data.length === 0) {
        return;
      }
      const newData = (data || []).map((item: Api.Hrm.FormItem) => {
        return {
          ...item,
          time: `${item.begin ? dayjs(item.begin).format('YYYY年MM月') : ''} ~ ${item.end ? dayjs(item.end).format('YYYY年MM月') : ''}`,
          educationStatusText: educationStatusOptions.value.find(i => Number(i.value) === item.status)?.label || '',
          educationLevelText:
            educationLevelOptions.value.find(i => Number(i.value) === item.educationLevel)?.label || '',
          educationModeText: educationModeColumns.value.find(i => Number(i.value) === item.educationMode)?.label || '',
          badgePhoto1: item.proofDocument?.find(i => i.kind === 13)?.fileId,
          badgePhoto2: item.proofDocument?.find(i => i.kind === 12)?.fileId,
          badgePhoto3: item.proofDocument?.find(i => i.kind === 14)?.fileId
        };
      });
      Object.assign(educationInfo, newData);
    } catch (e) {
      console.log(e);
    }
  }

  // 工作经历
  async function handleGetHrmWorkExperience(id: string) {
    try {
      Object.assign(workInfo, {});
      const { data } = await getHrmWorkExperience(id);
      if (!data) {
        return;
      }
      data.details = data.details || [];
      (data?.details || []).forEach((item: Api.Hrm.WorkExperienceDetailReqVO) => {
        item.time = `${item.begin ? dayjs(item.begin).format('YYYY年MM月') : ''} ~ ${item.end ? dayjs(item.end).format('YYYY年MM月') : ''}`;
      });
      Object.assign(workInfo, data);
    } catch (e) {
      console.log(e);
    }
  }

  // 技能资质
  async function handleGetHrmTrainCertificate(id: string) {
    try {
      trainInfo.length = 0;
      const { data } = await getHrmTrainCertificate(id);
      if (!Array.isArray(data) || data.length === 0) {
        return;
      }
      (data || []).forEach((item: Api.Hrm.UpdateTrainingAndCertificatesReqVO) => {
        item.certificateTypeText =
          certificateOptions.value.find(i => Number(i.value) === item.certificateType)?.label || '';
        if (item.certificateType === 1) {
          certificateLevelOptions.value = englishOptions.value;
        } else if (item.certificateType === 2) {
          certificateLevelOptions.value = computerOptions.value;
        } else if (item.certificateType === 3) {
          certificateLevelOptions.value = driverOptions.value;
        }
        item.certificateTime &&= dayjs(item.certificateTime).format('YYYY年MM月');
        item.certificateLevelText =
          certificateLevelOptions.value.find(i => Number(i.value) === item.certificateLevel)?.label || '';
        item.trainTime = `${item.trainBegin ? dayjs(item.trainBegin).format('YYYY年MM月') : ''} ~ ${item.trainEnd ? dayjs(item.trainEnd).format('YYYY年MM月') : ''}`;
      });
      Object.assign(trainInfo, data);
    } catch (e) {
      console.log(e);
    }
  }

  // 获取证书及文件
  async function handleGetHrmLegalCompliance(id: string) {
    try {
      Object.assign(legalInfo, [
        {
          label: '离职证明',
          files: [],
          hasFile: false,
          kind: 9,
          missingReason: null!,
          expectedProvideDate: '',
          fileIds: []
        },
        {
          label: '无犯罪记录证明',
          files: [],
          hasFile: false,
          kind: 5,
          missingReason: null!,
          expectedProvideDate: '',
          fileIds: []
        },
        {
          label: '个人征信报告',
          files: [],
          hasFile: false,
          kind: 10,
          missingReason: null!,
          expectedProvideDate: '',
          fileIds: []
        },
        {
          label: '体检证明',
          files: [],
          hasFile: false,
          kind: 11,
          missingReason: null!,
          expectedProvideDate: '',
          fileIds: []
        }
      ]);
      const { data } = await getHrmLegalCompliance(id);

      if (!Array.isArray(data) || data.length === 0) {
        legalInfo.length = 0;
        return;
      }
      // 保持顺序
      legalInfo.forEach(item => {
        const matched = data?.find(d => d.kind === item.kind);
        if (matched) {
          item.hasFile = !item.hasFile;
          Object.assign(item, matched);
        }
      });
    } catch (e) {
      console.log(e);
    }
  }

  // 获取银行信息
  async function handleGetFinancialBankInfo(id: string) {
    try {
      Object.assign(bankInfo, {
        bankAccountNumber: '',
        bankBranchName: '',
        bankCard: 0,
        bankCardFile: [],
        bankName: '',
        bankStatementPhotos: [],
        cardType: '',
        cardTypeText: '',
        expireDate: '',
        isNotProvide: false,
        missingReason: '',
        expectedProvideDate: ''
      });
      const { data } = await getFinancialBankInfo(id);
      if (!data) {
        return;
      }
      data.cardTypeText =
        (data?.cardType && cardTypeOptions.value.find(i => (i.value as string) === data.cardType)?.label) || '';
      data.expireDate = data?.expireDate ? dayjs(data.expireDate).format('YYYY年MM月') : '';
      Object.assign(bankInfo, data);
    } catch (e) {
      console.log(e);
    }
  }

  // 获取家庭成员信息
  async function handleGetFamilyMember(id: string) {
    try {
      Object.assign(familyInfo, {
        details: [],
        emergencyContact: '',
        emergencyContactPhone: '',
        familyOrFriendName: '',
        hasFamilyOrFriendInCompany: false
      });
      const { data, error } = await getFamilyMember(id);
      if (error || !data) {
        return;
      }
      data.details.forEach((item: Api.Hrm.UpdateFamilyMemberDetailReqVO) => {
        item.relationText = relationshipOptions.value.find(i => Number(i.value) === Number(item.relation))?.label || '';
      });
      Object.assign(familyInfo, data);
    } catch (e) {
      console.log(e);
    }
  }

  // 获取公司详情
  async function handleGetCompanyInfo(id?: number) {
    try {
      Object.assign(companyInfo, {
        address: '',
        contactWay: '',
        id: 0,
        legalPersonName: '',
        name: ''
      });
      const { data } = await fetchGetCompanyInfo(id);
      if (!data) {
        return;
      }
      Object.assign(companyInfo, data);
    } catch (e) {
      console.log(e);
    }
  }

  async function getDictData() {
    const { data } = await getFormatDictData({
      keys: [
        'hrm_marital_status',
        'hrm_political_status',
        'hrm_ethnicity',
        'hrm_gender',
        'hrm_education_level_type',
        'hrm_education_type',
        'hrm_english_level',
        'hrm_driver_license',
        'hrm_certificate_type',
        'hrm_computer_level',
        'hrm_bank_card_type',
        'hrm_education_mode',
        'hrm_relationship_with_members',
        'hrm_work_clothes_size'
      ].join(',')
    });
    maritalStatusOptions.value = data.hrm_marital_status || [];
    genderOptions.value = data.hrm_gender || [];
    politicalStatusOptions.value = data.hrm_political_status || [];
    ethnicityOptions.value = data.hrm_ethnicity || [];
    educationLevelOptions.value = data.hrm_education_level_type || [];
    educationStatusOptions.value = data.hrm_education_type || [];
    englishOptions.value = data.hrm_english_level || [];
    driverOptions.value = data.hrm_driver_license || [];
    certificateOptions.value = data.hrm_certificate_type || [];
    computerOptions.value = data.hrm_computer_level || [];
    cardTypeOptions.value = data.hrm_bank_card_type || [];
    educationModeColumns.value = data.hrm_education_mode || [];
    relationshipOptions.value = data.hrm_relationship_with_members || [];
    workClothesSizeOptions.value = data.hrm_work_clothes_size || [];
  }

  // 向sectionsData后添加数据
  function pushSection(data: (typeof sections.value)[0]) {
    if (!sections.value.find(s => s.id === data.id)) {
      sections.value.push(data);
    }
  }

  // 向sectionsData前添加数据
  function unshiftSection(data: (typeof sections.value)[0]) {
    if (!sections.value.find(s => s.id === data.id)) {
      sections.value.unshift(data);
    }
  }

  function resetSections() {
    sections.value = [
      { id: 1, title: '身份信息', component: shallowRef(BaseInfoForm) },
      { id: 2, title: '教育背景', component: shallowRef(EducationInfoForm) },
      { id: 3, title: '工作经历', component: shallowRef(WorkInfoForm) },
      { id: 4, title: '技能资质', component: shallowRef(TrainInfoForm) },
      { id: 5, title: '合规文件', component: shallowRef(LegalInfoForm) },
      { id: 6, title: '银行信息', component: shallowRef(BankInfoForm) },
      { id: 7, title: '家庭成员', component: shallowRef(FamilyInfoForm) }
    ];
  }

  async function getAllFun() {
    if (!userId.value) return;
    await handleGetHrmBasicInfo(userId.value);
    await handleGetHrmEducationExperience(userId.value);
    await handleGetHrmWorkExperience(userId.value);
    await handleGetHrmTrainCertificate(userId.value);
    await handleGetHrmLegalCompliance(userId.value);
    await handleGetFinancialBankInfo(userId.value);
    await handleGetFamilyMember(userId.value);
  }

  async function handleGetStatus(id: string) {
    try {
      const { data, error } = await getEmployeeStatus(id);
      if (error) {
        return;
      }
      Object.assign(statusInfo, data);
      const { data: reviewData, error: reviewError } = await getReviewDetail(id);
      if (reviewError) {
        return;
      }
      Object.assign(reviewInfo.value, reviewData);
      if (
        [
          PreEntryStatus.Reviewing,
          PreEntryStatus.PendingContracts,
          PreEntryStatus.Rejected,
          PreEntryStatus.WaitingForContract,
          PreEntryStatus.WaitingForSign
        ].includes(statusInfo.subStatusId)
      ) {
        pushSection({
          id: 8,
          title: '审核信息',
          component: shallowRef(ReviewInfoForm) as any
        });
      }
      if ([PreEntryStatus.PendingContracts].includes(statusInfo.subStatusId)) {
        pushSection({
          id: 9,
          title: '合同信息',
          component: shallowRef(ContractInfoFormWithActions) as any
        });
      }
    } catch (e) {
      console.log(e);
    }
  }

  // 离职历史列表数量
  async function handleGetResignedNumber(name: string) {
    const { data, error } = await fetchGetResigningHistoryCount(name);
    if (error) {
      return;
    }
    resignedNumber.value = data;
  }

  // 获取员工历史合同信息
  async function handleGetContractHistory(id: string) {
    contractHistory.value = [];
    const { data, error } = await fetchGetContractHistory(id);
    if (error) {
      return;
    }
    contractHistory.value = data;
  }
  return {
    companyInfo,
    baseInfo,
    reviewInfo,
    educationInfo,
    workInfo,
    trainInfo,
    legalInfo,
    bankInfo,
    familyInfo,
    statusInfo,
    sections,
    blackList,
    userId,
    resignedNumber,
    contractHistory,
    handleGetCompanyInfo,
    handleGetHrmBasicInfo,
    handleGetHrmEducationExperience,
    handleGetHrmWorkExperience,
    handleGetHrmTrainCertificate,
    handleGetHrmLegalCompliance,
    handleGetFinancialBankInfo,
    handleGetFamilyMember,
    handleFetchSimilarBlackList,
    handleGetStatus,
    resetSections,
    getDictData,
    getAllFun,
    handleGetResignedNumber,
    handleGetContractHistory
  };
});
