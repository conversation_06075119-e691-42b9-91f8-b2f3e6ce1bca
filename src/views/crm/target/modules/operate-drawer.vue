<script lang="ts" setup>
import { computed, onMounted, reactive, ref } from 'vue';
import { NSelect } from 'naive-ui';
import { useDebounceFn } from '@vueuse/core';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { BankTypeOptions, businessTypeOptions } from '@/enum';
import DateTimePicker from '@/components/common/date-time-picker.vue';
import { fetchAddTarget, fetchGetTargetListAll, fetchUpdateTarget } from '@/service/api';
import { getEnumValue } from '@/utils/useful_func';

defineOptions({
  name: 'TargetOperateDrawer'
});

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Crm.ExtendedProjectDetail | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
  (e: 'btnClose'): void;
}

const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();
const model: Api.Crm.TargetFormParams = reactive(createDefaultModel());
const otherList = ref<Api.Crm.Target[]>([]);
const otherShow = ref<boolean>(false);
const handleSearch = useDebounceFn(nameChange, 300);
async function nameChange(val: string) {
  try {
    if (val === '') {
      otherList.value = [];
      otherShow.value = false;
      return;
    }
    const { data } = await fetchGetTargetListAll({ name: val });
    otherList.value = Array.isArray(data) ? data.slice(0, 10) : [];
    otherShow.value = Array.isArray(data) && otherList.value.length > 0;
  } catch (e) {
    console.log(e);
  }
}
function createDefaultModel(): Api.Crm.TargetFormParams {
  return {
    name: null,
    businessType: null,
    businessTypes: [],
    averageRevenue: null,
    remark: null,
    baseMode: null,
    biddingTime: null,
    contractEndTime: null,
    entryCountries: null,
    expectedStartTime: null,
    partnerDuration: null,
    requirement: null,
    singleEstimatedValue: null,
    supplierInfo: null,
    bankType: null,
    totalScale: null,
    businessCategory: null
  };
}

type RuleKey = keyof Pick<
  Api.Crm.TargetFormParams,
  'name' | 'businessType' | 'businessTypes' | 'bankType' | 'businessCategory'
>;

const rules = ref<Record<RuleKey, App.Global.FormRule>>({
  name: defaultRequiredRule,
  businessType: defaultRequiredRule,
  businessTypes: defaultRequiredRule,
  bankType: defaultRequiredRule,
  businessCategory: defaultRequiredRule
});

function handleInitModel() {
  Object.assign(model, createDefaultModel());

  if (props.operateType === 'edit' && props.rowData) {
    const { rowData } = props;

    Object.assign(model, rowData);
    const bankTypeNum = Number(rowData.bankType);
    model.bankType = bankTypeNum === 0 ? null : bankTypeNum;
    model.averageRevenue = rowData.averageRevenue;
  }
}

async function handleSubmit() {
  try {
    await validate();
    if (props.operateType === 'add') {
      const requestArr = [];
      for (const type of model.businessTypes as []) {
        const data = {
          ...model,
          businessType: type
        };
        requestArr.push(fetchAddTarget(data));
      }

      const results = await Promise.all(requestArr);
      for (const result of results) {
        if (result.error) {
          return false;
        }
      }
      emit('submitted');
      emit('btnClose');
      window.$message?.success($t('common.addSuccess'));
      return true;
    } else if (props.operateType === 'edit') {
      const { error } = await fetchUpdateTarget(model);
      if (error) {
        return false;
      }
      emit('submitted');
      emit('btnClose');
      window.$message?.success($t('common.updateSuccess'));
    }

    return false;
  } catch (e: unknown) {
    console.error(e);
    return false;
  }
}

onMounted(() => {
  handleInitModel();
  restoreValidation();
});
</script>

<template>
  <NForm ref="formRef" :model="model" :rules="rules">
    <NGrid responsive="screen" model-responsive x-gap="20">
      <NFormItemGi span="12 s:12 m:12" label="目标客户" path="name">
        <NInput v-model:value="model.name" clearable placeholder="请输入目标客户" @update-value="handleSearch" />
      </NFormItemGi>
      <NFormItemGi
        v-if="!otherShow && props.operateType === 'add'"
        span="12 s:12 m:12"
        label="业务类型"
        path="businessTypes"
      >
        <NCheckboxGroup v-model:value="model.businessTypes">
          <NCheckbox v-for="item in businessTypeOptions" :key="item.value" :value="item.value">
            {{ item.label }}
          </NCheckbox>
        </NCheckboxGroup>
      </NFormItemGi>
      <NFormItemGi
        v-if="!otherShow && props.operateType === 'edit'"
        span="12 s:12 m:12"
        label="业务类型"
        path="businessType"
      >
        <NRadioGroup v-model:value="model.businessType">
          <NRadio v-for="item in businessTypeOptions" :key="item.value" :value="item.value">
            {{ item.label }}
          </NRadio>
        </NRadioGroup>
      </NFormItemGi>
      <NFormItemGi v-show="!otherShow" span="12 s:12 m:12" label="银行类型" path="bankType">
        <NSelect v-model:value="model.bankType" :options="BankTypeOptions" clearable />
      </NFormItemGi>
      <NFormItemGi v-show="!otherShow" span="12 s:12 m:12" label="业务类别" path="businessCategory">
        <NInput v-model:value="model.businessCategory" class="w-full" clearable placeholder="请输入业务类别" />
      </NFormItemGi>
      <NFormItemGi v-show="!otherShow" span="12 s:12 m:12" label="投标时间" path="biddingTime">
        <DateTimePicker v-model:time="model.biddingTime" class="w-full" label="投标时间" use-for="month_date" />
      </NFormItemGi>
      <NFormItemGi v-show="!otherShow" span="12 s:12 m:12" label="总规模" path="totalScale">
        <NInput v-model:value="model.totalScale" class="w-full" clearable placeholder="请输入总规模" />
      </NFormItemGi>
      <NFormItemGi v-show="!otherShow" span="12 s:12 m:12" label="合同结束时间" path="contractEndTime">
        <DateTimePicker v-model:time="model.contractEndTime" class="w-full" label="合同结束时间" use-for="date" />
      </NFormItemGi>
      <NFormItemGi v-show="!otherShow" span="12 s:12 m:12" label="预估规模" path="singleEstimatedValue">
        <NInput v-model:value="model.singleEstimatedValue" class="w-full" clearable placeholder="请输入预估规模" />
      </NFormItemGi>
      <NFormItemGi v-show="!otherShow" span="12 s:12 m:12" label="预计启动时间" path="expectedStartTime">
        <DateTimePicker
          v-model:time="model.expectedStartTime"
          class="w-full"
          label="预计启动时间"
          use-for="month_date"
        />
      </NFormItemGi>
      <NFormItemGi v-show="!otherShow" span="12 s:12 m:12" label="基地规模" path="baseMode">
        <NInput v-model:value="model.baseMode" class="w-full" clearable placeholder="请输入基地规模" />
      </NFormItemGi>

      <NFormItemGi v-show="!otherShow" span="12 s:12 m:12" label="入围家数" path="entryCountries">
        <NInput v-model:value="model.entryCountries" class="w-full" clearable placeholder="请输入入围家数" />
      </NFormItemGi>
      <NFormItemGi v-show="!otherShow" span="12 s:12 m:12" label="合作年限" path="partnerDuration">
        <NInput v-model:value="model.partnerDuration" class="w-full" clearable placeholder="请输入合作年限" />
      </NFormItemGi>

      <NFormItemGi v-show="!otherShow" span="12 s:12 m:12" label="城市" path="city">
        <NInput v-model:value="model.city" class="w-full" clearable placeholder="请输入职场城市要求" />
      </NFormItemGi>
      <NFormItemGi v-show="!otherShow" span="12 s:12 m:12" label="人均营收" path="averageRevenue">
        <NInput v-model:value="model.averageRevenue" class="w-full" clearable placeholder="请输入人均营收" />
      </NFormItemGi>
      <NFormItemGi
        v-show="!otherShow"
        span="12 s:12 m:12"
        label="合作中的老供应商信息（供应商名称、规模、上次报价）"
        path="supplierInfo"
      >
        <NInput
          v-model:value="model.supplierInfo"
          class="w-full"
          clearable
          placeholder="请输入合作中的老供应商信息（供应商名称、规模、上次报价）"
          type="textarea"
          rows="1"
        />
      </NFormItemGi>
      <NFormItemGi v-show="!otherShow" span="12 s:12 m:12" label="备注" path="remark">
        <NInput
          v-model:value="model.remark"
          class="w-full"
          clearable
          placeholder="请输入备注"
          type="textarea"
          rows="1"
        />
      </NFormItemGi>
    </NGrid>
    <div v-show="otherShow" class="conflict-list mb-10px">
      <div class="text-right">
        <NButton type="primary" @click="otherShow = !otherShow">继续添加</NButton>
      </div>
      <p class="conflict-title">系统中已存在以下相似目标客户：</p>
      <NTable :bordered="false" :single-line="false" class="conflict-table">
        <thead>
          <tr>
            <th>序号</th>
            <th>目标客户</th>
            <th>银行类型</th>
            <th>业务类别</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, index) in otherList" :key="item.id">
            <td>{{ index + 1 }}</td>
            <td>{{ item.name }} {{ `(${getEnumValue(businessTypeOptions, item.businessType)})` }}</td>
            <td>{{ getEnumValue(BankTypeOptions, Number(item?.bankType)) }}</td>
            <td>{{ item?.businessCategory }}</td>
          </tr>
        </tbody>
      </NTable>
    </div>
    <NSpace :size="16" justify="end">
      <NButton @click="emit('btnClose')">{{ $t('common.cancel') }}</NButton>
      <ReButton type="primary" @click="handleSubmit">{{ $t('common.confirm') }}</ReButton>
    </NSpace>
  </NForm>
</template>

<style scoped>
.conflict-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  color: #595959;
}
.conflict-table th,
.conflict-table td {
  border: 1px solid #d9d9d9;
  padding: 8px;
  text-align: left;
  background-color: #fff;
}
.conflict-table th {
  background-color: #fafafa;
  font-weight: 500;
}
</style>
