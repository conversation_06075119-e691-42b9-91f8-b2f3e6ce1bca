<script lang="tsx" setup>
import { N<PERSON>utton, NPopconfirm, NTooltip } from 'naive-ui';
import dayjs from 'dayjs';
import { h } from 'vue';
import { useTable } from '@/hooks/common/table';
import { $t } from '@/locales';
import { useAppStore } from '@/store/modules/app';
import { fetchDeleteTarget, fetchGetTargetList, fetchRestoreTarget } from '@/service/api';
import { renderModal } from '@/components/re-modal';
import { getEnumValue } from '@/utils/useful_func';
import { businessTypeOptions } from '@/enum';
import TargetOperateDrawer from './modules/operate-drawer.vue';
import TargetSearch from './modules/search.vue';

const appStore = useAppStore();

const {
  columns,
  columnChecks,
  data,
  getData,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams,
  getDataByPage
} = useTable({
  apiFn: fetchGetTargetList,
  showTotal: true,
  apiParams: {
    name: null,
    businessType: null,
    isSuitable: 1
  },
  columns: () => [
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'name',
      title: '目标客户名称',
      align: 'center',
      width: 180,
      render: (rowData: Api.Crm.Target) => `${rowData.name}(${getEnumValue(businessTypeOptions, rowData.businessType)})`
    },
    {
      key: 'biddingTime',
      title: '投标时间',
      align: 'center',
      render: (row: Api.Crm.ExtendedProjectDetail) => row.biddingTime && dayjs(row.biddingTime).format('YYYY年MM月')
    },
    {
      key: 'contractEndTime',
      title: '合同结止时间',
      align: 'center',
      width: 130,
      render: (row: Api.Crm.ExtendedProjectDetail) =>
        row.contractEndTime && dayjs(row.contractEndTime).format('YYYY年MM月DD日')
    },
    {
      key: 'expectedStartTime',
      title: '预计启动时间',
      align: 'center',
      render: (row: Api.Crm.ExtendedProjectDetail) =>
        row.expectedStartTime && dayjs(row.expectedStartTime).format('YYYY年MM月')
    },
    {
      key: 'partnerDuration',
      title: '合作年限',
      align: 'center'
    },
    {
      key: 'totalScale',
      title: '总规模',
      align: 'center'
    },
    {
      key: 'entryCountries',
      title: '入围家数',
      align: 'center'
    },
    {
      key: 'singleEstimatedValue',
      title: '预估规模',
      align: 'center'
    },
    {
      key: 'baseMode',
      title: '基地模式',
      align: 'center'
    },
    {
      key: 'city',
      title: '城市',
      align: 'center'
    },
    {
      key: 'averageRevenue',
      title: '人均营收',
      align: 'center'
    },
    {
      key: 'updateTime',
      title: '更新时间',
      align: 'center',
      minWidth: 100,
      render: (rowData: Api.Crm.Target) => (
        <NTooltip trigger="hover" placement="top">
          {{
            default: () => h('div', null, rowData.updaterName ? `更新人：${rowData.updaterName}` : ''),
            trigger: () => <div>{rowData.updateTime}</div>
          }}
        </NTooltip>
      )
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 130,
      render: (row: Api.Crm.Target) => (
        <div class="flex-center gap-8px">
          <NButton type="primary" ghost size="small" onClick={() => handleEdit(row)}>
            {$t('common.edit')}
          </NButton>
          {row.isSuitable === 1 ? (
            <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
              {{
                default: () => $t('common.confirmDelete'),
                trigger: () => (
                  <NButton type="error" ghost size="small">
                    {$t('common.delete')}
                  </NButton>
                )
              }}
            </NPopconfirm>
          ) : null}
          {row.isSuitable === 0 ? (
            <NPopconfirm onPositiveClick={() => handleRestore(row.id)}>
              {{
                default: () => $t('common.confirmRestore'),
                trigger: () => (
                  <NButton type="success" ghost size="small">
                    {$t('common.restore')}
                  </NButton>
                )
              }}
            </NPopconfirm>
          ) : null}
        </div>
      )
    }
  ]
});
// 还原
async function handleRestore(id: number) {
  try {
    await fetchRestoreTarget({ id });
    window.$message?.success($t('common.restoreSuccess'));
    await getData();
  } catch (e) {
    console.log(e);
  }
}
// 删除
async function handleDelete(id: number) {
  try {
    await fetchDeleteTarget({ id });
    window.$message?.success($t('common.deleteSuccess'));
    await getData();
  } catch (e) {
    console.log(e);
  }
}
function handleAdd() {
  renderModal(
    TargetOperateDrawer,
    { operateType: 'add', onSubmitted: getData },
    {
      title: '创建目标客户',
      style: {
        width: '50%'
      }
    }
  );
}
function handleEdit(row: Api.Crm.Target) {
  renderModal(
    TargetOperateDrawer,
    { operateType: 'edit', rowData: row, onSubmitted: getData },
    {
      title: '编辑目标客户',
      style: {
        width: '50%'
      }
    }
  );
}
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <TargetSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <NCard :bordered="false" class="sm:flex-1-hidden card-wrapper" size="small" title="目标客户">
      <template #header-extra>
        <TableHeaderOperation v-model:columns="columnChecks" :loading="loading" @add="handleAdd" @refresh="getData" />
      </template>
      <NDataTable
        :columns="columns"
        :data="data"
        :loading="loading"
        :pagination="mobilePagination"
        :row-key="row => row.id"
        :flex-height="!appStore.isMobile"
        :scroll-x="962"
        remote
        size="small"
        class="sm:h-full"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
