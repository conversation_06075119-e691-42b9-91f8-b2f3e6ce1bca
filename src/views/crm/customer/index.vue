<script setup lang="tsx">
import { NButton, NTag } from 'naive-ui';
import { useTable } from '@/hooks/common/table';
import { $t } from '@/locales';
import { fetchGetCustomerList } from '@/service/api';
import { useAppStore } from '@/store/modules/app';
import { renderModal, renderModalBtn } from '@/components/re-modal';
import { editRoleBtn, infoRoleBtn } from '@/directives/permission/permi-btn';
import OperateDrawer from './modules/customer-operate-drawer.vue';
import Search from './modules/customer-search.vue';
import CustomerInfoModal from './modules/customer-info.vue';

const appStore = useAppStore();

const { columns, data, getData, loading, mobilePagination, searchParams, resetSearchParams, getDataByPage } = useTable({
  apiFn: fetchGetCustomerList,
  showTotal: true,
  apiParams: {
    name: null,
    projectId: null,
    pageNo: 1
  },
  columns: () => [
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'name',
      title: '联络人名称',
      align: 'center',
      minWidth: 100
    },
    {
      key: 'projectId',
      title: '所属项目',
      align: 'center',
      minWidth: 100,
      render: (row: Api.Crm.Customer) => {
        return <NTag type="info">{row.project?.bankName}</NTag>;
      }
    },
    {
      key: 'department',
      title: '部门',
      align: 'center',
      minWidth: 100
    },
    {
      key: 'title',
      title: '职位',
      align: 'center',
      minWidth: 100
    },
    {
      key: 'createTime',
      title: '添加时间',
      align: 'center',
      width: 180
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 130,
      render: row => (
        <div class="flex-center gap-8px">
          {editRoleBtn(['crm:customers:update'], () => edit(row))}
          {infoRoleBtn(['crm:customers:query'], 'success', () => info(row.id))}
        </div>
      )
    }
  ]
});
function handleAdd() {
  renderModalBtn(
    OperateDrawer,
    { operateType: 'add', onSubmitted: getData },
    {
      title: '添加联络人',
      style: {
        width: '50%'
      },
      func: getData
    }
  );
}
function edit(rowData: Api.Crm.Customer) {
  renderModalBtn(
    OperateDrawer,
    { operateType: 'edit', rowData, onSubmitted: getData },
    {
      title: '编辑联络人',
      style: {
        width: '50%'
      },
      func: getData
    }
  );
}

function info(id: number) {
  renderModal(
    CustomerInfoModal,
    { id },
    {
      title: '联络人详情',
      style: {
        width: '50%'
      }
    }
  );
}
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <Search v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <NCard title="联络人管理" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          :loading="loading"
          :permission="['crm:customers:create']"
          @add="handleAdd"
          @refresh="getData"
        />
      </template>
      <NDataTable
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="962"
        :loading="loading"
        remote
        :row-key="row => row.id"
        :pagination="mobilePagination"
        class="sm:h-full"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
