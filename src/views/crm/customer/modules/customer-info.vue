<script setup lang="ts">
import { onMounted, ref } from 'vue';
import TableInfoList from '@/components/common/table-info-list.vue';
import { fetchGetCustomerDetail } from '@/service/api';

interface Props {
  id: number;
}
const props = defineProps<Props>();

const customerInfo = ref<Partial<Api.Crm.Customer>>({
  name: '',
  department: '',
  title: '',
  profileImage: ''
});

const fetchCustomerInfo = async () => {
  const { error, data } = await fetchGetCustomerDetail(props.id);
  if (error) {
    return;
  }
  customerInfo.value = data;
};

const maps = {
  name: '联络人名称',
  department: '部门',
  title: '职位',
  profileImage: '联络人画像',
  createTime: '添加时间'
};

onMounted(() => {
  fetchCustomerInfo();
});
</script>

<template>
  <TableInfoList :maps="maps" :data="customerInfo" />
</template>
