<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import { getProjectOptions } from '@/utils/async-functions';
import SelectWithSearch from '@/components/common/select-with-search.vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { fetchAddCustomer, fetchUpdateCustomer } from '@/service/api';

defineOptions({
  name: 'CustomerOperateDrawer'
});

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Crm.Customer | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
  (e: 'btnClose'): void;
}

const emit = defineEmits<Emits>();
const { formRef, validate } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const model: Api.Crm.CustomerFormParams = reactive(createDefaultModel());

function createDefaultModel(): Api.Crm.CustomerFormParams {
  return {
    name: '',
    department: '',
    title: '',
    profileImage: '',
    projectId: null,
    project: null
  };
}

type RuleKey = Extract<keyof Api.Crm.CustomerFormParams, 'name' | 'projectId'>;

const rules = ref<Record<RuleKey, App.Global.FormRule>>({
  name: defaultRequiredRule,
  projectId: defaultRequiredRule
});

function handleInitModel() {
  Object.assign(model, createDefaultModel());

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model, props.rowData);
  }
}

async function handleSubmit() {
  await validate();

  if (props.operateType === 'add') {
    const { error } = await fetchAddCustomer({ ...model });
    if (error) {
      return;
    }
    window.$message?.success($t('common.addSuccess'));
  } else if (props.operateType === 'edit') {
    const { error } = await fetchUpdateCustomer({ ...model });
    if (error) {
      return;
    }
    window.$message?.success($t('common.updateSuccess'));
  }

  emit('submitted');
  emit('btnClose');
}
onMounted(() => {
  handleInitModel();
});
defineExpose({
  handleSubmit
});
</script>

<template>
  <NForm ref="formRef" :model="model" :rules="rules">
    <NFormItem label="联络人名称" path="name">
      <NInput v-model:value="model.name" placeholder="请输入联络人名称" clearable />
    </NFormItem>
    <NFormItem label="部门" path="department">
      <NInput v-model:value="model.department" placeholder="请输入部门" clearable />
    </NFormItem>
    <NFormItem label="职位" path="title">
      <NInput v-model:value="model.title" placeholder="请输入职位" clearable />
    </NFormItem>
    <NFormItem label="联络人画像" path="profileImage">
      <NInput v-model:value="model.profileImage" type="textarea" rows="4" placeholder="请输入联络人画像" clearable />
    </NFormItem>
    <NFormItem label="项目" path="projectId">
      <SelectWithSearch
        v-model:value="model.projectId"
        :api-func="getProjectOptions"
        :selected-options="[model.project]"
        label-field="bankName"
        placeholder="归属项目"
      />
    </NFormItem>
  </NForm>
</template>

<style scoped></style>
