<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { fetchCancelPlan } from '@/service/api';
import { usePlanStore } from '@/store/modules/plan';

defineOptions({
  name: 'PlanCancel'
});

interface Emits {
  (e: 'btnSubmit'): void;
  (e: 'btnClose'): void;
}

const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const store = usePlanStore();

type RuleKey = Extract<keyof Api.Crm.PlanFormParams, 'cancelReason'>;

const rules = ref<Record<RuleKey, App.Global.FormRule>>({
  cancelReason: defaultRequiredRule
});

async function handleSubmit(): Promise<undefined> {
  await validate();

  const { error } = await fetchCancelPlan({
    id: store.info.id,
    cancelReason: store.info.cancelReason
  });
  if (error) {
    return;
  }
  window.$message?.success('计划取消成功');

  emit('btnSubmit');

  store.getInfo(Number(store.info.id));
}

onMounted(() => {
  restoreValidation();
});
</script>

<template>
  <NForm ref="formRef" :model="store.info" :rules="rules">
    <NFormItem label="取消原因" path="cancelReason">
      <NInput v-model:value="store.info.cancelReason" clearable placeholder="请输入取消原因" rows="6" type="textarea" />
    </NFormItem>
    <NSpace :size="16" justify="end">
      <NButton @click="emit('btnClose')">{{ $t('common.cancel') }}</NButton>
      <NButton type="primary" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
    </NSpace>
  </NForm>
</template>
