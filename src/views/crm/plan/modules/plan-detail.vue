<script lang="ts" setup>
import { onMounted, ref, toRaw } from 'vue';
import { objectPick } from '@antfu/utils';
import TableInfoList from '@/components/common/table-info-list.vue';
import { usePlanStore } from '@/store/modules/plan';
import { PlanStatus } from '@/enum';
import FollowForm from '@/views/crm/follow/modules/form.vue';
import PlanForm from './plan-form.vue';
import CancelPlanForm from './cancel-form.vue';

interface Props {
  id: number;
}
const props = defineProps<Props>();

interface Emits {
  (e: 'btnSubmit'): void;
  (e: 'btnClose'): void;
}

const emit = defineEmits<Emits>();

const store = usePlanStore();
store.init();

// 准备添加跟进页面的数据
const followFormData = ref<CommonType.RecordNullable<Api.Crm.Follow>>({});
function getInfo() {
  store.getInfo(props.id).then(() => {
    followFormData.value = objectPick(toRaw(store.info), ['projectId', 'project', 'customerIds', 'customers']);
    followFormData.value.planId = props.id;
    followFormData.value.plan = {
      id: props.id,
      planComment: `${store.info.planComment}(${store.info.planDate})`
    };
    setTabIndex();
  });
}

const maps = {
  planComment: '计划说明',
  planDate: '计划时间',
  createTime: '创建时间'
};

// 打开后的标签页
const tabIndex = ref('info');
function setTabIndex() {
  if (store.info.planStatus === PlanStatus.WaitVisit) {
    tabIndex.value = 'follow';
    return;
  }
  tabIndex.value = 'info';
}

onMounted(() => {
  getInfo();
});

function afterSubmit() {
  tabIndex.value = 'info';
  emit('btnSubmit');
}
</script>

<template>
  <NAlert v-if="store.info.planStatus === PlanStatus.Canceled" class="mb-5px" title="计划已被取消" type="warning">
    当前计划已被取消，无法编辑或删除
  </NAlert>
  <NTabs v-model:value="tabIndex">
    <NTabPane v-hasPermi="['crm:visits:query']" name="follow" tab="跟进">
      <FollowForm
        operate-type="plan"
        :row-data="followFormData"
        @btn-submit="afterSubmit"
        @btn-close="emit('btnClose')"
      />
    </NTabPane>
    <NTabPane v-hasPermi="['crm:plans:update']" name="edit" tab="编辑">
      <PlanForm operate-type="edit" @btn-submit="afterSubmit" @btn-close="emit('btnClose')" />
    </NTabPane>
    <NTabPane v-hasPermi="['crm:plans:query']" name="info" tab="详情">
      <TableInfoList :data="store.info" :maps="maps" :show-customer="true" :show-project="true" :show-user="true" />
    </NTabPane>
    <NTabPane v-hasPermi="['crm:plans:update']" name="cancel" tab="取消">
      <CancelPlanForm @btn-submit="afterSubmit" @btn-close="emit('btnClose')" />
    </NTabPane>
  </NTabs>
</template>

<style lang="scss" scoped></style>
