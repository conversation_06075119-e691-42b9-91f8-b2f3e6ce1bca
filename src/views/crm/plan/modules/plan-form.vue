<script lang="ts" setup>
import { onMounted, ref, useTemplateRef, watch } from 'vue';
import { getCustomerOptions, getProjectOptions } from '@/utils/async-functions';
import SelectWithSearch from '@/components/common/select-with-search.vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { fetchAddPlan, fetchUpdatePlan } from '@/service/api';
import DateTimePicker from '@/components/common/date-time-picker.vue';
import { usePlanStore } from '@/store/modules/plan';
import type { SelectWithSearchType } from '@/typings/common-type';

defineOptions({
  name: 'PlanDetail'
});

interface Props {
  operateType: NaiveUI.TableOperateType;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'btnSubmit'): void;
  (e: 'btnClose'): void;
}

const emit = defineEmits<Emits>();

const store = usePlanStore();

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

type RuleKey = Extract<keyof Api.Crm.PlanFormParams, 'planComment' | 'planDate' | 'customerIds' | 'projectId'>;

const rules = ref<Record<RuleKey, App.Global.FormRule>>({
  planComment: defaultRequiredRule,
  planDate: defaultRequiredRule,
  customerIds: defaultRequiredRule,
  projectId: defaultRequiredRule
});

// 选择客户
const customerTref = useTemplateRef<SelectWithSearchType>('customerSelectRef');
watch(
  () => store.info.projectId,
  () => {
    customerTref.value!.reset();
  }
);

async function handleSubmit(): Promise<undefined> {
  await validate();

  // 计划时间不能小于当前时间
  if (
    props.operateType === 'add' &&
    store.info.planDate &&
    new Date(String(store.info.planDate)).getTime() < Date.now()
  ) {
    window.$message?.error('计划时间不能小于当前时间');
    return;
  }

  if (props.operateType === 'add') {
    const { error } = await fetchAddPlan({ ...store.info });
    if (error) {
      return;
    }
    window.$message?.success($t('common.addSuccess'));
  } else if (props.operateType === 'edit') {
    const { error } = await fetchUpdatePlan({ ...store.info });
    if (error) {
      return;
    }
    window.$message?.success($t('common.updateSuccess'));
  }

  emit('btnSubmit');
}

onMounted(() => {
  restoreValidation();
});
</script>

<template>
  <NForm ref="formRef" :model="store.info" :rules="rules">
    <NFormItem label="项目" path="projectId">
      <SelectWithSearch
        v-model:value="store.info.projectId"
        :api-func="getProjectOptions"
        :selected-options="[store.info.project]"
        label-field="bankName"
        placeholder="归属项目"
      />
    </NFormItem>
    <NFormItem label="联络人" path="customerIds">
      <SelectWithSearch
        ref="customerSelectRef"
        v-model:value="store.info.customerIds"
        :api-func="
          async (param: string, page: number) => await getCustomerOptions(param, Number(store.info.projectId), 0, page)
        "
        :multiple="true"
        :page-size="0"
        :selected-options="store.info.customers || []"
        label-field="name"
        placeholder="联络人"
      />
    </NFormItem>
    <NFormItem label="计划时间" path="planDate">
      <DateTimePicker v-model:time="store.info.planDate" label="计划时间" />
    </NFormItem>
    <NFormItem label="计划说明" path="planComment">
      <NInput v-model:value="store.info.planComment" clearable placeholder="请输入计划说明" rows="6" type="textarea" />
    </NFormItem>
    <NSpace :size="16" justify="end">
      <NButton @click="emit('btnClose')">{{ $t('common.cancel') }}</NButton>
      <NButton type="primary" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
    </NSpace>
  </NForm>
</template>
