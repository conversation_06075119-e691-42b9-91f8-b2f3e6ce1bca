<script lang="ts" setup>
import dayjs from 'dayjs';
import { onMounted, ref } from 'vue';
import { fetchGetPlanList } from '@/service/api';
import { renderModal } from '@/components/re-modal';
import { usePlanStore } from '@/store/modules/plan';
import { useAuth } from '@/hooks/business/auth';
import PlanDetail from './modules/plan-detail.vue';
import PlanForm from './modules/plan-form.vue';
const value = ref(Date.now());
const currentMonth = ref(dayjs());

// 计划列表
const planLists = ref<Api.Crm.PlanList>([]);
const { hasAuth } = useAuth();
console.log(hasAuth(['crm:plans:update']));
async function getPlanList() {
  const { error, data } = await fetchGetPlanList({
    times: [
      currentMonth.value.startOf('month').format('YYYY-MM-DD'),
      currentMonth.value.endOf('month').format('YYYY-MM-DD')
    ]
  });
  if (error) {
    return;
  }
  planLists.value = data;
}

// 日历切换
function panelChange(info: { year: number; month: number }) {
  currentMonth.value = dayjs(`${info.year}-${info.month}`);
  getPlanList();
}

// 添加计划
function addPlan() {
  const store = usePlanStore();
  store.init();
  renderModal(
    PlanForm,
    { operateType: 'add' },
    {
      title: '创建计划',
      style: {
        width: '50%'
      },
      func: getPlanList
    }
  );
}

// 计划详情/编辑
function showPlan(item: Api.Crm.Plan) {
  renderModal(
    PlanDetail,
    { id: item.id, onBtnSubmit: getPlanList },
    {
      title: '编辑计划',
      style: {
        width: '70%'
      },
      func: getPlanList
    }
  );
}

// 按钮
function showPlanButton(item: Api.Crm.Plan): string {
  return `${dayjs(String(item.planDate)).format('A h:mm')} ${item.customers?.map(customer => customer.name).join(' & ')}`;
}

onMounted(() => {
  getPlanList();
});
</script>

<template>
  <div>
    <NCalendar v-model:value="value" @panel-change="panelChange">
      <template #header="{ year, month }">
        {{ year }}年{{ month }}月
        <NButton v-hasPermi="['crm:plans:create']" ghost size="small" type="primary" @click="addPlan">添加计划</NButton>
      </template>
      <template #default="{ year, month, date }">
        <div v-for="planItem in planLists" :key="planItem.id" @click="showPlan(planItem)">
          <NPerformantEllipsis
            v-if="dayjs(year + '-' + month + '-' + date).isSame(dayjs(String(planItem.planDate)), 'day')"
            class="mt-3px cursor-pointer"
            tooltip
          >
            <span class="single-plan">{{ showPlanButton(planItem) }}</span>
            <template #tooltip>
              <div>
                {{ showPlanButton(planItem) }}
              </div>
            </template>
          </NPerformantEllipsis>
        </div>
      </template>
    </NCalendar>
  </div>
</template>

<style lang="scss" scoped>
:deep(.n-calendar .n-calendar-cell) {
  cursor: auto;
}

.single-plan {
  color: rgb(var(--primary-color));
  border: 1px solid rgb(var(--primary-color));
  border-radius: 5px;
  margin-bottom: 5px;
  padding: 2px 5px;
  font-size: 13px;
}
</style>
