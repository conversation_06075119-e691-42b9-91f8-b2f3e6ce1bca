<script lang="tsx" setup>
import { NButton, NDropdown, NTag, NTooltip } from 'naive-ui';
import { h } from 'vue';
import { ProjectStatus, projectStatusOptions, winningProbabilityOptions } from '@/enum';
import { getEnumTagType, getEnumValue, showUserStr } from '@/utils/useful_func';
import { useTable } from '@/hooks/common/table';
import { $t } from '@/locales';
import { fetchGetProjectList } from '@/service/api';
import { useAppStore } from '@/store/modules/app';
import { renderModal, renderModalBtn } from '@/components/re-modal';
import { useProjectMarkerStore } from '@/store/modules/project/project-marker';
import { useAuthStore } from '@/store/modules/auth';
import { checkHasRole } from '@/directives/permission/hasRole';
import { editRoleBtn } from '@/directives/permission/permi-btn';
import ProjectStateModal from './modules/project-state.vue';
import ProjectOperateModal from './modules/project-operate-model.vue';
import ProjectSearch from './modules/project-search.vue';
import TransferModal from './modules/transfer-modal.vue';

const appStore = useAppStore();
const userStore = useAuthStore();
const store = useProjectMarkerStore();
const {
  columns,
  columnChecks,
  data,
  getData,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams,
  getDataByPage
} = useTable({
  apiFn: fetchGetProjectList,
  showTotal: true,
  apiParams: {
    all: true,
    pageNo: 1,
    pageSize: 10,
    status: null,
    bankName: null,
    ownerUserId: null
  },
  columns: () => [
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'bankName',
      title: '银行名称',
      align: 'center',
      minWidth: 100,
      render: (rowData: Api.Crm.Project) => `${rowData.bankName ? `${rowData.bankName}(${rowData.businessType})` : '-'}`
    },
    {
      key: 'ownerUserId',
      title: '商务负责人',
      align: 'center',
      minWidth: 100,
      render: (row: Api.Crm.Project) => {
        return row.user ? <NTag type="info">{showUserStr(row?.user)}</NTag> : '-';
      }
    },
    {
      key: 'city',
      title: '城市',
      align: 'center',
      minWidth: 100
    },
    {
      key: 'winningProbability',
      title: '中标率',
      align: 'center',
      minWidth: 100,
      render: row => getEnumValue(winningProbabilityOptions, row.winningProbability)
    },
    {
      key: 'singleEstimatedValue',
      title: '预估规模',
      align: 'center',
      minWidth: 100,
      render: (rowData: Api.Crm.Project) => `${rowData.singleEstimatedValue ? `${rowData.singleEstimatedValue}` : '-'}`
    },

    {
      key: 'status',
      title: '状态',
      align: 'center',
      width: 100,
      render: row => {
        if (row.status === null) {
          return null;
        }

        return (
          <NTag type={getEnumTagType(projectStatusOptions, row.status)}>
            {getEnumValue(projectStatusOptions, row.status)}
          </NTag>
        );
      }
    },
    {
      key: 'updateTime',
      title: '更新时间',
      align: 'center',
      minWidth: 100,
      render: (rowData: Api.Crm.Project) => (
        <NTooltip trigger="hover" placement="top">
          {{
            default: () => h('div', null, rowData.updaterName ? `更新人：${rowData.updaterName}` : ''),
            trigger: () => <div>{rowData.updateTime}</div>
          }}
        </NTooltip>
      )
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 200,
      render: (row: Api.Crm.Project) => {
        const options = dropdownOptions(row);
        return (
          <div class="flex-center gap-8px">
            {editRoleBtn(['crm:projects:update'], () => edit(row.id))}
            <NButton type="success" ghost size="small" onClick={() => info(row.id)}>
              详情
            </NButton>
            {options.length ? (
              <NDropdown
                trigger="hover"
                placement="right-start"
                options={dropdownOptions(row)}
                onSelect={(key: string) => {
                  if (key === 'establishment') {
                    edit(row.id, true);
                  } else if (key === 'transfer') {
                    transfer(row, false);
                  } else if (key === 'claim') {
                    transfer(row, true);
                  }
                }}
              >
                <NButton size="small" type="primary" ghost>
                  ...
                </NButton>
              </NDropdown>
            ) : null}
          </div>
        );
      }
    }
  ]
});
function dropdownOptions(row: Api.Crm.Project) {
  const options = [];

  if (checkHasRole(['crm', 'crm_admin']) && row.status === ProjectStatus.Clue) {
    options.push({
      label: '立项',
      key: 'establishment'
    });
  }

  if (checkHasRole(['crm_admin']) || Number(userStore.userInfo.id) === row.ownerUserId) {
    options.push({
      label: '交接',
      key: 'transfer'
    });
  }

  if (checkHasRole(['crm']) && !row.ownerUserId) {
    options.push({
      label: '认领',
      key: 'claim'
    });
  }

  return options;
}
function handleAddProject() {
  edit(0);
}
// 交接 | 认领
function transfer(rowData: Api.Crm.Project, isReceive: boolean) {
  renderModalBtn(
    TransferModal,
    {
      rowData,
      isReceive
    },
    {
      title: !isReceive ? '交接' : '项目认领说明',
      style: {
        width: '30%'
      },
      func: getData
    }
  );
}
function edit(id: number, isMarker = false) {
  store.setIsMarker(isMarker);
  renderModal(
    ProjectOperateModal,
    {
      projectId: id,
      isMarker
    },
    {
      title: isMarker ? '立项' : '编辑项目',
      style: {
        width: '80%'
      },
      closeOnEsc: false,
      func: getData,
      onClose() {
        getData();
        return true;
      }
    }
  );
}

function info(id: number) {
  renderModal(
    ProjectStateModal,
    {
      id
    },
    {
      title: '项目详情',
      style: {
        width: '80%'
      },
      func: getData
    }
  );
}
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <ProjectSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <NCard :bordered="false" class="sm:flex-1-hidden card-wrapper" size="small" title="项目管理">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :loading="loading"
          :add-disabled="true"
          :permission="['crm:projects:create']"
          @add="handleAddProject"
          @refresh="getData"
        />
      </template>
      <NDataTable
        :columns="columns"
        :data="data"
        :flex-height="!appStore.isMobile"
        :loading="loading"
        :pagination="mobilePagination"
        :row-key="row => row.id"
        :scroll-x="962"
        class="sm:h-full"
        remote
        size="small"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
