<script lang="ts" setup>
import { useProjectStore } from '@/store/modules/project';
import { fetchUpdateProject } from '@/service/api';
import { $t } from '@/locales';
import { useNaiveForm } from '@/hooks/common/form';
import { checkHasRole } from '@/directives/permission/hasRole';
import { renderModalBtn } from '@/components/re-modal';
import TransferModal from '@/views/crm/project/modules/transfer-modal.vue';

const { formRef } = useNaiveForm();
const store = useProjectStore();
async function handleSubmit() {
  store.submitCheck();
  if (checkHasRole(['crm']) && !store.info.ownerUserId) {
    renderModalBtn(
      TransferModal,
      {
        rowData: store.info,
        isReceive: true
      },
      {
        title: '认领项目',
        style: {
          width: '30%'
        },
        func: apiFun
      }
    );
  } else {
    await apiFun();
  }
}
async function apiFun() {
  const { error } = await fetchUpdateProject(store.info);
  if (error) {
    return;
  }
  window.$message?.success($t('common.modifySuccess'));
}
</script>

<template>
  <NForm ref="formRef" class="mt-5px" :model="store.info">
    <NGrid x-gap="12" :cols="2">
      <NGi>
        <NFormItem label="标准工作日" path="standardWorkdays">
          <NInput v-model:value="store.info.standardWorkdays" placeholder="请输入标准工作日" clearable class="w-full" />
        </NFormItem>
      </NGi>
      <NGi>
        <NFormItem label="排班要求" path="schedulingReq">
          <NInput v-model:value="store.info.schedulingReq" placeholder="请输入排班要求" clearable class="w-full" />
        </NFormItem>
      </NGi>
      <NGi>
        <NFormItem label="培训周期" path="trainingCycle">
          <NInput v-model:value="store.info.trainingCycle" placeholder="请输入培训周期" clearable class="w-full" />
        </NFormItem>
      </NGi>
      <NGi>
        <NFormItem label="培训是否结算" path="trainingSettle">
          <NRadioGroup v-model:value="store.info.trainingSettle">
            <NRadio :value="0">否</NRadio>
            <NRadio :value="1">是</NRadio>
          </NRadioGroup>
        </NFormItem>
      </NGi>
      <NGi>
        <NFormItem label="培训支持" path="trainingSupport">
          <NInput v-model:value="store.info.trainingSupport" placeholder="请输入培训支持" clearable class="w-full" />
        </NFormItem>
      </NGi>
      <NGi>
        <NFormItem label="驻场管理" path="onSiteMgmt">
          <NInput v-model:value="store.info.onSiteMgmt" placeholder="请输入驻场管理" clearable class="w-full" />
        </NFormItem>
      </NGi>
      <NGi>
        <NFormItem label="业务系统" path="businessSys">
          <NInput v-model:value="store.info.businessType" placeholder="请输入业务系统" clearable class="w-full" />
        </NFormItem>
      </NGi>
      <NGi>
        <NFormItem label="专线要求" path="dedicatedLineReq">
          <NInput v-model:value="store.info.dedicatedLineReq" placeholder="请输入业务系统" clearable class="w-full" />
        </NFormItem>
      </NGi>
      <NGi>
        <NFormItem label="线路要求" path="lineReq">
          <NInput v-model:value="store.info.lineReq" placeholder="请输入线路要求" clearable class="w-full" />
        </NFormItem>
      </NGi>
      <NGi>
        <NFormItem label="产品确认" path="productConfirm">
          <NInput v-model:value="store.info.productConfirm" placeholder="请输入产品确认" clearable class="w-full" />
        </NFormItem>
      </NGi>
    </NGrid>
    <div class="mt-4 flex justify-end">
      <ReButton class="ml-3" type="primary" @click="handleSubmit" />
    </div>
  </NForm>
</template>

<style scoped lang="scss"></style>
