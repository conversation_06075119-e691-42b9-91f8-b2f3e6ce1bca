<script lang="ts" setup>
import { useProjectStore } from '@/store/modules/project';
import { fetchUpdateProject } from '@/service/api';
import { $t } from '@/locales';
import { useNaiveForm } from '@/hooks/common/form';
import { checkHasRole } from '@/directives/permission/hasRole';
import { renderModalBtn } from '@/components/re-modal';
import TransferModal from '@/views/crm/project/modules/transfer-modal.vue';

const { formRef } = useNaiveForm();
const store = useProjectStore();

async function handleSubmit() {
  store.submitCheck();
  if (checkHasRole(['crm']) && !store.info.ownerUserId) {
    renderModalBtn(
      TransferModal,
      {
        rowData: store.info,
        isReceive: true
      },
      {
        title: '认领项目',
        style: {
          width: '30%'
        },
        func: apiFun
      }
    );
  } else {
    await apiFun();
  }
}
async function apiFun() {
  const { error } = await fetchUpdateProject(store.info);
  if (error) {
    return;
  }
  window.$message?.success($t('common.modifySuccess'));
}
</script>

<template>
  <NForm ref="formRef" class="mt-5px" :model="store.info">
    <NGrid x-gap="12" :cols="2">
      <NGi>
        <NFormItem label="招标流程" path="biddingProcess">
          <NInput v-model:value="store.info.biddingProcess" placeholder="请输入招标流程" clearable class="w-full" />
        </NFormItem>
        <NFormItem label="供应量数量" path="supplyQty">
          <NInput v-model:value="store.info.supplyQty" placeholder="请输入供应量数量" clearable class="w-full" />
        </NFormItem>
        <NFormItem label="中标关键点" path="biddingKeyPoints">
          <NInput v-model:value="store.info.biddingKeyPoints" placeholder="请输入中标关键点" clearable class="w-full" />
        </NFormItem>
        <NFormItem label="合同年限" path="contractYears">
          <NInput v-model:value="store.info.contractYears" placeholder="请输入合同年限" clearable class="w-full" />
        </NFormItem>
      </NGi>
    </NGrid>
    <div class="mt-4 flex justify-end">
      <ReButton class="ml-3" type="primary" @click="handleSubmit" />
    </div>
  </NForm>
</template>

<style scoped lang="scss"></style>
