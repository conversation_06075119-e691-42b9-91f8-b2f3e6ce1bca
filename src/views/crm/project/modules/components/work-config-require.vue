<script lang="ts" setup>
import { useProjectStore } from '@/store/modules/project';
import { fetchUpdateProject } from '@/service/api';
import { $t } from '@/locales';
import { useNaiveForm } from '@/hooks/common/form';
import { checkHasRole } from '@/directives/permission/hasRole';
import { renderModalBtn } from '@/components/re-modal';
import TransferModal from '@/views/crm/project/modules/transfer-modal.vue';

const { formRef } = useNaiveForm();
const store = useProjectStore();

async function handleSubmit() {
  store.submitCheck();
  if (checkHasRole(['crm']) && !store.info.ownerUserId) {
    renderModalBtn(
      TransferModal,
      {
        rowData: store.info,
        isReceive: true
      },
      {
        title: '认领项目',
        style: {
          width: '30%'
        },
        func: apiFun
      }
    );
  } else {
    await apiFun();
  }
}
async function apiFun() {
  const { error } = await fetchUpdateProject(store.info);
  if (error) {
    return;
  }
  window.$message?.success($t('common.modifySuccess'));
}
</script>

<template>
  <NForm ref="formRef" class="mt-5px" :model="store.info">
    <NGrid x-gap="12" :cols="2">
      <NGi>
        <NFormItem label="离场职场建设成本" path="exitWorkplaceCost">
          <NInputNumber
            v-model:value="store.info.exitWorkplaceCost"
            placeholder="请输入离场职场建设成本"
            clearable
            class="w-full"
            :min="0"
            :max="99999999"
          >
            <template #suffix>万元</template>
          </NInputNumber>
        </NFormItem>
      </NGi>
    </NGrid>
    <div class="mt-4 flex justify-end">
      <ReButton class="ml-3" type="primary" @click="handleSubmit" />
    </div>
  </NForm>
</template>

<style scoped lang="scss">
// 单位
:deep(.n-input .n-input-number-suffix) {
  color: #9ca3af;
}

:deep(.n-input .n-input__suffix) {
  color: #9ca3af;
}

// 数字 +- 的按钮
:deep(.n-button .n-button__icon) {
  display: none;
}
</style>
