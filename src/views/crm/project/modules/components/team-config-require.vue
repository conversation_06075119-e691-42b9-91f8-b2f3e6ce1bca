<script lang="ts" setup>
import type { DataTableColumns } from 'naive-ui';
import { NButton, NInput } from 'naive-ui';
import { h, ref } from 'vue';
import { fetchUpdateProject } from '@/service/api';
import { useProjectStore } from '@/store/modules/project';
import { $t } from '@/locales';
import { checkHasRole } from '@/directives/permission/hasRole';
import { renderModalBtn } from '@/components/re-modal';
import TransferModal from '@/views/crm/project/modules/transfer-modal.vue';

const store = useProjectStore();
type RowData = Api.Crm.ProjectTeam;

function createTimea(): RowData[] {
  if (!store.info.mgmtStaffRatio?.length) {
    return [
      {
        key: 0,
        educationJob: '',
        educationReq: '',
        businessExp: '',
        mgmtStaffRatio: ''
      }
    ];
  }

  return store.info.mgmtStaffRatio;
}

const data = ref(createTimea());

const addRow = () => {
  data.value.push({
    key: data.value.length,
    educationJob: '',
    educationReq: '',
    businessExp: '',
    mgmtStaffRatio: ''
  });
};
const deleteRow = (index: number) => {
  data.value.splice(index, 1);
};

const createColumns = (): DataTableColumns<RowData> => [
  {
    title: '岗位',
    key: 'educationJob',
    render(row, index) {
      return h(NInput, {
        value: row.educationJob,
        onUpdateValue(v) {
          data.value[index].educationJob = v;
        }
      });
    }
  },
  {
    title: '学历要求',
    key: 'educationReq',
    render(row, index) {
      return h(NInput, {
        value: row.educationReq,
        onUpdateValue(v) {
          data.value[index].educationReq = v;
        }
      });
    }
  },
  {
    title: '业务经验',
    key: 'businessExp',
    render(row, index) {
      return h(NInput, {
        value: row.businessExp,
        onUpdateValue(v) {
          data.value[index].businessExp = v;
        }
      });
    }
  },
  {
    title: '配置要求',
    key: 'mgmtStaffRatio',
    render(row, index) {
      return h(NInput, {
        value: row.mgmtStaffRatio,
        onUpdateValue(v) {
          data.value[index].mgmtStaffRatio = v;
        }
      });
    }
  },
  {
    title: '操作',
    key: 'actions',
    render(_, index) {
      return h(
        NButton,
        {
          size: 'small',
          onClick: () => deleteRow(index)
        },
        { default: () => '删除' }
      );
    }
  }
];
const columns = createColumns();

async function handleSubmit() {
  store.submitCheck();
  if (checkHasRole(['crm']) && !store.info.ownerUserId) {
    renderModalBtn(
      TransferModal,
      {
        rowData: store.info,
        isReceive: true
      },
      {
        title: '认领项目',
        style: {
          width: '30%'
        },
        func: apiFun
      }
    );
  } else {
    await apiFun();
  }
}
async function apiFun() {
  const { error } = await fetchUpdateProject({ ...store.info, mgmtStaffRatio: data.value });
  if (error) {
    return;
  }
  window.$message?.success($t('common.modifySuccess'));
}
</script>

<template>
  <NDataTable :columns="columns" :data="data" />
  <div class="mt-5">
    <NButton class="w-1/1" @click="addRow">新增一行</NButton>
  </div>
  <div class="mt-4 flex justify-end">
    <ReButton class="ml-3" type="primary" @click="handleSubmit" />
  </div>
</template>
