<script lang="ts" setup>
import { ref } from 'vue';
import { useProjectStore } from '@/store/modules/project';
import { fetchUpdateProject } from '@/service/api';
import { $t } from '@/locales';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { useProjectMarkerStore } from '@/store/modules/project/project-marker';
import { checkHasRole } from '@/directives/permission/hasRole';
import { renderModalBtn } from '@/components/re-modal';
import TransferModal from '@/views/crm/project/modules/transfer-modal.vue';

const { formRef, validate } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();
const store = useProjectStore();
const projectMarkerStore = useProjectMarkerStore();

type RuleKey = Extract<keyof Api.Crm.ProjectBaseInfo, 'avgRevenueMonth' | 'reconSettleCycle'>;
const rules = ref<Record<RuleKey, App.Global.FormRule>>({
  ...(projectMarkerStore.isMarker
    ? {
        avgRevenueMonth: defaultRequiredRule,
        reconSettleCycle: defaultRequiredRule
      }
    : {})
});

async function handleSubmit() {
  await validate();
  store.submitCheck();
  if (checkHasRole(['crm']) && !store.info.ownerUserId) {
    renderModalBtn(
      TransferModal,
      {
        rowData: store.info,
        isReceive: true
      },
      {
        title: '认领项目',
        style: {
          width: '30%'
        },
        func: apiFun
      }
    );
  } else {
    await apiFun();
  }
}
async function apiFun() {
  const { error } = await fetchUpdateProject(store.info);
  if (error) {
    return;
  }
  window.$message?.success($t('common.modifySuccess'));
}
</script>

<template>
  <NForm ref="formRef" :model="store.info" :rules="rules">
    <NGrid x-gap="12" :cols="2">
      <NGi>
        <NFormItem label="结算规则及标准" path="settlementRule">
          <NInput
            v-model:value="store.info.settlementRule"
            placeholder="请输入结算规则及标准"
            clearable
            class="w-full"
          />
        </NFormItem>
      </NGi>
      <NGi>
        <NFormItem label="名单量/人/天" path="listQuantity">
          <NInputNumber
            v-model:value="store.info.listQuantity"
            placeholder="请输入名单量/人/天"
            :precision="0"
            clearable
            class="w-full"
          >
            <template #suffix>条</template>
          </NInputNumber>
        </NFormItem>
      </NGi>
      <NGi>
        <NFormItem label="名单接通率" path="connectRate">
          <NInputNumber
            v-model:value="store.info.connectRate"
            placeholder="请输入名单接通率"
            clearable
            :min="0"
            :max="99.9999"
            class="w-full"
          />
        </NFormItem>
      </NGi>
      <NGi>
        <NFormItem label="外呼方式及限制" path="callMethodLimit">
          <NCheckboxGroup v-model:value="store.info.callMethodLimit">
            <NSpace item-style="display: flex;">
              <NCheckbox value="1" label="预测" />
              <NCheckbox value="2" label="手拔" />
            </NSpace>
          </NCheckboxGroup>
        </NFormItem>
      </NGi>
      <NGi>
        <NFormItem label="接通量/人/天" path="connectQty">
          <NInputNumber
            v-model:value="store.info.connectQty"
            :precision="0"
            placeholder="请输入接通量/人/天"
            clearable
            class="w-full"
          >
            <template #suffix>通</template>
          </NInputNumber>
        </NFormItem>
      </NGi>
      <NGi>
        <NFormItem label="数据接通转化率" path="connectConv">
          <NInputNumber
            v-model:value="store.info.connectConv"
            placeholder="请输入数据接通转化率"
            clearable
            class="w-full"
          />
        </NFormItem>
      </NGi>
      <NGi>
        <NFormItem label="日人均成功单量" path="avgSuccessCnt">
          <NInputNumber
            v-model:value="store.info.avgSuccessCnt"
            placeholder="请输入日人均成功单量"
            clearable
            class="w-full"
          />
        </NFormItem>
      </NGi>
      <NGi>
        <NFormItem label="退单率" path="refundRate">
          <NInputNumber
            v-model:value="store.info.refundRate"
            placeholder="请输入退单率"
            clearable
            class="w-full"
            :min="0"
            :max="99.9999"
          />
        </NFormItem>
      </NGi>
      <NGi>
        <NFormItem label="件均" path="avgOrder">
          <NInputNumber v-model:value="store.info.avgOrder" placeholder="请输入件均" clearable class="w-full" />
        </NFormItem>
      </NGi>
      <NGi>
        <NFormItem label="月人均业绩" path="avgPerfMonth">
          <NInputNumber
            v-model:value="store.info.avgPerfMonth"
            placeholder="请输入月人均业绩"
            :min="0"
            :max="99999999"
            clearable
            class="w-full"
          >
            <template #suffix>万元</template>
          </NInputNumber>
        </NFormItem>
      </NGi>
      <NGi>
        <NFormItem label="本业务现有供应商月人均营收" path="avgRevenueMonth">
          <NInputNumber
            v-model:value="store.info.avgRevenueMonth"
            placeholder="请输入本业务现有供应商月人均营收"
            clearable
            class="w-full"
            :min="0"
            :max="99999999"
          >
            <template #suffix>元</template>
          </NInputNumber>
        </NFormItem>
      </NGi>
      <NGi>
        <NFormItem label="结算标准" path="settlementStd">
          <NInput v-model:value="store.info.settlementStd" placeholder="请输入结算标准" clearable class="w-full" />
        </NFormItem>
      </NGi>
      <NGi>
        <NFormItem label="结算系数" path="settlementFactor">
          <NInput v-model:value="store.info.settlementFactor" placeholder="请输入结算系数" clearable class="w-full" />
        </NFormItem>
      </NGi>
      <NGi>
        <NFormItem label="扣罚风险点" path="penaltyRisk">
          <NInput v-model:value="store.info.penaltyRisk" placeholder="请输入扣罚风险点" clearable class="w-full" />
        </NFormItem>
      </NGi>
      <NGi>
        <NFormItem label="扶持政策" path="supportPolicy">
          <NInput v-model:value="store.info.supportPolicy" placeholder="请输入扶持政策" clearable class="w-full" />
        </NFormItem>
      </NGi>
      <NGi>
        <NFormItem label="甲方收取的系统费用金额" path="systemCost">
          <NInputNumber
            v-model:value="store.info.systemCost"
            placeholder="请输入甲方收取的系统费用金额"
            clearable
            :min="0"
            :max="99999999"
            class="w-full"
          >
            <template #suffix>元</template>
          </NInputNumber>
        </NFormItem>
      </NGi>
      <NGi>
        <NFormItem label="甲方收取的的话费费用金额" path="callCost">
          <NInputNumber
            v-model:value="store.info.callCost"
            placeholder="请输入甲方收取的的话费费用金额"
            clearable
            :min="0"
            :max="99999999"
            class="w-full"
          >
            <template #suffix>元</template>
          </NInputNumber>
        </NFormItem>
      </NGi>
      <NGi>
        <NFormItem label="甲方收取的的职场费用金额" path="workplaceCost">
          <NInputNumber
            v-model:value="store.info.workplaceCost"
            placeholder="请输入甲方收取的的职场费用金额"
            clearable
            :min="0"
            :max="99999999"
            class="w-full"
          >
            <template #suffix>元</template>
          </NInputNumber>
        </NFormItem>
      </NGi>
      <NGi>
        <NFormItem label="排名规则" path="rankingRule">
          <NInput v-model:value="store.info.rankingRule" placeholder="请输入排名规则" clearable class="w-full" />
        </NFormItem>
      </NGi>
      <NGi>
        <NFormItem label="对账及结算周期" path="reconSettleCycle">
          <NRadioGroup v-model:value="store.info.reconSettleCycle">
            <NRadio :value="1">季度支付</NRadio>
            <NRadio :value="2">月度支付</NRadio>
            <NRadio :value="3">年度支付</NRadio>
          </NRadioGroup>
        </NFormItem>
      </NGi>
      <NGi>
        <NFormItem label="发票类型" path="invoiceType">
          <NRadioGroup v-model:value="store.info.invoiceType">
            <NRadio :value="1">专票</NRadio>
            <NRadio :value="2">普票</NRadio>
          </NRadioGroup>
        </NFormItem>
      </NGi>
      <NGi>
        <NFormItem label="友商项目赢利情况" path="competitorProfit">
          <NInput
            v-model:value="store.info.competitorProfit"
            placeholder="请输入友商项目赢利情况"
            clearable
            class="w-full"
          />
        </NFormItem>
      </NGi>
      <NGi>
        <NFormItem label="其它说明事宜" path="otherNotes">
          <NInput v-model:value="store.info.otherNotes" placeholder="请输入其它说明事宜" clearable class="w-full" />
        </NFormItem>
      </NGi>
    </NGrid>
    <div class="mt-4 flex justify-end">
      <ReButton class="ml-3" type="primary" @click="handleSubmit" />
    </div>
  </NForm>
</template>

<style scoped lang="scss">
// 单位
:deep(.n-input .n-input-number-suffix) {
  color: #9ca3af;
}

:deep(.n-input .n-input__suffix) {
  color: #9ca3af;
}

// 数字 +- 的按钮
:deep(.n-button .n-button__icon) {
  display: none;
}
</style>
