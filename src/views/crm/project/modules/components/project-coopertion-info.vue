<script lang="ts" setup>
import { ref } from 'vue';
import { winningProbabilityOptions } from '@/enum';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { useProjectStore } from '@/store/modules/project';
import { $t } from '@/locales';
import { fetchAddProject, fetchUpdateProject } from '@/service/api';
import { getTargetCustomerOptions } from '@/utils/async-functions';
import SelectWithSearch from '@/components/common/select-with-search.vue';
import DateTimePicker from '@/components/common/date-time-picker.vue';
import { useProjectMarkerStore } from '@/store/modules/project/project-marker';
import { renderModalBtn } from '@/components/re-modal';
import TransferModal from '@/views/crm/project/modules/transfer-modal.vue';
import { checkHasRole } from '@/directives/permission/hasRole';

const { formRef, validate } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();
const store = useProjectStore();
const projectMarkerStore = useProjectMarkerStore();
const rules = ref<Record<RuleKey, App.Global.FormRule>>({
  targetCustomerId: defaultRequiredRule,
  bankName: defaultRequiredRule,
  ownerUserId: defaultRequiredRule,
  middleman: defaultRequiredRule,
  status: defaultRequiredRule,
  businessType: defaultRequiredRule,
  ...(projectMarkerStore.isMarker
    ? {
        city: defaultRequiredRule,
        totalScale: defaultRequiredRule,
        workplaceType: defaultRequiredRule,
        requirement: defaultRequiredRule,
        baseMode: defaultRequiredRule
      }
    : {})
});
type RuleKey = Extract<
  keyof Api.Crm.ProjectBaseInfo,
  'targetCustomerId' | 'bankName' | 'ownerUserId' | 'middleman' | 'status' | 'customerCity' | 'businessType'
>;
async function handleSubmit() {
  await validate();
  if (store.getIsCreate()) {
    const { error, data } = await fetchAddProject(store.info);
    if (error) {
      return;
    }

    store.setProjectId(data.id);
    window.$message?.success($t('common.addSuccess'));
  } else if (checkHasRole(['crm']) && !store.info.ownerUserId) {
    renderModalBtn(
      TransferModal,
      {
        rowData: store.info,
        isReceive: true
      },
      {
        title: '认领项目',
        style: {
          width: '30%'
        },
        func: apiFun
      }
    );
  } else {
    await apiFun();
  }
}
async function apiFun() {
  const { error } = await fetchUpdateProject(store.info);
  if (error) {
    return;
  }
  window.$message?.success($t('common.modifySuccess'));
}
const onlyAllowNumber = (value: string) => !value || /^\d+$/.test(value);

// 自动填充银行名称
function selectTargetCustomer(option: CommonType.Option<number> | null | CommonType.Option<number>[]) {
  if (store.getIsCreate() && option) {
    const selectOption = option as CommonType.Option<number>;
    store.info.bankName = selectOption.name;
    store.info.businessType = selectOption.businessType;
  }
}
</script>

<template>
  <div class="mt-5">
    <NForm ref="formRef" class="mt-5px" :model="store.info" :rules="rules">
      <NGrid :cols="2" x-gap="12">
        <NGi>
          <NFormItem label="目标客户" path="targetCustomerId">
            <SelectWithSearch
              v-model:value="store.info.targetCustomerId"
              :api-func="getTargetCustomerOptions"
              :selected-options="[store.info.targetCustomer]"
              class="w-full"
              placeholder="目标客户"
              @value-changed="selectTargetCustomer"
            />
          </NFormItem>
          <NFormItem label="银行名称" path="bankName">
            <NInput v-model:value="store.info.bankName" class="w-full" clearable placeholder="请输入银行名称" />
          </NFormItem>
          <NFormItem label="业务类型" path="businessType">
            <NInput v-model:value="store.info.businessType" class="w-full" clearable placeholder="请输入业务类型" />
          </NFormItem>
          <NFormItem label="投标时间" path="biddingTime">
            <DateTimePicker
              v-model:time="store.info.biddingTime"
              class="w-full"
              label="投标时间"
              use-for="month_date"
            />
          </NFormItem>
          <NFormItem label="合同结束时间" path="contractEndTime">
            <DateTimePicker
              v-model:time="store.info.contractEndTime"
              class="w-full"
              label="合同结束时间"
              use-for="date"
            />
          </NFormItem>
          <NFormItem label="预计启动时间" path="expectedStartTime">
            <DateTimePicker
              v-model:time="store.info.expectedStartTime"
              class="w-full"
              label="预计启动时间"
              use-for="month_date"
            />
          </NFormItem>
          <NFormItem label="合作年限" path="partnerDuration">
            <NInput v-model:value="store.info.partnerDuration" class="w-full" clearable placeholder="请输入合作年限" />
          </NFormItem>
          <NFormItem label="总规模" path="totalScale">
            <NInput v-model:value="store.info.totalScale" class="w-full" clearable placeholder="请输入总规模" />
          </NFormItem>
          <NFormItem label="入围家数" path="entryCountries">
            <NInput v-model:value="store.info.entryCountries" class="w-full" clearable placeholder="请输入入围家数" />
          </NFormItem>
          <NFormItem label="预估规模" path="singleEstimatedValue">
            <NInput
              v-model:value="store.info.singleEstimatedValue"
              class="w-full"
              clearable
              placeholder="请输入预估规模"
            />
          </NFormItem>

          <NFormItem label="职场类型" path="workplaceType">
            <NRadioGroup v-model:value="store.info.workplaceType">
              <NRadio :value="0">驻场</NRadio>
              <NRadio :value="1">离场</NRadio>
              <NRadio :value="2">驻场和离场</NRadio>
              <NRadio :value="3">驻场或离场</NRadio>
            </NRadioGroup>
          </NFormItem>
        </NGi>
        <NGi>
          <NFormItem label="职场城市要求" path="requirement">
            <NInput v-model:value="store.info.requirement" class="w-full" clearable placeholder="请输入职场城市要求" />
          </NFormItem>
          <NFormItem label="居间人" path="middleman">
            <NRadioGroup v-model:value="store.info.middleman">
              <NRadio value="0">无</NRadio>
              <NRadio value="1">有</NRadio>
            </NRadioGroup>
          </NFormItem>
          <NFormItem label="合作中的老供应商信息（供应商名称、规模、上次报价）" path="supplierInfo">
            <NInput
              v-model:value="store.info.supplierInfo"
              class="w-full"
              clearable
              type="textarea"
              rows="1"
              placeholder="请输入合作中的老供应商信息（供应商名称、规模、上次报价）"
            />
          </NFormItem>
          <NFormItem label="首批开班人数" path="firstBatchSeats">
            <NInput
              v-model:value="store.info.firstBatchSeats"
              :allow-input="onlyAllowNumber"
              class="w-full"
              clearable
              placeholder="请输入首批开班人数"
            >
              <template #suffix>席</template>
            </NInput>
          </NFormItem>
          <NFormItem label="首批开班日期" path="projectStartDate">
            <DateTimePicker
              v-model:time="store.info.projectStartDate"
              class="w-full"
              label="首批开班日期"
              use-for="date"
            />
          </NFormItem>
          <NFormItem label="首批上线日期" path="projectOnlineDate">
            <DateTimePicker
              v-model:time="store.info.projectOnlineDate"
              class="w-full"
              label="首批上线日期"
              use-for="date"
            />
          </NFormItem>
          <NFormItem label="城市" path="city">
            <NInput v-model:value="store.info.city" class="w-full" clearable placeholder="请输入城市" />
          </NFormItem>
          <NFormItem label="人均营收" path="averageRevenue">
            <NInput v-model:value="store.info.averageRevenue" class="w-full" clearable placeholder="请输入人均营收" />
          </NFormItem>

          <NFormItem label="基地规模" path="baseMode">
            <NInput v-model:value="store.info.baseMode" class="w-full" clearable placeholder="请输入基地规模" />
          </NFormItem>
          <NFormItem label="中标率" path="winningProbability">
            <NRadioGroup v-model:value="store.info.winningProbability">
              <NRadio v-for="item in winningProbabilityOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </NRadio>
            </NRadioGroup>
          </NFormItem>
          <NFormItem label="备注" path="remark">
            <NInput
              v-model:value="store.info.remark"
              class="w-full"
              clearable
              placeholder="请输入备注"
              type="textarea"
              rows="1"
            />
          </NFormItem>
        </NGi>
      </NGrid>
      <div class="mt-4 flex justify-end">
        <ReButton class="ml-3" type="primary" @click="handleSubmit" />
      </div>
    </NForm>
  </div>
</template>

<style lang="scss" scoped>
// 单位
:deep(.n-input .n-input-number-suffix) {
  color: #9ca3af;
}
:deep(.n-input .n-input__suffix) {
  color: #9ca3af;
}
// 数字 +- 的按钮
:deep(.n-button .n-button__icon) {
  display: none;
}
</style>
