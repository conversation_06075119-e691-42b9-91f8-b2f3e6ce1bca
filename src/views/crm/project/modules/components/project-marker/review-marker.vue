<script lang="ts" setup>
import { ref } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { fetchPutReviewMarkersProject } from '@/service/api';
import { ReviewStatus, reviewStatusOptions } from '@/enum';
import { useProjectMarkerStore } from '@/store/modules/project/project-marker';
import { showUserStr } from '@/utils/useful_func';

const store = useProjectMarkerStore();

const { formRef, validate } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();
type RuleKey = Extract<keyof Api.Crm.Marker, 'confirmComment' | 'confirmResult'>;

const rules = ref<Record<RuleKey, App.Global.FormRule>>({
  confirmResult: defaultRequiredRule,
  confirmComment: defaultRequiredRule
});

async function handleSubmit() {
  await validate();
  // 请求参数
  const params: Api.Crm.MarkerReview = {
    ...store.info
  };

  if (store.info.confirmResult === ReviewStatus.Reviewing) {
    window.$message?.error('请先选择评审状态');
    return;
  }

  // 编辑
  if (!store.info.id) {
    window.$message?.error('未发起立项申请');
    return;
  }
  const { error } = await fetchPutReviewMarkersProject(params);

  if (error) return;
  await store.getInfo();
  window.$message?.success('更新成功');
}
</script>

<template>
  <NCard
    title="立项评审"
    :segmented="{
      content: true,
      footer: 'soft'
    }"
  >
    <NForm ref="formRef" :model="store.info" :rules="rules">
      <div v-if="store.showResult()">
        <NFormItem label="评审人">
          <NTag type="warning" size="small">{{ showUserStr(store.info?.confirmUser) }}</NTag>
        </NFormItem>
        <NFormItem label="评审时间">
          {{ store.info.confirmedAt }}
        </NFormItem>
      </div>
      <NFormItem label="评审状态" path="confirmResult">
        <NRadioGroup v-model:value="store.info.confirmResult">
          <NRadioButton
            v-for="(item, index) in reviewStatusOptions"
            :key="index"
            :disabled="item.value === ReviewStatus.Reviewing"
            :value="item.value"
          >
            {{ item.label }}
          </NRadioButton>
        </NRadioGroup>
      </NFormItem>
      <NFormItem label="评审说明" path="confirmComment">
        <NInput
          v-model:value="store.info.confirmComment"
          :rows="4"
          class="w-full"
          clearable
          placeholder="请输入评审说明"
          type="textarea"
        />
      </NFormItem>
    </NForm>
    <template #footer>
      <div class="mt-4 flex justify-end">
        <ReButton class="ml-3" type="primary" @click="handleSubmit" />
      </div>
    </template>
  </NCard>
</template>

<style lang="scss" scoped>
// 单位
:deep(.n-input .n-input-number-suffix) {
  color: #9ca3af;
}
:deep(.n-input .n-input__suffix) {
  color: #9ca3af;
}
// 数字 +- 的按钮
:deep(.n-button .n-button__icon) {
  display: none;
}
</style>
