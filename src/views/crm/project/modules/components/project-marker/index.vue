<script setup lang="ts">
import { getEnumTagType, getEnumValue } from '@/utils/useful_func';
import { reviewStatusOptions } from '@/enum';
import { useAuthStore } from '@/store/modules/auth';
import { useProjectMarkerStore } from '@/store/modules/project/project-marker';
import { useVerifyStore } from '@/store/modules/project/verify';
import TiggerMarker from './tigger-marker.vue';
import ReviewMarker from './review-marker.vue';

interface ProjectModal {
  id: number;
}

const props = defineProps<ProjectModal>();

const store = useProjectMarkerStore();
const verifyStore = useVerifyStore();
store.init();
store.setProjectId(props.id);
store.getInfo();

const userStore = useAuthStore();
</script>

<template>
  <NAlert v-if="store.showResult()" :type="getEnumTagType(reviewStatusOptions, store.confirmResult)" class="mb-5">
    {{ getEnumValue(reviewStatusOptions, store.confirmResult) }}
  </NAlert>
  <NAlert v-if="verifyStore.isShowVerify" type="error" class="mb-5">
    {{ verifyStore.verifyText }}
  </NAlert>
  <NRow :gutter="10">
    <NCol :span="14">
      <TiggerMarker />
    </NCol>
    <NCol v-hasPermi="['crm:markers:review']" :span="10">
      <ReviewMarker />
    </NCol>
  </NRow>
</template>

<style scoped></style>
