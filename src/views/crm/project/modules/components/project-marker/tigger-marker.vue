<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { fetchPostTiggerMarkersProject, fetchPutTiggerMarkersProject } from '@/service/api';
import { ProjectMarker, ReviewStatus } from '@/enum';
import { useProjectMarkerStore } from '@/store/modules/project/project-marker';
import { useVerifyStore } from '@/store/modules/project/verify';
import { showUserStr } from '@/utils/useful_func';

const store = useProjectMarkerStore();
const verifyStore = useVerifyStore();
const { formRef, validate } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();
type RuleKey = Extract<keyof Api.Crm.Marker, 'tiggerComment'>;
const rules = ref<Record<RuleKey, App.Global.FormRule>>({
  tiggerComment: defaultRequiredRule
});

const btnText = computed(() => {
  if (!store.info.id) {
    return () => '提交';
  }
  if (store.confirmResult === ReviewStatus.Reviewing) {
    return () => '更新';
  }
  if (store.confirmResult === ReviewStatus.Failed) {
    return () => '重新提交';
  }
  return () => '提交';
});
async function handleSubmit() {
  await verifyStore.clickShowVerify();
  if (verifyStore.isShowVerify) {
    return;
  }
  await validate();
  // 请求参数
  const params: Api.Crm.MarkerTigger = {
    ...store.info,
    scene: ProjectMarker.Approval,
    projectId: Number(store.info.projectId)
  };

  // 编辑
  if (!store.info.id || store.info.confirmResult === ReviewStatus.Failed) {
    const { error } = await fetchPostTiggerMarkersProject(params);

    if (error) {
      return;
    }
    window.$message?.success('提交成功');
  } else {
    const { error } = await fetchPutTiggerMarkersProject(params);

    if (error) {
      return;
    }
    window.$message?.success('更新成功');
  }
  store.getInfo();
}
onMounted(() => {
  verifyStore.setShowVerify();
});
</script>

<template>
  <NCard
    title="发起立项"
    :segmented="{
      content: true,
      footer: 'soft'
    }"
  >
    <NForm ref="formRef" :model="store.info" :rules="rules">
      <div v-if="store.info.id">
        <NFormItem label="发起人">
          <NTag type="warning" size="small">{{ showUserStr(store.info?.tiggerUser) }}</NTag>
        </NFormItem>
        <NFormItem label="发起时间">
          {{ store.info.createTime }}
        </NFormItem>
      </div>
      <NFormItem label="立项说明" path="tiggerComment">
        <NInput
          v-model:value="store.info.tiggerComment"
          :rows="4"
          class="w-full"
          clearable
          placeholder="请输入立项说明"
          type="textarea"
        />
      </NFormItem>
    </NForm>
    <template #footer>
      <div class="mt-4 flex justify-end">
        <ReButton class="ml-3" type="primary" @click="handleSubmit">{{ btnText() }}</ReButton>
      </div>
    </template>
  </NCard>
</template>
