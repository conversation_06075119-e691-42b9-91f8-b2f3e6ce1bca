<script lang="ts" setup>
import { useProjectStore } from '@/store/modules/project';
import { fetchUpdateProject } from '@/service/api';
import { $t } from '@/locales';
import { useNaiveForm } from '@/hooks/common/form';

const { formRef } = useNaiveForm();
const store = useProjectStore();

async function handleSubmit() {
  store.submitCheck();
  const { error } = await fetchUpdateProject(store.info);
  if (error) {
    return;
  }
  window.$message?.success($t('common.modifySuccess'));
}
</script>

<template>
  <NForm ref="formRef" class="mt-5px" :model="store.info">
    <NGrid x-gap="12" :cols="2">
      <NGi>
        <NFormItem label="名单分配规则" path="listAllocRule">
          <NInput v-model:value="store.info.listAllocRule" placeholder="请输入名单分配规则" clearable class="w-full" />
        </NFormItem>
      </NGi>
      <NGi>
        <NFormItem label="新商户名单发放规则" path="merchantDistRule">
          <NInput
            v-model:value="store.info.merchantDistRule"
            placeholder="请输入新商户名单发放规则"
            clearable
            class="w-full"
          />
        </NFormItem>
      </NGi>
    </NGrid>
    <div class="mt-4 flex justify-end">
      <ReButton class="ml-3" type="primary" @click="handleSubmit" />
    </div>
  </NForm>
</template>

<style scoped lang="scss"></style>
