<script setup lang="ts">
import { useProjectStore } from '@/store/modules/project';
import { useVerifyStore } from '@/store/modules/project/verify';
import ProjectCoopertionInfo from './components/project-coopertion-info.vue';
import DataConfirmation from './components/data-confirmation.vue';
import ServiceContentConfirm from './components/service-content-confirm.vue';
import SettleRelatedConfirm from './components/settle-related-confirm.vue';
import TeamConfigRequire from './components/team-config-require.vue';
import TenderConfirmation from './components/tender-confirmation.vue';
import WorkConfigRequire from './components/work-config-require.vue';
import ProjectMarker from './components/project-marker/index.vue';
interface ProjectModal {
  projectId: number;
  isMarker: boolean;
}

const props = defineProps<ProjectModal>();
const verifyStore = useVerifyStore();
const projectStore = useProjectStore();
projectStore.init();
verifyStore.init();
projectStore.setProjectId(props.projectId);

if (!projectStore.getIsCreate()) {
  projectStore.getInfo();
}
</script>

<template>
  <NScrollbar :content-style="{ height: '70vh' }" trigger="hover">
    <NTabs>
      <NTabPane v-if="isMarker" name="marker" tab="发起立项">
        <ProjectMarker :id="projectId" />
      </NTabPane>
      <NTabPane name="info" tab="合作方基本信息">
        <ProjectCoopertionInfo />
      </NTabPane>
      <NTabPane name="contentSure" tab="服务内容确认">
        <ServiceContentConfirm />
      </NTabPane>
      <NTabPane name="dataSure" tab="数据确认">
        <DataConfirmation />
      </NTabPane>
      <NTabPane name="config" tab="团队配置要求">
        <TeamConfigRequire />
      </NTabPane>
      <NTabPane name="work" tab="职场配置要求">
        <WorkConfigRequire />
      </NTabPane>
      <NTabPane name="settle" tab="结算相关确认">
        <SettleRelatedConfirm />
      </NTabPane>
      <NTabPane name="tender" tab="招标确认">
        <TenderConfirmation />
      </NTabPane>
    </NTabs>
  </NScrollbar>
</template>
