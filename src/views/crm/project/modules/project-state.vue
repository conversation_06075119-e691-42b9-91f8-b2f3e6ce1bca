<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { getEnumOptionInfo, projectStatusOptions } from '@/enum';
import { fetchGetProjectDetail, fetchGetProjectState } from '@/service/api';
import { showUserStr } from '@/utils/useful_func';

interface Props {
  id: number;
}

const props = defineProps<Props>();
const percentage = ref(0);
const currentState = ref(1);
const totalStates = projectStatusOptions.length;

// 项目详情
type ProjectInfo = CommonType.RecordNullable<Api.Crm.Project>;
const projectInfo = ref<ProjectInfo>({});
const fetchProjectInfo = async () => {
  const { error, data } = await fetchGetProjectDetail(props.id);
  if (error) {
    return;
  }
  projectInfo.value = data;

  // 计算当前状态
  const index = projectStatusOptions.findIndex(option => option.value === data.status);
  if (index !== -1) {
    currentState.value = index + 1;
  }
};

// 项目状态
const projectState = ref<Array<Api.Crm.ProjectState>>();
const fetchProjectStates = async () => {
  const { error, data } = await fetchGetProjectState({ projectId: props.id });
  if (error) {
    return;
  }
  projectState.value = data;
  percentage.value = Math.round((currentState.value / totalStates) * 100);
};

const getStateName = (state: number): string => {
  const one = getEnumOptionInfo(projectStatusOptions, state);

  return (one?.label as string) || '';
};

const maps = {
  bankName: '银行名称',
  businessType: '业务类型',
  biddingTime: '投标时间',
  contractEndTime: '合同结束时间',
  expectedStartTime: '预计启动时间',
  partnerDuration: '合作年限',
  totalScale: '总规模',
  entryCountries: '入围家数',
  singleEstimatedValue: '预估规模',
  baseMode: '基地规模',
  city: '城市',
  winningProbability: '中标率',
  averageRevenue: '人均营收',
  createTime: '添加时间'
};
onMounted(() => {
  fetchProjectInfo().then(() => {
    fetchProjectStates();
  });
});
</script>

<template>
  <div>
    <NSpace class="mb-10px" vertical>
      <NSteps size="small" :current="currentState">
        <NStep v-for="(item, index) in projectStatusOptions" :key="index" :title="item.label" />
      </NSteps>
    </NSpace>
    <NProgress class="mb-10px" type="line" :percentage="percentage" indicator-placement="inside" processing />
    <NScrollbar :content-style="{ height: '60vh' }" trigger="hover">
      <NTabs>
        <NTabPane name="info" tab="详情">
          <TableInfoList :maps="maps" :data="projectInfo" />
        </NTabPane>
        <NTabPane name="state" tab="进度">
          <NTimeline>
            <NTimelineItem
              v-for="(item, index) in projectState"
              :key="index"
              :content="item?.remarks"
              type="warning"
              :time="item.createTime"
            >
              <template #header>
                {{ getStateName(item.newStatus) }}
                <NTag type="success" size="small">{{ showUserStr(item.user) }}</NTag>
              </template>
              <template #default>
                <div v-if="item.scene === 5">
                  <div>{{ item.remarks }}</div>
                  <div>接手人：{{ showUserStr(item.handoverUser) }}</div>
                </div>
              </template>
            </NTimelineItem>
          </NTimeline>
        </NTabPane>
      </NTabs>
    </NScrollbar>
  </div>
</template>
