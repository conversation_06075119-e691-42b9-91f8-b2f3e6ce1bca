<script setup lang="ts">
import { onMounted, ref } from 'vue';
import TableInfoList from '@/components/common/table-info-list.vue';
import { fetchGetProjectDetail } from '@/service/api';

interface Props {
  id: number;
}
const props = defineProps<Props>();
const projectInfo = ref<Partial<Api.Crm.Project>>({
  averageRevenue: '',
  entryCountries: '',
  status: 0,
  bankName: '',
  businessType: '',
  biddingTime: '',
  contractEndTime: '',
  expectedStartTime: '',
  partnerDuration: '',
  totalScale: '',
  singleEstimatedValue: '',
  baseMode: '',
  city: '',
  winningProbability: ''
});

const fetchProjectInfo = async () => {
  const { error, data } = await fetchGetProjectDetail(props.id);
  if (error) {
    return;
  }
  projectInfo.value = data;
};

const maps = {
  bankName: '银行名称',
  businessType: '业务类型',
  biddingTime: '投标时间',
  contractEndTime: '合同结束时间',
  expectedStartTime: '预计启动时间',
  partnerDuration: '合作年限',
  totalScale: '总规模',
  entryCountries: '入围家数',
  singleEstimatedValue: '预估规模',
  baseMode: '基地规模',
  city: '城市',
  winningProbability: '中标率',
  averageRevenue: '人均营收',
  createTime: '添加时间'
};
onMounted(() => {
  fetchProjectInfo();
});
</script>

<template>
  <TableInfoList :maps="maps" :data="projectInfo" />
</template>
