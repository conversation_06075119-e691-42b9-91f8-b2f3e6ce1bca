<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { fetchPuProjectTransfer } from '@/service/api';
import { $t } from '@/locales';
import { getUserByRoleOptions, getUserOptions } from '@/utils/async-functions';
import SelectWithSearch from '@/components/common/select-with-search.vue';
import { useAuthStore } from '@/store/modules/auth';

interface IProps {
  rowData: Api.Crm.Project;
  isReceive: boolean;
}
const props = defineProps<IProps>();
const userStore = useAuthStore();
const { formRef, validate } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();
type RuleKey = keyof Pick<Api.Crm.TransferRequest, 'newOwnerUserId'>;
const rules = ref<Record<RuleKey, App.Global.FormRule>>({
  newOwnerUserId: defaultRequiredRule
});
const model = ref<Api.Crm.TransferModel>(createModel());
function createModel(): Api.Crm.TransferModel {
  return {
    id: null,
    ownerUserId: null,
    newOwnerUserId: null,
    userName: null
  };
}
function initModel() {
  model.value.id = props.rowData.id;
  model.value.ownerUserId = props.rowData.user?.id;
  model.value.userName = props.rowData.user?.nickname;
}
async function handleSubmit() {
  await validate();
  const { error } = await fetchPuProjectTransfer({
    ...model.value,
    newOwnerUserId: props.isReceive ? userStore.userInfo.id : model.value.newOwnerUserId,
    isReceive: props.isReceive
  });
  if (error) {
    return false;
  }
  window.$message?.success($t(props.isReceive ? '认领成功' : 'common.modifySuccess'));
  return true;
}
defineExpose({
  handleSubmit
});
onMounted(() => {
  initModel();
});
</script>

<template>
  <NAlert type="warning">
    {{
      isReceive
        ? `认领项目后，您才能进行项目跟进，包括添加联络人、制定拜访计划以及记录客户拜访。若不认领，则无法编辑或跟进该项目。是否确认认领${rowData.bankName ?? ''}项目？`
        : '项目交接后，新负责人将能够进行项目跟进，包括添加联络人、制定拜访计划和记录客户拜访。交接完成后，原负责人将不再具备这些权限。'
    }}
  </NAlert>
  <NForm v-if="!isReceive" ref="formRef" class="mt-5px" :model="model" :rules="rules">
    <NFormItem label="老负责人" path="ownerUserId">
      <NTag v-if="model.userName" type="primary">{{ model.userName }}</NTag>
    </NFormItem>
    <NFormItem label="新负责人" path="newOwnerUserId">
      <SelectWithSearch
        v-model:value="model.newOwnerUserId"
        :api-func="getUserByRoleOptions"
        :selected-options="[]"
        class="w-full"
        placeholder="新负责人"
      />
    </NFormItem>
  </NForm>
</template>

<style scoped></style>
