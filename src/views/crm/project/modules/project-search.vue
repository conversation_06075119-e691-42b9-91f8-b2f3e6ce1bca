<script setup lang="ts">
import { projectStatusOptions } from '@/enum';
import { useNaiveForm } from '@/hooks/common/form';
import { getUserByRoleOptions, getUserOptions } from '@/utils/async-functions';
import SelectWithSearch from '@/components/common/select-with-search.vue';

defineOptions({
  name: 'ProjectSearch'
});

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useNaiveForm();

const model = defineModel<Api.Crm.ProjectSearchParams>('model', { required: true });

async function reset() {
  await restoreValidation();
  emit('reset');
  emit('search');
}

async function search() {
  await validate();
  emit('search');
}
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper">
    <NCollapse>
      <NCollapseItem title="搜索" name="search">
        <NForm ref="formRef" :model="model" class="mb--12px" label-placement="left" :label-width="80">
          <NGrid responsive="screen" item-responsive>
            <NFormItemGi span="24 s:12 m:6" label="银行名称" path="bankName" class="pr-24px">
              <NInput v-model:value="model.bankName" clearable placeholder="请输入银行名称" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="商务" path="ownerUserId" class="pr-24px">
              <SelectWithSearch
                v-model:value="model.ownerUserId"
                :api-func="getUserByRoleOptions"
                :selected-options="[]"
                placeholder="商务"
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="状态" path="status" class="pr-24px">
              <NSelect
                v-model:value="model.status"
                placeholder="请选择项目状态"
                :options="projectStatusOptions"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 m:12" class="pr-24px">
              <NSpace class="w-full" justify="end">
                <NButton @click="reset">
                  <template #icon>
                    <icon-ic-round-refresh class="text-icon" />
                  </template>
                  {{ $t('common.reset') }}
                </NButton>
                <NButton type="primary" ghost @click="search">
                  <template #icon>
                    <icon-ic-round-search class="text-icon" />
                  </template>
                  {{ $t('common.search') }}
                </NButton>
              </NSpace>
            </NFormItemGi>
          </NGrid>
        </NForm>
      </NCollapseItem>
    </NCollapse>
  </NCard>
</template>

<style scoped>
:deep(.n-collapse-item__content-inner) {
  padding-top: 12px !important;
}
</style>
