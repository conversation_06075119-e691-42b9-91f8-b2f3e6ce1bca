<script lang="tsx" setup>
import { NButton, NTag } from 'naive-ui';
import { onMounted, reactive, ref, toRaw } from 'vue';
import dayjs from 'dayjs';
import { $t } from '@/locales';
import { fetchGetFollowList } from '@/service/api';
import { getEnumTagType, getEnumValue, showUserStr } from '@/utils/useful_func';
import { renderModal } from '@/components/re-modal';
import { useAuthStore } from '@/store/modules/auth';
import { jsonClone } from '~/packages/utils/src/klona';
import { reviewStatusOptions } from '@/enum';
import FormModal from './modules/form.vue';
import ReviewModal from './modules/follow-review.vue';
import Search from './modules/search.vue';

const userStore = useAuthStore();

const searchParams = reactive<Api.Crm.FollowSearchParams>({
  pageNo: 1,
  pageSize: 10,
  projectId: null,
  customerIds: null,
  ownerUserId: null,
  isValid: null
});
const initParams = { ...jsonClone(searchParams) };
const lists = ref<Api.Crm.Follow[]>([]);
const total = ref(0);
const pageCount = ref(0);
const loading = ref(false);
async function getData() {
  loading.value = true;
  const { error, data } = await fetchGetFollowList(toRaw(searchParams));
  if (error) {
    loading.value = false;
    return;
  }
  lists.value = data.list;
  total.value = data.total;
  pageCount.value = Math.ceil(data.total / searchParams.pageSize);
  setTimeout(() => {
    loading.value = false;
  }, 200);
}

// 切换页数
function handlePageSizeChange(num: number) {
  searchParams.pageNo = 1;
  searchParams.pageSize = num;
  getData();
}

// 重置搜索条件
function resetSearchParams() {
  Object.assign(searchParams, initParams);
  getData();
}

function getDataSearch() {
  searchParams.pageNo = 1;
  getData();
}

onMounted(() => {
  getData();
});

// 显示表单
function showForm(row: Api.Crm.Follow | undefined) {
  renderModal(
    FormModal,
    { operateType: row ? 'edit' : 'add', rowData: row },
    {
      title: row ? '编辑拜访记录' : '添加拜访记录',
      style: {
        width: '66%'
      },
      func: getData
    }
  );
}

// 评审
function review(id: number) {
  renderModal(
    ReviewModal,
    { followId: id, onSubmitted: getData },
    {
      title: '评审',
      style: {
        width: '66%'
      }
    }
  );
}
</script>

<template>
  <div>
    <Search v-model:model="searchParams" @reset="resetSearchParams" @search="getDataSearch" />
    <NCard :bordered="false" class="mt-10px sm:flex-1-hidden card-wrapper p-2" size="small" title="拜访记录">
      <template #header-extra>
        <NTooltip trigger="hover">
          <template #trigger>
            <NButton v-hasPermi="['crm:visits:create']" type="default" ghost size="small" @click="showForm(undefined)">
              新增拜访记录
            </NButton>
          </template>
          <span>建议在拜访计划页面的具体计划中添加拜访总结，以便系统化管理拜访记录并支持后续数据分析。</span>
        </NTooltip>
      </template>
      <div v-if="total" class="mt-5px">
        <NSpin :show="loading">
          <NGrid x-gap="10" y-gap="8" cols="2 s:3 m:4 l:5 xl:6 2xl:7" responsive="screen">
            <NGi v-for="item in lists" :key="item.id">
              <NCard class="box">
                <template #header>
                  <div class="flex flex-col">
                    <div>
                      <CustomerButton v-for="one of item.customers" :key="one.id" :customer="one" class="mr-5px" />
                    </div>
                    <div class="mt-5px text-12px">
                      <NTag type="warning" ghost size="small">{{ showUserStr(item.user) }}</NTag>
                      <span class="pl-3px">{{ dayjs(item.createTime).format('YYYY-MM-DD Ah:mm') }}</span>
                    </div>
                  </div>
                </template>

                <NScrollbar content-style="height: 120px">
                  {{ item.summary }}
                </NScrollbar>

                <template #footer>
                  <div>
                    <ProjectButton :project="item.project" ghost />
                  </div>
                </template>
                <template #action>
                  <div class="w-full text-left">
                    <NButton
                      v-hasPermi="['crm:visits:update']"
                      type="primary"
                      ghost
                      size="small"
                      @click="showForm(item)"
                    >
                      {{ $t('common.edit') }}
                    </NButton>
                    <NButton
                      v-hasPermi="['crm:visits:review']"
                      :type="getEnumTagType(reviewStatusOptions, item.isValid)"
                      ghost
                      size="small"
                      class="ml-15px"
                      @click="review(item.id)"
                    >
                      {{ getEnumValue(reviewStatusOptions, item.isValid) }}
                    </NButton>
                  </div>
                </template>
              </NCard>
            </NGi>
          </NGrid>
          <NPagination
            v-model:page="searchParams.pageNo"
            :page-count="pageCount"
            :page-sizes="[10, 20, 30, 40, 50]"
            class="mt-10px flex justify-end"
            show-size-picker
            @update:page="getData"
            @update:page-size="handlePageSizeChange"
          >
            <template #prefix>共 {{ total }} 条</template>
          </NPagination>
        </NSpin>
      </div>
      <NResult v-else status="info" description="未查询到相关数据"></NResult>
    </NCard>
  </div>
</template>

<style lang="scss" scoped>
.box {
  height: 275px;
  border: 1px solid rgb(var(--primary-color));
  border-radius: 10px;
  padding: 5px;
}
:deep(.n-card-header) {
  padding: 0 !important;
}
:deep(.n-card__action) {
  padding: 5px 3px !important;
  background: none;
}
:deep(.n-card__content) {
  border-top: none !important ;
  padding: 0 5px 5px 5px;
}
:deep(.n-card__footer) {
  padding: 5px 3px !important;
}
</style>
