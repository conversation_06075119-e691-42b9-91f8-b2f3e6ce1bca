<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue';
import { showUserStr } from '@/utils/useful_func';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { fetchGetFollowReview, fetchUpdateFollowReview } from '@/service/api';
import { ReviewStatus, reviewStatusOptions } from '@/enum';

interface Props {
  followId: number;
}
interface Emits {
  (e: 'emitGet'): void;
}

const emit = defineEmits<Emits>();
const props = defineProps<Props>();

const { formRef, validate } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();
const model: Api.Crm.FollowReviewParams = reactive({
  id: props.followId,
  visitId: props.followId,
  user: undefined,
  userId: undefined,
  isValid: 0,
  comment: '',
  createTime: ''
});

type RuleKey = Extract<keyof Api.Crm.FollowReviewParams, 'isValid'>;
const rules = ref<Record<RuleKey, App.Global.FormRule>>({
  isValid: defaultRequiredRule
});

const reviewStatus = ref<ReviewStatus>(ReviewStatus.Reviewing);

async function fetchReviewInfo() {
  const { error, data } = await fetchGetFollowReview(props.followId);
  if (error) {
    return;
  }
  if (data) {
    Object.assign(model, data);
    reviewStatus.value = data.isValid;
  }
}

// 提交
async function handleSubmit() {
  await validate();
  if (model.isValid === ReviewStatus.Reviewing) {
    window.$message?.error('请选择评审结果');
    return;
  }

  const { error } = await fetchUpdateFollowReview(model);
  if (error) {
    return;
  }
  window.$message?.success('提交成功');
  await fetchReviewInfo();
  emit('emitGet');
}

onMounted(() => {
  fetchReviewInfo();
});
</script>

<template>
  <NForm ref="formRef" :model="model" :rules="rules" class="w-full" label-placement="left">
    <div v-if="reviewStatus">
      <NFormItem label="评审人">
        <NTag type="warning" size="small">{{ showUserStr(model?.user) }}</NTag>
      </NFormItem>
      <NFormItem label="评审时间">
        {{ model.createTime }}
      </NFormItem>
    </div>
    <NFormItem label="结果" path="isValid">
      <NRadioGroup v-model:value="model.isValid">
        <NRadioButton
          v-for="(item, index) in reviewStatusOptions"
          :key="index"
          :disabled="item.value === ReviewStatus.Reviewing"
          :value="item.value"
        >
          {{ item.label }}
        </NRadioButton>
      </NRadioGroup>
    </NFormItem>
    <NFormItem label="说明" path="comment">
      <NInput v-model:value="model.comment" clearable placeholder="请输入说明" rows="4" type="textarea" />
    </NFormItem>
    <NSpace :size="16" justify="end">
      <NFormItem>
        <ReButton type="primary" :disabled="reviewStatus === ReviewStatus.Passed" @click="handleSubmit">提交</ReButton>
      </NFormItem>
    </NSpace>
  </NForm>
</template>

<style scoped></style>
