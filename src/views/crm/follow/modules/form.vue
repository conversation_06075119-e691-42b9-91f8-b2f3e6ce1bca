<script setup lang="ts">
import { nextTick, onMounted, reactive, ref, useTemplateRef, watch } from 'vue';
import { getCustomerOptions, getPlanOptions, getProjectOptions } from '@/utils/async-functions';
import SelectWithSearch from '@/components/common/select-with-search.vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { fetchAddFollow, fetchGetVisits, fetchUpdateFollow } from '@/service/api';
import DateTimePicker from '@/components/common/date-time-picker.vue';
import type { SelectWithSearchType } from '@/typings/common-type';

defineOptions({
  name: 'FollowFormModal'
});

type OperateType = 'add' | 'edit' | 'plan';

interface Props {
  operateType: OperateType;
  rowData?: Api.Crm.Follow | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'btnSubmit'): void;
  (e: 'btnClose'): void;
}

const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule, createTextLengthRule } = useFormRules();

const model: Api.Crm.FollowAddParams = reactive(createDefaultModel());

function createDefaultModel(): Api.Crm.FollowAddParams {
  return {
    projectId: null,
    customerIds: null,
    id: null,
    planId: null,
    // 拜访总结
    interactionLevel: '',
    summary: '',
    visitDate: '',
    // 下次拜访计划
    planComment: '',
    planDate: null
  };
}

// 意向等级快速选择
const interactionLevelOptions = [
  { label: '高', value: '较高' },
  { label: '中', value: '一般' },
  { label: '低', value: '较低' }
];
function quickSelectInteraction(value: string) {
  model.interactionLevel = `客户意向${value}`;
}

type RuleKey = Extract<keyof Api.Crm.FollowFormParams, 'customerName'>;

const rules = ref<Record<RuleKey, App.Global.FormRule>>({
  projectId: defaultRequiredRule,
  customerIds: defaultRequiredRule,
  summary: [defaultRequiredRule, createTextLengthRule('总结')],
  visitDate: defaultRequiredRule,
  interactionLevel: defaultRequiredRule
});

// 是否是创建
const isCreate = ref(false);
async function handleInitModel() {
  Object.assign(model, createDefaultModel());

  if (props.rowData) {
    Object.assign(model, props.rowData);
  }
  if (props.operateType === 'plan') {
    const { data, error } = await fetchGetVisits(props.rowData!.planId);
    if (error) {
      return;
    }
    Object.assign(model, data);
  }
  isCreate.value = ['add', 'plan'].includes(props.operateType) && !model.id;
}

async function handleSubmit(): Promise<boolean> {
  await validate();

  const formData: Api.Crm.FollowAddParams = {
    id: props.rowData?.id || model.id,
    projectId: model.projectId,
    planId: model.planId,
    customerIds: model.customerIds || [],
    summary: model.summary,
    visitDate: model.visitDate,
    interactionLevel: model.interactionLevel,
    planDate: model?.planDate,
    planComment: model?.planComment
  };

  function isEmpty() {
    if (!model.planComment && !model.planDate) {
      return true;
    }
    if (model.planComment && model.planDate) {
      return true;
    }

    return false;
  }

  if (isCreate.value) {
    // 下次拜访计划（可以不填，填了必须都填）
    if (!isEmpty()) {
      window.$message?.error('下次拜访计划信息不完整');
      return false;
    }

    const { error } = await fetchAddFollow(formData);
    if (error) {
      return false;
    }
    window.$message?.success($t('common.addSuccess'));
  } else if (props.operateType === 'edit' || model.id) {
    const { error } = await fetchUpdateFollow(formData);
    if (error) {
      return false;
    }
    window.$message?.success($t('common.updateSuccess'));
  }
  emit('btnSubmit');
  return true;
}

const planTref = useTemplateRef<SelectWithSearchType>('planSelectRef');
const customerTref = useTemplateRef<SelectWithSearchType>('customerSelectRef');
// 监听项目变更
watch(
  () => model.projectId,
  () => {
    planTref.value?.reset();
    customerTref.value?.reset();
  }
);

// 监听项目变更
watch(
  () => model.planId,
  () => {
    customerTref.value?.reset();
  }
);

// 选择计划时，自动选中客户
function selectedPlan(option: CommonType.Option<number> | null | CommonType.Option<number>[]) {
  const selectOption = option as unknown as Api.Crm.Plan;
  nextTick(() => {
    model.customerIds = selectOption.customers?.map(item => item.id);
  });
}

onMounted(() => {
  handleInitModel();
  restoreValidation();
});
</script>

<template>
  <NForm ref="formRef" :model="model" :rules="rules" class="flex justify-around">
    <NRow :gutter="30">
      <NCol :span="isCreate ? 8 : 12">
        <NAlert class="mb-10px" title="基本信息" type="info">请选择拜访客户的基本信息</NAlert>
        <NFormItem label="项目" path="projectId">
          <SelectWithSearch
            v-model:value="model.projectId"
            :api-func="getProjectOptions"
            :selected-options="[model.project]"
            label-field="bankName"
            placeholder="归属项目"
          />
        </NFormItem>
        <NFormItem label="拜访计划" path="planId">
          <SelectWithSearch
            ref="planSelectRef"
            v-model:value="model.planId"
            :selected-options="[model.plan]"
            :api-func="
              async (param: string, page: number) => await getPlanOptions(param, Number(model.projectId), page)
            "
            label-field="planComment"
            placeholder="拜访计划"
            @value-changed="selectedPlan"
          />
        </NFormItem>
        <NFormItem label="联络人" path="customerIds">
          <SelectWithSearch
            ref="customerSelectRef"
            v-model:value="model.customerIds"
            :selected-options="model.customers || []"
            :multiple="true"
            :page-size="0"
            :api-func="
              async (param: string, page: number) => await getCustomerOptions(param, Number(model.projectId), page)
            "
            label-field="name"
            placeholder="联络人"
          />
        </NFormItem>
      </NCol>
      <NCol :span="isCreate ? 8 : 12">
        <NAlert class="mb-10px" title="本次拜访总结" type="info">请填写本次拜访总结</NAlert>
        <NFormItem label="拜访总结" path="summary">
          <NInput v-model:value="model.summary" type="textarea" rows="7" placeholder="请输入拜访总结" clearable />
        </NFormItem>
        <NFormItem label="拜访日期" path="visitDate">
          <DateTimePicker v-model:time="model.visitDate" class="w-full" label="实际拜访日期" />
        </NFormItem>
        <NFormItem label="意向等级" path="interactionLevel">
          <div class="flex flex-col items-start !w-full">
            <NRadioGroup class="mb-5px" size="small" @update:value="quickSelectInteraction">
              <NRadioButton v-for="(item, index) in interactionLevelOptions" :key="index" :value="item.value">
                {{ item.label }}
              </NRadioButton>
            </NRadioGroup>
            <NInput
              v-model:value="model.interactionLevel"
              type="textarea"
              rows="4"
              placeholder="请输入意向等级"
              clearable
            />
          </div>
        </NFormItem>
      </NCol>
      <Transition name="fade" mode="out-in">
        <NCol v-if="isCreate" :span="8">
          <NAlert class="mb-10px" title="下次拜访计划" type="info">若需继续拜访，可在此添加拜访计划</NAlert>
          <NFormItem label="计划时间" path="planDate">
            <DateTimePicker v-model:time="model.planDate" label="下次计划时间" />
          </NFormItem>
          <NFormItem label="计划说明" path="planComment">
            <NInput
              v-model:value="model.planComment"
              type="textarea"
              rows="6"
              placeholder="请输入下次计划说明"
              clearable
            />
          </NFormItem>
        </NCol>
      </Transition>
    </NRow>
  </NForm>
  <NSpace :size="16" justify="end">
    <NButton @click="emit('btnClose')">{{ $t('common.cancel') }}</NButton>
    <NButton type="primary" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
  </NSpace>
</template>

<style scoped>
.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: all 0.4s ease;
}
.fade-slide-enter-from {
  opacity: 0;
  transform: translateX(20px);
}
.fade-slide-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}
</style>
