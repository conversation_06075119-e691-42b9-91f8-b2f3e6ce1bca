<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { NButton } from 'naive-ui';
import TableInfoList from '@/components/common/table-info-list.vue';
import { fetchGetFollowDetail } from '@/service/api';
import { useAuthStore } from '@/store/modules/auth';
import ReviewHistory from './review-history.vue';

interface Props {
  followId: number;
}
interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();
const props = defineProps<Props>();
const info = ref<Partial<Api.Crm.FollowDetail>>({
  interactionLevel: '',
  summary: '',
  visitDate: '',
  updateTime: '',
  plan: undefined
});

const fetchInfo = async () => {
  const { error, data } = await fetchGetFollowDetail(props.followId);
  if (error) {
    return;
  }
  info.value = data;
};

const maps = {
  interactionLevel: '意向等级',
  summary: '拜访总结',
  visitDate: '拜访日期',
  planDate: '下一步拜访时间',
  planComment: '下次拜访计划',
  createTime: '添加时间'
};

const userStore = useAuthStore();
const emitGet = () => {
  emit('submitted');
};
onMounted(() => {
  fetchInfo();
});
</script>

<template>
  <NFlex>
    <div class="w-60%">
      <NScrollbar :content-style="{ maxHeight: '60vh' }">
        <TableInfoList :show-customer="true" :show-project="true" :show-user="true" :maps="maps" :data="info" />
      </NScrollbar>
    </div>
    <div v-hasPermi="['crm:visits:review']" class="ml-10px w-36%">
      <ReviewHistory :follow-id="followId" @emit-get="emitGet" />
    </div>
  </NFlex>
</template>
