<script lang="tsx" setup>
import dayjs from 'dayjs';
import { NButton, NDropdown, NTag } from 'naive-ui';
import type { ProSearchFormColumns } from 'pro-naive-ui';
import { createProSearchForm } from 'pro-naive-ui';
import { ref, watch } from 'vue';
import { useRouter } from 'vue-router';

import PositionPro from '@/components/common/position-pro.vue';
import { useDict } from '@/hooks/business/useDict';
import { useTable } from '@/hooks/common/table';
import { $t } from '@/locales';
import { fetchGetPromotionList } from '@/service/api/hrm/roster_promoted';
import { useAppStore } from '@/store/modules/app';
import { getLabelByValue } from '@/utils/useful_func';

// 状态选项配置
const subStatusOptions = [
  { label: '审批中', value: 'processing', realValue: [1] },
  { label: '审批结束', value: 'done', realValue: [2, 3, 4] }
];

// 默认选中“审批中”
const selectedStatus = ref('processing');
const { hrm_bpm_status, hrm_position_property } = useDict(['hrm_bpm_status', 'hrm_position_property']);
const searchColumns: ProSearchFormColumns<Api.Hrm.PromotionReqVOData> = [
  {
    title: '员工姓名',
    path: 'name'
  },
  {
    title: '岗位属性',
    path: 'positionProperty',
    field: 'select',
    fieldProps: {
      options: hrm_position_property.value
    }
  },
  {
    labelWidth: 120,
    title: '晋升前岗位名',
    path: 'fromPositionName'
  },
  {
    title: '晋升后岗位名',
    labelWidth: 120,
    path: 'toPositionName'
  },
  {
    title: '晋升日期',
    path: 'effectiveDate',
    field: 'date-range'
  }
];

const appStore = useAppStore();
const router = useRouter();
const {
  columns,
  columnChecks,
  data,
  getData,
  loading,
  mobilePagination,
  searchParams,
  getDataByPage,
  updateSearchParams
} = useTable({
  apiFn: fetchGetPromotionList,
  showTotal: true,
  apiParams: {
    pageNo: 1,
    pageSize: 10,
    effectiveDate: null,
    name: null,
    fromPositionName: null,
    toPositionName: null,
    type: 2,
    statuses: subStatusOptions.find(o => o.value === selectedStatus.value)?.realValue || [],
    positionProperty: null
  },
  columns: () => [
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'name',
      title: '员工姓名',
      align: 'center',
      minWidth: 100
    },
    {
      key: 'positionProperty',
      title: '岗位属性',
      align: 'center',
      render: (row: Api.Hrm.PositionChangeRespVO) => <PositionPro positionProperty={row.positionProperty} />
    },
    {
      key: 'fromDepts',
      title: '晋升前部门',
      align: 'center',
      render: (row: Api.Hrm.PositionChangeRespVO) => {
        return row.fromDepts?.map(dept => dept.deptName).join(',');
      },
      minWidth: 100
    },

    {
      key: 'toDepts',
      title: '晋升后部门',
      align: 'center',
      render: (row: Api.Hrm.PositionChangeRespVO) => {
        return row.toDepts?.map(dept => dept.deptName).join(',');
      },
      minWidth: 100
    },
    {
      key: 'fromPosition',
      title: '晋升前职位名称',
      align: 'center',
      minWidth: 100
    },
    {
      key: 'toPosition',
      title: '晋升后职位名称',
      align: 'center',
      minWidth: 100
    },

    {
      key: 'status',
      title: '状态',
      align: 'center',
      render: (row: Api.Hrm.PositionChangeRespVO) => {
        return row.status ? <NTag type="info">{getLabelByValue(hrm_bpm_status.value, row.status)}</NTag> : null;
      },
      minWidth: 100
    },
    {
      key: 'description',
      title: '晋升描述',
      align: 'center',
      minWidth: 100
    },
    {
      key: 'createTime',
      title: '钉钉发起日期',
      align: 'center',
      minWidth: 100
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 180,
      render: (rowData: Api.Hrm.PositionChangeRespVO) => (
        <div class="flex-center gap-8px">
          <NButton
            type="primary"
            ghost
            size="small"
            onClick={() =>
              rowData.employeeId && router.push({ path: '/hrm/roster/profile', query: { id: rowData.employeeId } })
            }
          >
            详情
          </NButton>
          <NDropdown
            trigger="hover"
            placement="right-start"
            options={[{ label: '钉钉审批详情', key: 'apply' }]}
            onSelect={(key: string) => {
              if (key === 'apply') {
                window.open(rowData.externalUrl);
              }
            }}
          >
            <NButton size="small" type="primary" ghost>
              <icon-ic-outline-more-horiz />
            </NButton>
          </NDropdown>
        </div>
      )
    }
  ]
});

const form = createProSearchForm({
  defaultCollapsed: true,
  initialValues: searchParams,
  onReset: () => {
    updateSearchParams({
      statuses: subStatusOptions.find(o => o.value === selectedStatus.value)?.realValue || [],
      effectiveDate: null,
      fromPositionName: null,
      toPositionName: null,
      type: 2,
      name: null,
      positionProperty: null
    });
    getDataByPage();
  },
  onSubmit: () => {
    const formValues = form.fieldsValue.value;
    const [start, end] = formValues.effectiveDate || [];

    updateSearchParams({
      ...formValues,
      effectiveDate: formValues.effectiveDate
        ? [
            dayjs(start).startOf('day').format('YYYY-MM-DD HH:mm:ss'),
            dayjs(end).endOf('day').format('YYYY-MM-DD HH:mm:ss')
          ]
        : null
    });

    getDataByPage();
  }
});

watch(selectedStatus, val => {
  const found = subStatusOptions.find(item => item.value === val);
  if (found) {
    updateSearchParams({ statuses: found.realValue, ...form.fieldsValue.value });
    getDataByPage();
  }
});
</script>

<template>
  <div class="min-h-500px flex-col-stretch overflow-hidden lt-sm:overflow-auto">
    <ProCard class="mb-10px" :show-collapse="false" content-class="!pb-[0px]">
      <ProSearchForm :form="form" :columns="searchColumns" />
    </ProCard>
    <NCard
      :bordered="false"
      class="sm:flex-1-hidden card-wrapper"
      size="small"
      title="晋升"
      content-class="flex flex-col"
    >
      <template #header-extra>
        <TableHeaderOperation v-model:columns="columnChecks" :loading="loading" :show-add="false" @refresh="getData" />
      </template>

      <NTabs v-model:value="selectedStatus" type="card" size="small">
        <NTabPane v-for="song in subStatusOptions" :key="song.value" :name="song.value" :tab="song.label">
          <template #tab>
            <div class="flex items-center gap-4px">
              {{ song.label }}
            </div>
          </template>
        </NTabPane>
      </NTabs>

      <NDataTable
        :columns="columns"
        :data="data"
        :loading="loading"
        :pagination="mobilePagination"
        :row-key="row => row.id"
        :flex-height="!appStore.isMobile"
        remote
        :scroll-x="1300"
        size="small"
        class="sm:h-full"
      />
    </NCard>
  </div>
</template>

<style scoped>
:deep(.n-tab-pane) {
  display: none;
}
:deep(.n-tabs-pad) {
  display: none;
}
</style>
