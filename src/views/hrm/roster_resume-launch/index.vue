<script setup lang="tsx">
import { NTag } from 'naive-ui';
import type { ProDataTableColumns, ProSearchFormColumns } from 'pro-naive-ui';
import { createProSearchForm, useNDataTable } from 'pro-naive-ui';

import AddForm from './modules/add-form.vue';

import { renderModal } from '@/components/re-modal';
import { infoRoleBtn } from '@/directives/permission/permi-btn';
import { CompletionStatusOptions } from '@/enum';
import { $t } from '@/locales';
import { fetchGetTodoList } from '@/service/api/kip/task-center';
import { getAllPositionOptions } from '@/utils/async-functions';
import { getEnumTagType, showUserStr } from '@/utils/useful_func';

const searchForm = createProSearchForm({
  defaultCollapsed: true
});
const {
  table: { tableProps, onChange },
  search: { proSearchFormProps }
} = useNDataTable(
  async ({ current, pageSize }, values) => {
    return fetchGetTodoList({
      ...values,
      pageSize,
      pageNo: current
    }).then(({ data }) => {
      return {
        list: data?.list || [],
        total: data?.total || 0
      };
    });
  },
  {
    form: searchForm
  }
);

/* 搜索 */
const searchColumns: ProSearchFormColumns<Api.Hrm.SimpleEmployeeInfoSearch> = [
  {
    title: '员工姓名',
    path: 'name'
  },
  {
    title: '员工工号',
    path: 'jobNumber'
  },
  {
    title: '员工职位',
    path: 'positionId',
    field: 'select-with-search',
    fieldProps: {
      apiFunc: getAllPositionOptions,
      selectedOptions: [],
      pageSize: 0,
      placeholder: '职位'
    }
  },
  {
    title: '所属部门',
    path: 'deptId',
    field: 'dept-tree-select',
    fieldProps: {
      placeholder: '请选择部门'
    }
  }
];

/* 表格 */
const columns: ProDataTableColumns<Api.Kip.QueryTodoListRespVO> = [
  {
    type: 'index',
    fixed: 'left'
  },
  {
    width: 120,
    title: '批次号',
    path: 'a',
    align: 'center'
  },
  {
    width: 120,
    title: '公司主体',
    path: 'v',
    align: 'center',
    render: (row: Api.Kip.QueryTodoListRespVO) => {
      return row.assignee ? <NTag type="info">{showUserStr(row?.assignedBy)}</NTag> : '-';
    }
  },
  {
    width: 120,
    title: '业务分类',
    path: 'c',
    align: 'center',
    render: (row: Api.Kip.QueryTodoListRespVO) => {
      return row.assignee ? <NTag type="info">{showUserStr(row?.assignee)}</NTag> : '-';
    }
  },
  {
    width: 120,
    title: '员工',
    path: 'v',
    align: 'center'
  },
  {
    width: 120,
    title: '签署总数',
    path: 'd',
    align: 'center',
    render: (row: Api.Kip.QueryTodoListRespVO) => {
      return <NTag type={getEnumTagType(CompletionStatusOptions, row.processStatus.id)}>{row.processStatus.name}</NTag>;
    }
  },
  {
    width: 120,
    title: '发起成功数量',
    path: 'e',
    align: 'center'
  },
  {
    width: 120,
    title: '签署成功数量',
    path: 'f',
    align: 'center'
  },
  {
    width: 120,
    title: '签署截止日期',
    path: 'g',
    align: 'center'
  },
  {
    width: 120,
    title: '业务说明',
    path: 'h',
    align: 'center'
  },
  {
    width: 120,
    fixed: 'right',
    title: $t('common.operate'),
    align: 'center',
    render: (row: Api.Kip.QueryTodoListRespVO) => {
      return (
        <div class="flex flex-wrap gap-2">
          {infoRoleBtn(['core-minder:todo:query'], 'success', () => handleOpenDetail(row))}
        </div>
      );
    }
  }
];

// 新增弹窗
function handleOpenModal() {
  renderModal(
    AddForm,
    {},
    {
      title: '批量发起合同',
      style: {
        width: '70%'
      },
      contentClass: 'max-h-[80vh] overflow-y-auto',
      func: onChange
    }
  );
}

// 详情弹窗
function handleOpenDetail(row: Api.Kip.QueryTodoListRespVO) {
  console.log('详情弹窗', row);
}
</script>

<template>
  <div class="h-full flex flex-col">
    <ProCard class="mb-24px" :show-collapse="false" content-class="!pb-0">
      <ProSearchForm :form="searchForm" :columns="searchColumns" v-bind="proSearchFormProps" />
    </ProCard>

    <ProDataTable
      title="发起合同"
      size="small"
      flex-height
      :columns="columns"
      :scroll-x="1320"
      row-key="id"
      v-bind="tableProps"
    >
      <template #toolbar>
        <NButton v-hasPermi="['']" size="small" ghost type="primary" @click="handleOpenModal()">
          <template #icon>
            <icon-ic-round-plus class="text-icon" />
          </template>
          {{ $t('common.add') }}
        </NButton>
      </template>
    </ProDataTable>
  </div>
</template>
