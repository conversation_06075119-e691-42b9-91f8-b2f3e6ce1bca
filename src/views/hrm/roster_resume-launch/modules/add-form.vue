<script setup lang="ts">
import { useRosterStore } from '@/store/modules/roster';
import ContractFormPure from '@/views/hrm/roster_review/modules/contract-form-pure.vue';
import { useContractActions } from '@/views/hrm/roster_review/modules/useContractActions';

const useRoster = useRosterStore();
const contractInfoFormRef = ref<InstanceType<typeof ContractFormPure>>();
const hasContractData = ref(false);
const { previewContract, getValidatedContractData } = useContractActions();

// 处理合同选择完成事件
function handleContractSelected(data: any) {
  console.log('收到合同选择事件:', data);
  hasContractData.value = data.formModel?.modelList?.length > 0;
  console.log('hasContractData:', hasContractData.value);
}

// 预览合同
async function handlePreview() {
  try {
    await previewContract(contractInfoFormRef.value);
  } catch (error) {
    // 错误已在 useContractActions 中处理
  }
}

// 提交合同
async function handleSubmit() {
  try {
    const contractData = await getValidatedContractData(contractInfoFormRef.value);

    console.log('合同数据:', contractData);

    // 这里可以调用你的提交 API
    // await submitContract({
    //   ...contractData.templateModel,
    //   employeeId: contractData.employeeId,
    //   companyName: contractData.companyName,
    //   templateParams: contractData.contractData
    // });

    // 你也可以调用这个页面的其他方法
    await handleCustomSubmitLogic(contractData);

    window.$message?.success('提交成功！');
  } catch (error) {
    console.error('提交失败:', error);
    window.$message?.error('提交失败，请检查表单数据');
  }
}

// 这里可以添加你自己的提交逻辑
async function handleCustomSubmitLogic(contractData: any) {
  // 这里可以调用引用页面的方法
  // 例如：调用父组件方法、路由跳转、额外的数据处理等
  console.log('执行自定义提交逻辑:', contractData);

  // 示例：可以在这里调用其他API、更新状态、发送事件等
  // await someCustomAPI(contractData);
  // emit('submit-success', contractData);
  // router.push('/success');
}

onMounted(async () => {
  useRoster.userId = '2098';
  await useRoster.getDictData();
  await useRoster.getAllFun();
});
</script>

<template>
  <NDialogProvider>
    <ContractFormPure ref="contractInfoFormRef" :is-on-job="false" @contract-selected="handleContractSelected" />
    <!-- 外部按钮 - 只在有合同数据时显示 -->
    <div v-if="hasContractData" class="mt-4 flex justify-end gap-3">
      <NButton type="default" size="large" @click="handlePreview">预览合同</NButton>
      <NButton type="primary" size="large" @click="handleSubmit">提交合同</NButton>
    </div>
  </NDialogProvider>
</template>

<style scoped lang="scss"></style>
