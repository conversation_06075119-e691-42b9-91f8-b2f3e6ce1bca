<script setup lang="ts">
defineProps<{
  qiyuesuoEmployeeOptions: { label: string; value: number; data: Api.Hrm.QiYueSuoEmployeeRespVO }[];
}>();
</script>

<template>
  <NAlert type="info" class="mb-20px">
    <div>完成平台账号绑定后，您将代表企业发起电子签署流程。员工签署完成后，您可以通过平台后台实时查看签署状态。</div>
    <div>若在后台修改了相关信息，需重新进行账号绑定。</div>
  </NAlert>
  <ProSelect
    title="电子签平台账号"
    path="qiyuesuoId"
    required
    :field-props="{
      options: qiyuesuoEmployeeOptions
    }"
  />
  <ProRadioGroup
    path="global"
    :field-props="{
      type: 'button',
      options: [
        { label: '是', value: true },
        { label: '否', value: false }
      ]
    }"
  >
    <template #label>
      <ToolTipHover label="是否全局抄送人" code="电子签账号" scene="账号绑定" />
    </template>
  </ProRadioGroup>
</template>
