<script setup lang="tsx">
import { NSwitch, NTag } from 'naive-ui';
import type { ProDataTableColumns } from 'pro-naive-ui';
import { createProModalForm, createProSearchForm, useNDataTable, useRequest } from 'pro-naive-ui';

import ReassociateForm from './modules/reassociat-form.vue';

import { roleBtn } from '@/directives/permission/permi-btn';
import { AccountIdentifierTypeOptions } from '@/enum';
import { useAuth } from '@/hooks/business/auth';
import { $t } from '@/locales';
import {
  fetchGetEmployeeMapping,
  fetchPostEmployeeMapping,
  fetchPutEmployeeMapping,
  fetchPutEmployeeMappingOptionsQiyuesuo
} from '@/service/api/hrm/signature-account';
import { getEnumValue } from '@/utils/useful_func';

const { runAsync: handleGetQiyuesuoEmployee } = useRequest(fetchPutEmployeeMappingOptionsQiyuesuo, {
  manual: true
});
const qiyuesuoEmployeeOptions = ref<{ label: string; value: number; data: Api.Hrm.QiYueSuoEmployeeRespVO }[]>([]);
const { hasAuth } = useAuth();
const searchForm = createProSearchForm();
const {
  table: { tableProps, onChange }
} = useNDataTable(
  () => {
    return fetchGetEmployeeMapping().then(({ data }) => {
      return {
        list: data || []
      };
    });
  },
  {
    form: searchForm
  }
);

/* 绑定 */
const { loading: fetchPostTodoLoading, runAsync: runAsyncFetchPostTodoList } = useRequest(fetchPostEmployeeMapping, {
  manual: true
});

/* 绑定 */
const modalForm = createProModalForm<{
  qiyuesuoId: number | null;
  global: boolean;
  employeeId: number;
  localName: string;
}>({
  onSubmit: async values => {
    try {
      const param = qiyuesuoEmployeeOptions.value.find(item => item.value === values.qiyuesuoId);
      if (!param) {
        throw new Error('电子签员工不存在');
      }
      const { error } = await runAsyncFetchPostTodoList({
        ...modalForm.values.value,
        qiyuesuoName: param.data.name,
        contact: param.data.contact,
        contactType: param.data.contactType
      });
      if (!error) {
        onChange();
        window.$message?.success('绑定成功');
        modalForm.close();
      } else {
        window.$message?.error('绑定失败');
      }
    } catch (e) {
      console.log(e);
    }
  }
});

/* 表格 */
const columns: ProDataTableColumns<Api.Hrm.EmployeeMappingRespVO> = [
  {
    type: 'index'
  },
  {
    title: '人事信息',
    path: 'name',
    render: (row: Api.Hrm.EmployeeMappingRespVO) => {
      return (
        <div class="text-left space-y-1">
          <div>
            <span class="text-gray-500 font-medium">姓名：</span>
            {row.localEmployee.name}
          </div>
          <div>
            <span class="text-gray-500 font-medium">联系方式：</span>
            {row.localEmployee.phoneNumber}
          </div>
        </div>
      );
    }
  },
  {
    title: '电子签平台信息',
    path: 'qiYueSuoId',
    render: (row: Api.Hrm.EmployeeMappingRespVO) => {
      return isBinding(row) ? (
        <div class="text-left space-y-1">
          <div>
            <span class="text-gray-500 font-medium">姓名：</span>
            {row.qiyuesuoEmployee?.name}
          </div>
          <div>
            <span class="text-gray-500 font-medium">联系方式：</span>
            {row.qiyuesuoEmployee?.contact}
          </div>
          <div>
            <span class="text-gray-500 font-medium">类型：</span>
            {getEnumValue(AccountIdentifierTypeOptions, row.qiyuesuoEmployee?.contactType || '')}
          </div>
        </div>
      ) : (
        <NTag type="warning" size="small">
          未绑定
        </NTag>
      );
    }
  },
  {
    title: '是否全局抄送人',
    path: 'global',
    align: 'center',
    render: (row: Api.Hrm.EmployeeMappingRespVO) => {
      return (
        <NSwitch
          key={row.localEmployee.id}
          value={row.global}
          loading={row.switchLoading}
          disabled={!isBinding(row) && !hasAuth(['hrm:signature-account:update'])}
          onUpdateValue={async (val: boolean) => await handleChange(row, val)}
        />
      );
    }
  },
  {
    width: 120,
    title: $t('common.operate'),
    align: 'center',
    render: (row: Api.Hrm.EmployeeMappingRespVO) => {
      return roleBtn('绑定', ['hrm:signature-account:update'], 'primary', () => handleReassociate(row));
    }
  }
];

function isBinding(row: Api.Hrm.EmployeeMappingRespVO): boolean {
  return Boolean(row.mappingId) && Boolean(row.qiyuesuoEmployee);
}

async function handleReassociate(row: Api.Hrm.EmployeeMappingRespVO) {
  modalForm.values.value = {
    qiyuesuoId: row.qiyuesuoEmployee?.id || null,
    global: row.global || false,
    employeeId: row.localEmployee.id,
    localName: row.localEmployee.name
  };
  getOptions();
  modalForm.open();
}

async function handleChange(row: Api.Hrm.EmployeeMappingRespVO, status: boolean) {
  row.switchLoading = true;
  const oldStatus = row.global;
  row.global = status;

  try {
    await fetchPutEmployeeMapping(row.mappingId, { global: status });
    window?.$message?.success('抄送人设置成功');
    onChange();
  } catch (e: unknown) {
    row.global = oldStatus;
    window?.$message?.error('抄送人设置失败');
  } finally {
    row.switchLoading = false;
  }
}

function refresh() {
  onChange();
  getOptions();
}

async function getOptions() {
  const { data } = await handleGetQiyuesuoEmployee({ name: '' });
  qiyuesuoEmployeeOptions.value =
    data?.map(item => ({
      label: item.name,
      value: item.id,
      data: item.data
    })) || [];
}
onMounted(() => {
  onChange();
});
</script>

<template>
  <div class="h-full flex flex-col">
    <ProDataTable
      title="电子签账号绑定"
      size="small"
      flex-height
      :columns="columns"
      row-key="id"
      v-bind="tableProps"
      :pagination="false"
    >
      <template #toolbar>
        <NButton size="small" @click="refresh">
          <template #icon>
            <icon-mdi-refresh class="text-icon" />
          </template>
          {{ $t('common.refresh') }}
        </NButton>
      </template>
    </ProDataTable>
    <ProModalForm :form="modalForm" title="绑定电子签账号" :loading="fetchPostTodoLoading" preset="card" width="30%">
      <ReassociateForm :qiyuesuo-employee-options="qiyuesuoEmployeeOptions" />
    </ProModalForm>
  </div>
</template>
