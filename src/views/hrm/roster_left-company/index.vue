<script lang="tsx" setup>
import { NButton, NTag } from 'naive-ui';
import { useRouter } from 'vue-router';
import { useTable } from '@/hooks/common/table';
import { $t } from '@/locales';
import { useAppStore } from '@/store/modules/app';
import { fetchGetResignedList } from '@/service/api/hrm/roster_left-company';
import Search from './modules/search.vue';

const appStore = useAppStore();
const router = useRouter();
const {
  columns,
  columnChecks,
  data,
  getData,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams,
  getDataByPage
} = useTable({
  apiFn: fetchGetResignedList,
  showTotal: true,
  apiParams: {
    positionId: null,
    deptId: null,
    jobNumber: null,
    name: null
  },
  columns: () => [
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'name',
      title: '姓名',
      align: 'center'
    },
    {
      key: 'entryDate',
      title: '入职日期',
      align: 'center'
    },
    {
      key: 'dept.name',
      title: '部门',
      align: 'center'
    },
    {
      key: 'position.name',
      title: '职位',
      align: 'center'
    },
    {
      key: 'subStatus.name',
      title: '员工状态',
      align: 'center',
      render: (row: Api.Hrm.SimpleEmployeeInfo) => <NTag type="info">{row.subStatus?.name}</NTag>
    },
    {
      key: 'resignedDate',
      title: '离职日期',
      align: 'center'
    },
    {
      key: 'resignedReason',
      title: '离职原因',
      align: 'center'
    },

    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 180,
      render: (rowData: Api.Hrm.SimpleEmployeeInfo) => (
        <div class="flex-center gap-8px">
          <NButton
            type="primary"
            ghost
            size="small"
            onClick={() => router.push({ path: '/hrm/roster/detail', query: { id: rowData.id } })}
          >
            详情
          </NButton>
        </div>
      )
    }
  ]
});
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <Search v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <NCard :bordered="false" class="sm:flex-1-hidden card-wrapper" size="small" title="已离职">
      <template #header-extra>
        <TableHeaderOperation v-model:columns="columnChecks" :loading="loading" :show-add="false" @refresh="getData" />
      </template>
      <NDataTable
        :columns="columns"
        :data="data"
        :loading="loading"
        :pagination="mobilePagination"
        :row-key="row => row.id"
        :flex-height="!appStore.isMobile"
        remote
        size="small"
        class="sm:h-full"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
