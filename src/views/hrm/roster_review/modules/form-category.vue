<script setup lang="tsx">
import { NInput } from 'naive-ui';
import DateTimePicker from '@/components/common/date-time-picker.vue';

const emit = defineEmits(['changeContract']);
const model = defineModel<Api.Hrm.QiYueSuoTemplateParamsRespVO>('model', { required: true });
function inputBlur(item, val) {
  emit('changeContract', item, val);
}
</script>

<template>
  <!-- 文本  -->
  <NInput
    v-if="model.type === 'text' || model.type === 'person'"
    v-model:value="model.value"
    clearable
    :placeholder="model.description ? model.description : `请输入${model.name}`"
    :disabled="model?.expression?.disabled"
    @blur="inputBlur(model, model.value)"
  />
  <!-- 单选框  -->
  <NRadioGroup
    v-if="model.type === 'radio'"
    v-model:value="model.value"
    :disabled="model?.expression?.disabled"
    @update:value="inputBlur(model, model.value)"
  >
    <NRadioButton v-for="radio in model.optionsConfig" :key="radio" :value="radio.value">
      {{ radio.label }}
    </NRadioButton>
  </NRadioGroup>
  <!--      时间        -->
  <DateTimePicker
    v-if="model.type === 'date'"
    v-model:time="model.value"
    class="w-full"
    :label="model.name"
    use-for="date"
    :disabled="model?.expression?.disabled"
  />
  <!--      选择器        -->
  <div v-if="model.type === 'select'" class="w-full">
    <NSelect
      v-model:value="model.value"
      :options="(model.optionsConfig || []).map(item => ({ label: item, value: item }))"
      class="w-full"
      placeholder="选择歌曲"
      :disabled="model?.expression?.disabled"
      @update:value="inputBlur(model, model.value)"
    />
  </div>
</template>

<style scoped></style>
