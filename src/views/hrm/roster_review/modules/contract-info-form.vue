<script setup lang="ts">
import { useTemplateRef } from 'vue';

import ContractFormBase from './contract-form-base.vue';
import EmployeeBasicInfo from './employee-basic-info.vue';

interface IProps {
  isOnJob?: boolean;
  isLaunch?: boolean;
}

const props = defineProps<IProps>();
const contractFormRef = useTemplateRef<InstanceType<typeof ContractFormBase>>('contractFormRef');

const emit = defineEmits<{
  contractSelected: [data: any];
}>();

function getContractData() {
  return contractFormRef.value?.getContractData();
}

defineExpose({
  topFormValidate: () => contractFormRef.value?.topFormValidate(),
  formRefValidate: () => contractFormRef.value?.formRefValidate(),
  getContractData,
  get templateModel() {
    return contractFormRef.value?.templateModel;
  },
  get formModel() {
    return contractFormRef.value?.formModel;
  },
  get companyLabel() {
    return contractFormRef.value?.companyLabel;
  },
  get bizTypeLabel() {
    return contractFormRef.value?.bizTypeLabel;
  }
});
</script>

<template>
  <div>
    <NCard title="员工基本信息" content-class="!pb-2" header-class="!p-3">
      <EmployeeBasicInfo :show-info="!isLaunch" />
    </NCard>
    <slot name="middle" />
    <NCard title="发起合同" content-class="!pb-2" header-class="!p-3">
      <ContractFormBase
        ref="contractFormRef"
        :is-on-job="isOnJob"
        :is-launch="isLaunch"
        @contract-selected="$emit('contractSelected', $event)"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
