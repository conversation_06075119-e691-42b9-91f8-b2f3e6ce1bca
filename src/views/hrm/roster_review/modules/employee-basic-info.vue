<script setup lang="ts">
import { computed } from 'vue';

import { useRosterStore } from '@/store/modules/roster';

interface IProps {
  showInfo?: boolean;
}

const props = withDefaults(defineProps<IProps>(), {
  showInfo: true
});

const useRoster = useRosterStore();

const maxEducation = computed(() => {
  const list = useRoster.educationInfo;
  return Array.isArray(list) && list.length
    ? list.reduce((max, curr) => (curr.educationLevel > max.educationLevel ? curr : max))
    : null;
});

const maxWorkExperience = computed(() => {
  return useRoster.legalInfo.find(item => item.kind === 9);
});
const ResignationCertificate = computed(() => {
  if (maxWorkExperience.value?.missingReason && !maxWorkExperience.value?.expectedProvideDate) {
    return `无，原因：${maxWorkExperience.value?.missingReason}`;
  }
  if (maxWorkExperience.value?.missingReason && maxWorkExperience.value?.expectedProvideDate) {
    return `暂时无法提供，原因：${maxWorkExperience.value?.missingReason}；预计提供日期：${maxWorkExperience.value?.expectedProvideDate}`;
  }
  return '有';
});
</script>

<template>
  <NDescriptions v-if="showInfo" label-placement="top" bordered :column="8">
    <NDescriptionsItem label="姓名">{{ useRoster.baseInfo.name }}</NDescriptionsItem>
    <NDescriptionsItem label="身份证号">{{ useRoster.baseInfo.idNumber }}</NDescriptionsItem>
    <NDescriptionsItem label="最高学历">{{ maxEducation?.educationLevelText }}</NDescriptionsItem>
    <NDescriptionsItem label="岗位">{{ useRoster.baseInfo.entryPosition.name }}</NDescriptionsItem>
    <NDescriptionsItem label="离职证明">{{ ResignationCertificate }}</NDescriptionsItem>
    <NDescriptionsItem label="岗位属性">{{ useRoster.baseInfo.positionProperty }}</NDescriptionsItem>
    <NDescriptionsItem label="部门">{{ useRoster.baseInfo.deptName }}</NDescriptionsItem>
    <NDescriptionsItem label="公司名称">{{ useRoster.baseInfo.companyEntity }}</NDescriptionsItem>
  </NDescriptions>
</template>

<style scoped></style>
