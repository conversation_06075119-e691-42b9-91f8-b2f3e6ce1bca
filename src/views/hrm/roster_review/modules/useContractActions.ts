import { renderModal } from '@/components/re-modal';
import { fetchGetContactPreview } from '@/service/api/hrm/contract_info';
import { useRosterStore } from '@/store/modules/roster';
import PreviewInfo from '@/views/hrm/roster_review/modules/preview-info.vue';

export function useContractActions() {
  const useRoster = useRosterStore();

  // 预览合同
  async function previewContract(contractFormRef: any, resign: boolean = false) {
    try {
      await contractFormRef?.topFormValidate();
      await contractFormRef?.formRefValidate();

      const contractData = contractFormRef?.getContractData();
      const templateModel = contractFormRef?.templateModel;
      const companyLabel = contractFormRef?.companyLabel;
      const bizTypeLabel = contractFormRef?.bizTypeLabel;

      const { data, error } = await fetchGetContactPreview({
        ...templateModel,
        employeeId: Number(useRoster.userId),
        companyName: useRoster.companyInfo.name,
        templateParams: contractData,
        categoryName: bizTypeLabel,
        resign
      });

      if (error) return;

      const previewData = {
        ...data,
        userId: Number(useRoster.userId),
        bizTypeLabel,
        companyLabel
      };

      renderModal(PreviewInfo, previewData, {
        title: '预览合同',
        style: {
          width: '80%'
        }
      });
    } catch (error) {
      console.error('预览失败:', error);
      window.$message?.error('预览失败，请检查表单数据');
      throw error;
    }
  }

  // 验证并获取合同数据
  async function getValidatedContractData(contractFormRef: any) {
    try {
      await contractFormRef?.topFormValidate();
      await contractFormRef?.formRefValidate();

      const contractData = contractFormRef?.getContractData();
      const templateModel = contractFormRef?.templateModel;
      const formModel = contractFormRef?.formModel;
      const companyLabel = contractFormRef?.companyLabel;
      const bizTypeLabel = contractFormRef?.bizTypeLabel;

      return {
        contractData,
        templateModel,
        formModel,
        companyLabel,
        bizTypeLabel,
        employeeId: Number(useRoster.userId),
        companyName: useRoster.companyInfo.name
      };
    } catch (error) {
      console.error('数据验证失败:', error);
      throw error;
    }
  }

  return {
    previewContract,
    getValidatedContractData
  };
}
