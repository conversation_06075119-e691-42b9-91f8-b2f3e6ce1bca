<script setup lang="tsx">
import { h } from 'vue';

import { fetchPutContactPreview } from '@/service/api/hrm/contract_info';

interface Interface {
  id: number;
  data: string;
  userId: number;
  bizTypeLabel: string;
  companyLabel: string;
  hideBtn?: boolean;
}

const props = withDefaults(defineProps<Interface>(), {
  hideBtn: false
});

const router = useRouter();

async function handleSubmit() {
  const d = window.$dialog?.info({
    title: '系统提示',
    maskClosable: false,
    content: () =>
      h('div', [
        '请您确认以下关键信息是否准确：',
        h('br'),
        '合同主体：',
        h(
          'span',
          {
            style: {
              fontWeight: 'bold',
              fontSize: '16px',
              color: '#333',
              marginLeft: '4px'
            }
          },
          props.companyLabel
        ),
        h('br'),
        '业务分类：',
        h(
          'span',
          {
            style: {
              fontWeight: 'bold',
              fontSize: '16px',
              color: '#333',
              marginLeft: '4px'
            }
          },
          props.bizTypeLabel
        ),
        h('br'),
        h('br'),
        '上述信息将直接影响合同审批流程，如填写错误将导致退回并延误整体进度。请务必核对无误后再提交。'
      ]),
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        d.loading = true;
        const { error } = await fetchPutContactPreview(props.id, props.userId);
        if (error) return;
        window.$message?.success('提交成功');
        await router.push({
          path: '/hrm/roster/entry-pending'
        });
        window.$modal?.destroyAll();
      } catch (e) {
        console.log(e);
      } finally {
        d.loading = false;
      }
    }
  });
}
</script>

<template>
  <div>
    <div class="h-[80vh]">
      <iframe id="iframeContainer" class="size-full" :src="data" height="100%"></iframe>
    </div>
    <NSpace v-if="!hideBtn" :size="16" justify="end" class="mt-10px">
      <ReButton type="primary" @click="handleSubmit">提交</ReButton>
    </NSpace>
  </div>
</template>

<style scoped></style>
