<script setup lang="tsx">
import dayjs from 'dayjs';
import { NForm, NSelect, useDialog } from 'naive-ui';
import { computed, h, onMounted, reactive, ref, useTemplateRef } from 'vue';

import DateTimePicker from '@/components/common/date-time-picker.vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { fetchPutCompanyOptions } from '@/service/api/hrm/company-management';
import {
  fetchGetContactDetail,
  fetchGetContactOptions,
  fetchGetFeeInfo,
  fetchGetPreEntryDetail
} from '@/service/api/hrm/contract_info';
import { fetchGetPromotionConditionsByEmployeeId } from '@/service/api/hrm/roster_admission-conditions';
import { useRosterStore } from '@/store/modules/roster';
import { getFormatDictData } from '@/utils/async-functions';
import { calculateTrialEndDate } from '@/utils/date-format';
import FormCategory from '@/views/hrm/roster_review/modules/form-category.vue';

interface IProps {
  isOnJob?: boolean;
}

const props = defineProps<IProps>();

const emit = defineEmits<{
  contractSelected: [data: any];
}>();

const useRoster = useRosterStore();
const dialog = useDialog();
const { formRef, validate, restoreValidation } = useNaiveForm();
const topRef = useTemplateRef('topRef');
const { defaultRequiredRule } = useFormRules();
const contractOptions = ref<CommonType.Option<number>[]>([]);
const companyOptions = ref<CommonType.Option<number>[]>([]);
const hrmProbationPeriodTypeOptions = ref<Api.System.FormattedOption[]>([]);
const hrmEmployeeTypeOptions = ref<Api.System.FormattedOption[]>([]);
const templateModel = reactive({
  categoryId: null,
  companyId: null,
  employeeType: null,
  probationPeriodType: null,
  entryTime: null
});
const rules = ref({
  categoryId: defaultRequiredRule,
  companyId: defaultRequiredRule,
  employeeType: defaultRequiredRule,
  probationPeriodType: defaultRequiredRule,
  entryTime: defaultRequiredRule
});

const assessmentCriteria = reactive<Api.Hrm.UpdateOrCreatePromotionConditionsReqVO>({
  company: undefined,
  companyId: 0,
  employeeName: '',
  id: 0,
  idNumber: '',
  position: undefined,
  positionId: '',
  probationPerformanceCriteria: [],
  ratingRules: '',
  responsibilities: []
});

const columns = [
  {
    title: '考核项目',
    key: 'name'
  },
  {
    title: '第一个月',
    key: 'first'
  },
  {
    title: '第二个月',
    key: 'second'
  },
  {
    title: '第三个月',
    key: 'third'
  },
  {
    title: '评分规则',
    key: 'ratingRules'
  },
  {
    title: '权重（%）',
    key: 'weight'
  }
];

const isAssessmentCriteria = ref(false);
const formModel = reactive<{
  modelList: Api.Hrm.QiYueSuoTemplateRespVO[];
}>({
  modelList: []
});

const remainingAmount = reactive<Api.Hrm.FeeInfoRespVo>({
  lastUpdate: '',
  total: 0,
  remaining: 0
});

async function handleFetchGetContactOptions() {
  try {
    const { data, error } = await fetchGetContactOptions({
      companyName: companyOptions.value.find(i => i.value === templateModel.companyId)?.label || ''
    });
    if (error) {
      contractOptions.value = [];
      return;
    }
    contractOptions.value =
      (data || []).map(item => ({
        label: item.name,
        value: item.id
      })) || [];
  } catch (e) {
    console.log(e);
  }
}

const companyLabel = computed(() => {
  return companyOptions.value.find(i => i.value === templateModel.companyId)?.label || '';
});

const bizTypeLabel = computed(() => {
  return contractOptions.value.find(i => i.value === templateModel.categoryId)?.label || '';
});

const maxEducation = computed(() => {
  const list = useRoster.educationInfo;
  return Array.isArray(list) && list.length
    ? list.reduce((max, curr) => (curr.educationLevel > max.educationLevel ? curr : max))
    : null;
});

const maxWorkExperience = computed(() => {
  return useRoster.legalInfo.find(item => item.kind === 9);
});

const keyMap: Record<string, () => any> = {
  员工姓名: () => useRoster.baseInfo.name,
  员工性别: () => useRoster.baseInfo.genderText,
  员工身份证号: () => useRoster.baseInfo.idNumber,
  员工联系电话: () => useRoster.baseInfo.phoneNumber,
  员工电话: () => useRoster.baseInfo.phoneNumber,
  员工现居住地: () => useRoster.baseInfo.currentResidence,
  员工户籍所在地: () => useRoster.baseInfo.householdRegistration,
  公司名称: () => useRoster.companyInfo.name,
  公司地址: () => useRoster.companyInfo.address,
  法人联系电话: () => useRoster.companyInfo.contactWay,
  法人姓名: () => useRoster.companyInfo.legalPersonName,
  岗位: () => useRoster.baseInfo.entryPosition.name,
  员工毕业院校: () => maxEducation.value?.name ?? '',
  员工毕业时间: () => maxEducation.value?.end ?? '',
  员工学历类型: () => maxEducation.value?.educationLevelText ?? '',
  无离职证明原因: () => maxWorkExperience.value?.missingReason ?? '',
  员工入职时间: () => templateModel.entryTime,
  实习开始时间: () => templateModel.entryTime,
  合同期限形式: {
    type: 'radio',
    optionsConfig: [
      {
        label: '第一种',
        value: '一'
      },
      {
        label: '第二种',
        value: '二'
      },
      {
        label: '第三种',
        value: '三'
      }
    ]
  },
  工时制度: {
    type: 'radio',
    optionsConfig: [
      {
        label: '第一种',
        value: '1'
      },
      {
        label: '第二种',
        value: '2'
      },
      {
        label: '第三种',
        value: '3'
      }
    ]
  },
  发薪日期: {
    type: 'radio',
    optionsConfig: [
      {
        label: '每月20日',
        value: '20'
      },
      {
        label: '每月最后工作日',
        value: '最后工作'
      }
    ]
  }
};

async function selectContract() {
  await topRef.value?.validate();
  const d = dialog.info({
    title: '系统提示',
    content: () =>
      h('div', [
        '请您确认以下关键信息是否准确：',
        h('br'),
        '合同主体：',
        h(
          'span',
          {
            style: {
              fontWeight: 'bold',
              fontSize: '16px',
              color: '#333',
              marginLeft: '4px'
            }
          },
          companyLabel.value
        ),
        h('br'),
        '业务分类：',
        h(
          'span',
          {
            style: {
              fontWeight: 'bold',
              fontSize: '16px',
              color: '#333',
              marginLeft: '4px'
            }
          },
          bizTypeLabel.value
        ),
        h('br'),
        h('br'),
        '上述信息将直接影响合同审批流程，如填写错误将导致退回并延误整体进度。请务必核对无误后再提交。'
      ]),
    positiveText: '确定',
    negativeText: '取消',
    maskClosable: false,
    onPositiveClick: async () => {
      try {
        d.loading = true;
        if (formModel.modelList.length) formModel.modelList = [];
        await getContractDetail();
        const el = document.getElementById('__SCROLL_EL_ID__');

        el?.scrollTo({
          top: 500,
          behavior: 'smooth'
        });
      } finally {
        d.loading = false;
      }
    }
  });
}

async function getAdmissionConditions() {
  try {
    const { data, error } = await fetchGetPromotionConditionsByEmployeeId(useRoster.userId);
    if (error) return;
    Object.assign(assessmentCriteria, data);
  } catch (e) {
    console.log(e);
  }
}

async function getContractDetail() {
  try {
    const { data, error } = await fetchGetContactDetail(templateModel.categoryId);
    if (error || !data) return;

    const containsAssessmentCriteria = data.some(contract => contract.name.includes('试用期录用转正条件确认函'));

    if (containsAssessmentCriteria) {
      isAssessmentCriteria.value = true;
      await getAdmissionConditions();
    }
    for (const contract of data) {
      for (const param of contract.params) {
        const valueGetter = keyMap[param.name];

        if (typeof valueGetter === 'function') {
          param.value = valueGetter();
          param.expression = { api: '', dictType: '', key: '', type: '', disabled: true };
        } else {
          Object.assign(param, valueGetter);
          param.value = '';
        }
      }
    }

    formModel.modelList = data;

    // 触发事件，通知外部合同已选择
    emit('contractSelected', {
      templateModel: { ...templateModel },
      formModel: { ...formModel },
      companyLabel: companyLabel.value,
      bizTypeLabel: bizTypeLabel.value
    });
  } catch (err) {
    console.error('获取合同详情失败:', err);
  }
}

function renovateData() {
  formModel.modelList.forEach(item => {
    if (item.name.includes('试用期录用转正条件确认函')) {
      const responsibilities = assessmentCriteria?.responsibilities || [];
      const probation = assessmentCriteria?.probationPerformanceCriteria || [];

      item.params.forEach(param => {
        if (param.name === '试用期绩效考核标准') {
          const orderedKeys = ['name', 'first', 'second', 'third', 'ratingRules', 'weight'];
          param.value = JSON.stringify(probation.map(i => orderedKeys.map(key => i[key as keyof typeof i] ?? '')));
        }
      });

      const dutyParams = item.params.filter(p => /^岗位职责\d+$/.test(p.name));

      responsibilities.forEach((val, index) => {
        const existing = dutyParams[index];
        if (existing) {
          existing.value = `${index + 1}：${val}`;
        } else {
          item.params.push({
            name: `岗位职责${index + 1}`,
            type: 'text',
            options: null,
            required: false,
            expression: null,
            value: `${index + 1}：${val}`
          });
        }
      });
    }
  });
}

async function getContractDict() {
  const { data } = await getFormatDictData({
    keys: ['hrm_probation_period_type', 'hrm_employee_type', 'hrm_position_property'].join(',')
  });
  hrmProbationPeriodTypeOptions.value = data.hrm_probation_period_type || [];
  hrmEmployeeTypeOptions.value = data.hrm_employee_type || [];
}

function resetContractTime(contract) {
  ['一', '二', '三'].forEach(key => {
    const start = contract.params.find(p => p.name === `期限${key}合同开始时间`);
    const end = contract.params.find(p => p.name === `期限${key}合同结束时间`);
    const trialStart = contract.params.find(p => p.name === `期限${key}试用期开始时间`);
    const trialEnd = contract.params.find(p => p.name === `期限${key}试用期结束时间`);
    if (start) start.value = '';
    if (end) end.value = '';
    if (trialStart) trialStart.value = '';
    if (trialEnd) trialEnd.value = '';
  });
}

function changeContract(item, val: string) {
  if (item.name === '合同期限形式') {
    const contract = formModel.modelList.find(item => item.name === '一通数据劳动合同');
    if (!contract) return;
    resetContractTime(contract);

    if (!['一', '二', '三'].includes(val)) {
      window.$message?.error('合同期限形式只允许填写一、二、三');
      contract.params.forEach(param => {
        if (param.name === '合同期限形式') param.value = '';
      });
      return;
    }

    const startParam = contract.params.find(p => p.name === `期限${val}合同开始时间`);
    const endParam = contract.params.find(p => p.name === `期限${val}合同结束时间`);
    const trialStartsParam = contract.params.find(p => p.name === `期限${val}试用期开始时间`);
    const trialEndsParam = contract.params.find(p => p.name === `期限${val}试用期结束时间`);

    const entry = templateModel.entryTime;
    const probation = Number(templateModel.probationPeriodType);
    if (probation === 8) {
      resetContractTime(contract);
      return;
    }
    if (startParam) {
      startParam.value = entry;
    }

    if (endParam) {
      endParam.value = entry ? dayjs(entry).add(3, 'year').format('YYYY-MM-DD') : '';
    }

    if (trialStartsParam) {
      trialStartsParam.value = entry;
    }

    if (trialEndsParam) {
      trialEndsParam.value = calculateTrialEndDate(entry, probation);
    }
  }
}

async function handleGetCompanyOptions() {
  const { data, error } = await fetchPutCompanyOptions();
  if (error) return;
  companyOptions.value = data.map(item => ({
    label: item.name,
    value: item.id
  }));
}

async function handleFetchGetFeeInfo() {
  try {
    const companyName = companyOptions.value.find(i => i.value === templateModel.companyId)?.label || '';
    if (!companyName) return;
    const { data, error } = await fetchGetFeeInfo(companyName);
    if (error) return;
    Object.assign(remainingAmount, data);
  } catch (e) {
    console.log(e);
  }
}

async function handlePreEntryDetail() {
  const { data, error } = await fetchGetPreEntryDetail(Number(useRoster.userId));
  if (error) return;
  Object.assign(templateModel, {
    companyId: data?.companyId ?? null,
    employeeType: data?.employeeType !== null ? String(data.employeeType) : null,
    probationPeriodType: data?.probationPeriodType !== null ? String(data.probationPeriodType) : null,
    entryTime: data?.entryDate ?? null
  });
}

onMounted(async () => {
  restoreValidation();
  getContractDict();
  await handleGetCompanyOptions();
  await handlePreEntryDetail();
  if (templateModel.companyId) {
    await handleFetchGetFeeInfo();
    await useRoster.handleGetCompanyInfo(templateModel.companyId);
    await handleFetchGetContactOptions();
  }
});

function getContractData() {
  renovateData();

  const newData = formModel.modelList.flatMap(item => item.params);

  newData.forEach(param => {
    if ((param.name.includes('日期') || param.name.includes('时间')) && param.value) {
      const d = dayjs(param.value);
      if (d.isValid()) {
        param.value = d.format('YYYY年MM月DD日');
      }
    }
  });
  return newData;
}

defineExpose({
  topFormValidate: () => topRef.value?.validate(),
  formRefValidate: () => formRef.value?.validate(),
  getContractData,
  templateModel,
  formModel,
  companyLabel,
  bizTypeLabel
});
</script>

<template>
  <div>
    <NForm
      ref="topRef"
      :model="templateModel"
      :rules="rules"
      label-align="right"
      label-width="100px"
      label-placement="left"
    >
      <NGrid responsive="screen" item-responsive x-gap="20">
        <NFormItemGi span="24 l:12" label="合同主体" path="companyId">
          <template #label>
            <ToolTipHover label="公司主体" scene="邀请入职" />
          </template>
          <NSelect
            v-model:value="templateModel.companyId"
            :options="companyOptions"
            placeholder="请选择合同主体"
            @update:value="
              e => {
                handleFetchGetFeeInfo();
                useRoster.handleGetCompanyInfo(e);
                handleFetchGetContactOptions();
                templateModel.categoryId = null;
              }
            "
          />
        </NFormItemGi>

        <NFormItemGi span="24 l:12" label="业务分类" path="categoryId">
          <template #label>
            <ToolTipHover label="业务分类" scene="发起合同" />
          </template>
          <NSelect v-model:value="templateModel.categoryId" :options="contractOptions" placeholder="请选择业务分类" />
        </NFormItemGi>

        <NFormItemGi span="24 s:12 l:6" label="员工类型" path="employeeType">
          <NSelect
            v-model:value="templateModel.employeeType"
            :options="hrmEmployeeTypeOptions"
            placeholder="请选择员工类型"
          />
        </NFormItemGi>

        <NFormItemGi v-if="!isOnJob" span="24 s:12 l:6" label="试用期类型" path="probationPeriodType">
          <NSelect
            v-model:value="templateModel.probationPeriodType"
            clearable
            :options="hrmProbationPeriodTypeOptions"
            placeholder="请选择试用期类型"
          />
        </NFormItemGi>

        <NFormItemGi span="24 s:12 l:6" label="入职时间" path="entryTime">
          <DateTimePicker v-model:time="templateModel.entryTime" label="入职时间" use-for="date" class="w-full" />
        </NFormItemGi>

        <NFormItemGi span="24 s:12 l:6" label="电子签可用额度" label-width="110">
          <NPopover trigger="hover">
            <template #trigger>
              <span class="inline-flex cursor-pointer items-center gap-1">
                <span :class="{ 'text-red-600': remainingAmount.remaining <= 1000 }">
                  {{ remainingAmount.remaining }}
                </span>
                <span>/</span>
                <span>{{ remainingAmount.total }}</span>
              </span>
            </template>
            <div>数据更新时间：{{ remainingAmount.lastUpdate }}</div>
          </NPopover>
        </NFormItemGi>

        <NFormItemGi span="24">
          <div class="flex justify-end">
            <ReButton type="primary" @click="selectContract">确定</ReButton>
          </div>
        </NFormItemGi>
      </NGrid>
    </NForm>

    <div v-if="formModel.modelList.length">
      <NForm ref="formRef" :model="formModel" label-placement="top">
        <div v-for="(model, modelIndex) in formModel.modelList" :key="modelIndex">
          <NCard
            v-if="model.name !== '试用期录用转正条件确认函'"
            class="mt-10px"
            :title="`文件${modelIndex + 1} ${model.name}`"
          >
            <div v-if="Array.isArray(model.params) && model.params.length">
              <NGrid responsive="screen" item-responsive x-gap="20">
                <NFormItemGi
                  v-for="(item, itemIndex) in model.params"
                  :key="itemIndex"
                  :span="item.type === 'dynamictable' ? 24 : `xs:24 s:12 m:8 l:6 `"
                  :label="item.name"
                  :required="item?.required"
                  :path="`modelList[${modelIndex}].params[${itemIndex}].value`"
                  :rule="item?.required ? [{ required: true, message: `${item.name}是必填项` }] : undefined"
                >
                  <FormCategory v-model:model="model.params[itemIndex]" @change-contract="changeContract" />
                </NFormItemGi>
              </NGrid>
            </div>
            <div v-else>当前文件无参数,无需配置</div>
          </NCard>

          <NCard v-else class="mt-10px" title="试用期录用转正条件确认函">
            <NForm :model="assessmentCriteria" :rules="rules" label-placement="left">
              <NGrid responsive="screen" item-responsive x-gap="20">
                <NFormItemGi span="6 xs:24 s:12 m:8 l:6 " label="公司名称">
                  <NText>{{ assessmentCriteria?.company?.name }}</NText>
                </NFormItemGi>
                <NFormItemGi span="6 xs:24 s:12 m:8 l:6 " label="员工姓名">
                  <NText>{{ assessmentCriteria?.employeeName }}</NText>
                </NFormItemGi>
                <NFormItemGi span="6 xs:24 s:12 m:8 l:6 " label="岗位">
                  <NText>{{ assessmentCriteria?.position?.name }}</NText>
                </NFormItemGi>
                <NFormItemGi span="6 xs:24 s:12 m:8 l:6 " label="身份证号">
                  <NText>{{ assessmentCriteria?.idNumber }}</NText>
                </NFormItemGi>
                <NFormItemGi span="8 s:8 m:8" label="岗位职责" label-placement="top">
                  <div class="flex flex-col">
                    <div v-for="(item, index) in assessmentCriteria?.responsibilities" :key="index">
                      {{ index + 1 }}: {{ item }}
                    </div>
                  </div>
                </NFormItemGi>
                <NFormItemGi span="16 s:16 m:16" label="试用期绩效考核" label-placement="top">
                  <div class="w-full flex flex-col">
                    <NDataTable
                      :columns="columns"
                      :data="assessmentCriteria.probationPerformanceCriteria"
                      :pagination="false"
                      bordered
                      size="small"
                    />
                  </div>
                </NFormItemGi>
              </NGrid>
            </NForm>
          </NCard>
        </div>
      </NForm>
    </div>
  </div>
</template>

<style scoped></style>
