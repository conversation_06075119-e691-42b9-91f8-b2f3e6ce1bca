<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';

import BlackList from './black-list.vue';

import { renderModal } from '@/components/re-modal';
import { HrmStepTypeOptions, PreEntryStatus, ReviewStatusSub } from '@/enum';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { fetchPostBlackList } from '@/service/api/hrm/roster_black';
import { putReviewEmployee } from '@/service/api/hrm/roster_user';
import { useRosterStore } from '@/store/modules/roster';
import InvitationForm from '@/views/hrm/roster_entry-pending/modules/invitation-form.vue';
import ResignedHistory from '@/views/hrm/roster_left-history/index.vue';
const useRoster = useRosterStore();
const { formRef, validate } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();
const model: Api.Hrm.ReviewEmployeeReqVO = reactive(createDefaultModel());
const InvitationFormRef = useTemplateRef<InstanceType<typeof InvitationForm>>('InvitationFormRef');

function createDefaultModel(): Api.Hrm.ReviewEmployeeReqVO {
  return {
    blackReason: '',
    errorSteps: [],
    isBlack: null!,
    reason: '',
    status: null!,
    isInvite: null!
  };
}

type RuleKey = keyof Api.Hrm.ReviewEmployeeReqVO;

const rules = ref<Record<RuleKey, App.Global.FormRule>>({
  status: defaultRequiredRule,
  isInvite: defaultRequiredRule,
  errorSteps: [
    {
      required: true,
      type: 'array',
      message: '请选择错误步骤',
      trigger: 'change'
    }
  ],
  reason: defaultRequiredRule,
  isBlack: defaultRequiredRule,
  blackReason: defaultRequiredRule
});

function handleInitModel() {
  Object.assign(model, createDefaultModel());
}

async function handleSubmit() {
  try {
    // 表单校验
    await validate();
    let reviewId: number | null = null;
    // 提交审核信息
    const { data, error: reviewError } = await putReviewEmployee(useRoster.userId, model);
    reviewId = data || null;
    if (reviewError) {
      console.error('审核失败:', reviewError);
      window.$message?.error('审核失败');
      return false;
    }

    // 处理邀约提交（前置必须是审核成功）
    if (model.status && model.isInvite) {
      await InvitationFormRef.value?.exportFormAlidate();

      const invitationError = await InvitationFormRef.value?.handleSubmit(reviewId);
      if (!invitationError) {
        console.error('邀约提交失败:', invitationError);
        window.$message?.error('邀约提交失败');
        // return false;
      }
    }

    // 加入黑名单（前置也是审核成功）
    if (model.isBlack) {
      const { error: blackError } = await fetchPostBlackList({
        employeeId: Number(useRoster.baseInfo.id),
        reason: model.blackReason,
        idNumber: useRoster.baseInfo.idNumber,
        name: useRoster.baseInfo.name,
        reviewId
      });

      if (blackError) {
        console.error('加入黑名单失败:', blackError);
        window.$message?.error('加入黑名单失败');
        // return false;
      }
    }

    window.$message?.success('审核完成');
    await useRoster.handleGetStatus(useRoster.userId as string);
    return true;
  } catch (e) {
    console.error('提交过程中发生错误:', e);
    window.$message?.error('提交过程中发生错误');
    return false;
  }
}

function handleOpen() {
  renderModal(
    ResignedHistory,
    {
      name: useRoster.baseInfo.name
    },
    {
      title: '离职员工',
      style: {
        width: '80%'
      },
      contentStyle: {
        display: 'flex',
        flexDirection: 'column',
        height: '70vh'
      }
    }
  );
}

function handleOpenBlackList() {
  renderModal(
    BlackList,
    {},
    {
      title: '黑名单',
      style: {
        width: '80%'
      },
      contentStyle: {
        display: 'flex',
        flexDirection: 'column',
        height: '70vh'
      }
    }
  );
}
watch(
  () => useRoster.baseInfo.name,
  (newName: string) => {
    if (newName) {
      useRoster.handleGetResignedNumber(newName);
    }
  }
);
onMounted(() => {
  handleInitModel();
});
defineExpose({
  handleSubmit
});
</script>

<template>
  <div v-if="[PreEntryStatus.Reviewing].includes(useRoster.statusInfo.subStatusId)">
    <div class="conflict-list mb-10px">
      <NAlert v-if="!useRoster.blackList.length" type="success" class="mb-10px">
        系统未检索到与黑名单匹配的记录。
      </NAlert>
      <div v-else>
        <NAlert type="warning" class="mb-10px">
          系统共检索到
          <span class="cursor-pointer text-red-500 underline" @click="handleOpenBlackList">
            {{ useRoster.blackList.length }}
          </span>
          条与
          {{ useRoster.baseInfo.name }}
          匹配的黑名单记录。
        </NAlert>
      </div>
      <NAlert v-if="useRoster.resignedNumber" type="warning" class="mb-10px">
        系统共检索到
        <span class="cursor-pointer text-red-500 underline" @click="handleOpen">
          {{ useRoster.resignedNumber }}
        </span>
        条与
        {{ useRoster.baseInfo.name }}
        匹配的离职员工记录。
      </NAlert>
      <NAlert v-else type="success" class="mb-10px">系统未检索到与离职员工匹配的记录。</NAlert>
    </div>
    <NForm ref="formRef" :model="model" :rules="rules">
      <FormItemDes label="是否通过" path="status" scene="待入职">
        <NRadioGroup v-model:value="model.status">
          <NRadioButton
            v-for="(song, index) in [
              { label: '通过', value: true },
              { label: '不通过', value: false }
            ]"
            :key="index"
            :value="song.value"
            :label="song.label"
          />
        </NRadioGroup>
      </FormItemDes>
      <div v-if="model.status === false">
        <FormItemDes label="错误步骤" path="errorSteps" scene="待入职">
          <NSelect
            v-model:value="model.errorSteps"
            :options="HrmStepTypeOptions"
            size="small"
            multiple
            placeholder="请选择错误步骤"
          />
        </FormItemDes>
        <FormItemDes label="不通过的原因" path="reason" scene="待入职">
          <NInput v-model:value="model.reason" placeholder="请输入不通过的原因" clearable type="textarea" :rows="2" />
        </FormItemDes>
        <FormItemDes label="是否加入黑名单" path="isBlack" scene="待入职">
          <NRadioGroup v-model:value="model.isBlack">
            <NRadioButton
              v-for="(song, index) in [
                { label: '是', value: true },
                { label: '否', value: false }
              ]"
              :key="index"
              :value="song.value"
              :label="song.label"
            />
          </NRadioGroup>
        </FormItemDes>
        <NFormItem v-if="model.isBlack" span="24 s:12 m:6" label="拉黑原因" path="blackReason">
          <NInput v-model:value="model.blackReason" clearable placeholder="请输入拉黑原因" type="textarea" />
        </NFormItem>
      </div>

      <div v-if="model.status === true">
        <FormItemDes label="是否邀请入职" path="isInvite" scene="待入职">
          <NRadioGroup v-model:value="model.isInvite">
            <NRadioButton
              v-for="(song, index) in [
                { label: '是', value: true },
                { label: '否', value: false }
              ]"
              :key="index"
              :value="song.value"
              :label="song.label"
            />
          </NRadioGroup>
        </FormItemDes>
        <InvitationForm
          v-if="model.isInvite === true"
          ref="InvitationFormRef"
          :use-ids="[useRoster.baseInfo.id]"
          type="single"
        />
      </div>

      <NSpace :size="16" justify="end">
        <ReButton v-hasPermi="['hrm:employee:review']" type="primary" @click="handleSubmit">
          {{ $t('common.confirm') }}
        </ReButton>
      </NSpace>
    </NForm>
  </div>
  <div v-else>
    <div class="border rounded-md bg-gray-50 p-4 pb-4 text-sm text-gray-700">
      <div class="mb-2">
        <span class="text-gray-800 font-medium">审核结果：</span>
        <NTag size="small" :type="useRoster.reviewInfo.reviewStatus === ReviewStatusSub.Pass ? 'success' : 'error'">
          {{ useRoster.reviewInfo.reviewStatus === ReviewStatusSub.Pass ? '审核通过' : '审核不通过' }}
        </NTag>
      </div>
      <div class="mb-2">
        <span class="text-gray-800 font-medium">审核人：</span>
        {{ useRoster.reviewInfo?.reviewer?.name || '-' }}
      </div>
      <div class="mb-2">
        <span class="text-gray-800 font-medium">审核时间：</span>
        {{ useRoster.reviewInfo.reviewTime || '-' }}
      </div>
      <div v-if="useRoster.reviewInfo.reviewStatus === ReviewStatusSub.Reject" class="mt-2 text-red-600">
        <span class="font-medium">未通过原因：</span>
        {{ useRoster.reviewInfo.rejectionReason || '无' }}
      </div>
    </div>
  </div>
</template>

<style scoped></style>
