<script setup lang="ts">
import { useTemplateRef } from 'vue';

import ContractFormPure from './contract-form-pure.vue';

interface IProps {
  isOnJob?: boolean;
  isLaunch?: boolean;
}

const props = defineProps<IProps>();

const emit = defineEmits<{
  contractSelected: [data: any];
}>();

const contractFormRef = useTemplateRef<InstanceType<typeof ContractFormPure>>('contractFormRef');

// 处理合同选择完成事件
function handleContractSelected(data: any) {
  emit('contractSelected', data);
}

// 暴露方法给外部调用
function getContractData() {
  return contractFormRef.value?.getContractData();
}

function topFormValidate() {
  return contractFormRef.value?.topFormValidate();
}

function formRefValidate() {
  return contractFormRef.value?.formRefValidate();
}

defineExpose({
  topFormValidate,
  formRefValidate,
  getContractData,
  get templateModel() {
    return contractFormRef.value?.templateModel;
  },
  get formModel() {
    return contractFormRef.value?.formModel;
  },
  get companyLabel() {
    return contractFormRef.value?.companyLabel;
  },
  get bizTypeLabel() {
    return contractFormRef.value?.bizTypeLabel;
  }
});
</script>

<template>
  <div>
    <ContractFormPure ref="contractFormRef" :is-on-job="isOnJob" @contract-selected="handleContractSelected" />
  </div>
</template>

<style scoped></style>
