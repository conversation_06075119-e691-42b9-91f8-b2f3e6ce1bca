# 合同表单组件使用指南

## 组件架构

现在的组件架构更加灵活，每个使用的地方都可以有自己的按钮逻辑：

```
contract-form-pure.vue          - 纯表单组件（无按钮）
contract-form-base.vue          - 基础组合组件（纯表单 + 员工信息显示，无按钮）
contract-info-form.vue          - 完整表单组件（员工信息 + 合同表单，无按钮）
useContractActions.ts           - 工具函数（预览、提交逻辑）
```

## 使用方式

### 1. 使用纯表单组件（推荐）

```vue
<template>
  <div>
    <ContractFormPure 
      ref="formRef" 
      :is-on-job="false" 
      @contract-selected="handleContractSelected" 
    />
    
    <!-- 你的自定义按钮 -->
    <div v-if="hasContractData" class="mt-4 flex justify-end gap-3">
      <NButton @click="handlePreview">预览合同</NButton>
      <NButton @click="handleSubmit">提交合同</NButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import ContractFormPure from '@/views/hrm/roster_review/modules/contract-form-pure.vue';
import { useContractActions } from '@/views/hrm/roster_review/modules/useContractActions';

const formRef = ref();
const hasContractData = ref(false);
const { previewContract, getValidatedContractData } = useContractActions();

function handleContractSelected(data: any) {
  hasContractData.value = data.formModel?.modelList?.length > 0;
}

async function handlePreview() {
  await previewContract(formRef.value);
}

async function handleSubmit() {
  const contractData = await getValidatedContractData(formRef.value);
  
  // 这里可以调用你的自定义逻辑
  await yourCustomSubmitLogic(contractData);
  
  window.$message?.success('提交成功！');
}

async function yourCustomSubmitLogic(contractData: any) {
  // 在这里可以调用引用页面的方法
  // 例如：调用父组件方法、路由跳转、额外的数据处理等
  console.log('执行自定义提交逻辑:', contractData);
}
</script>
```

### 2. 使用完整表单组件

```vue
<template>
  <div>
    <ContractInfoForm 
      ref="formRef" 
      :is-on-job="false" 
      :is-launch="false"
      @contract-selected="handleContractSelected" 
    />
    
    <!-- 你的自定义按钮 -->
    <div v-if="hasContractData" class="mt-4 flex justify-end gap-3">
      <NButton @click="handlePreview">预览合同</NButton>
      <NButton @click="handleSubmit">提交合同</NButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import ContractInfoForm from '@/views/hrm/roster_review/modules/contract-info-form.vue';
import { useContractActions } from '@/views/hrm/roster_review/modules/useContractActions';

// 类似的逻辑...
</script>
```

### 3. 使用预构建的带按钮组件

```vue
<template>
  <ContractInfoFormWithActions :is-on-job="false" :is-launch="false" />
</template>

<script setup lang="ts">
import ContractInfoFormWithActions from '@/views/hrm/roster_review/modules/contract-info-form-with-actions.vue';
</script>
```

## useContractActions 工具函数

### previewContract(formRef)
- 验证表单并预览合同
- 自动打开预览弹窗
- 返回预览数据

### getValidatedContractData(formRef)
- 验证表单并获取所有合同数据
- 返回结构化的合同数据对象
- 包含：contractData, templateModel, formModel, companyLabel, bizTypeLabel, employeeId, companyName

## 优势

1. **灵活性** - 每个页面可以有自己的按钮逻辑
2. **复用性** - 表单组件纯粹，按钮逻辑独立
3. **可扩展性** - 可以在每个页面调用引用页面的方法
4. **维护性** - 职责分离，易于维护

## 迁移指南

如果你之前使用的是带按钮的组件，可以：

1. 将按钮逻辑移到使用的地方
2. 使用 `useContractActions` 工具函数简化代码
3. 根据需要添加自定义逻辑

这样每个页面都可以有自己的按钮控制权，并且能够调用引用页面的方法。