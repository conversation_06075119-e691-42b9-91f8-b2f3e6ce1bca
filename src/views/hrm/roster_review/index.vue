<script setup lang="ts">
import { onMounted } from 'vue';

import { useRosterStore } from '@/store/modules/roster';
import DetailCom from '@/views/hrm/roster_detail/components/detail-com.vue';

const useRoster = useRosterStore();
const route = useRoute();
onMounted(() => {
  useRoster.userId = route.query.id as string;
  useRoster.handleGetStatus(useRoster.userId);
  useRoster.handleFetchSimilarBlackList(useRoster.userId);
});
</script>

<template>
  <DetailCom />
</template>
