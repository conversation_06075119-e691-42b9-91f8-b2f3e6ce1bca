<script setup lang="ts">
import EmployeeProfile from './moudules/employee-profile.vue';
import ProgressTracker from './moudules/progress-tracker.vue';
import TopInfoForm from './moudules/top-info-form.vue';

import { useRosterStore } from '@/store/modules/roster';

const useRoster = useRosterStore();
const route = useRoute();
const router = useRouter();

function handleBack() {
  router.back();
}

onMounted(async () => {
  useRoster.resetSections();
  useRoster.userId = route.query.id as string;
  await useRoster.getDictData();
  await useRoster.handleGetHrmBasicInfo(useRoster.userId as string);
});
</script>

<template>
  <div class="mx-auto h-auto w-80% cursor-pointer overflow-auto !bg-white">
    <NButton type="primary" ghost size="small" @click="handleBack">
      <icon-ic-round-arrow-back />
    </NButton>
    <!-- 头部 -->
    <TopInfoForm />
    <!-- 基础信息 -->
    <EmployeeProfile />
    <!-- 事件记录 -->
    <ProgressTracker />
  </div>
</template>
