<script setup lang="ts">
import { useRouter } from 'vue-router';

import { useDict } from '@/hooks/business/useDict';
import { useRosterStore } from '@/store/modules/roster';

const router = useRouter();
const useRoster = useRosterStore();
const { baseInfo } = storeToRefs(useRoster);
const { hrm_employee_type } = useDict(['hrm_employee_type']);

function handleClick() {
  router.push({ path: '/hrm/roster/detail', query: { id: useRoster.userId } });
}

function getEmployeeType(id: number) {
  if (!id) return '';
  return hrm_employee_type.value.find(item => item.value === id)?.label || '';
}
</script>

<template>
  <div>
    <ModuleHeader title="员工档案" :on-click="handleClick" />
    <NForm inline label-placement="left" label-width="100px">
      <NRow>
        <NCol :span="8">
          <NFormItem label="入职日期：" prop="entryDate">
            {{ baseInfo?.entryDate }}
          </NFormItem>
        </NCol>
        <NCol :span="8">
          <NFormItem label="工号：">
            {{ baseInfo?.jobNumber }}
          </NFormItem>
        </NCol>
        <NCol :span="8">
          <NFormItem label="出生日期：">
            {{ baseInfo?.birthday }}
          </NFormItem>
        </NCol>
        <NCol :span="8">
          <NFormItem label="性别：">
            {{ baseInfo?.genderText }}
          </NFormItem>
        </NCol>
        <NCol :span="8">
          <NFormItem label="员工类型：">
            {{ getEmployeeType(baseInfo?.employeeType) }}
          </NFormItem>
        </NCol>
      </NRow>
    </NForm>
  </div>
</template>

<style scoped lang="scss"></style>
