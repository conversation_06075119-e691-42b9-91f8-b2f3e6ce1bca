<script setup lang="ts">
import { useThrottleFn } from '@vueuse/core';

import { fetchGetEmployeeEventTimeline } from '@/service/api/hrm/roster_profile';
import { getToken } from '@/store/modules/auth/shared';

const route = useRoute();
const scrollContainerRef = ref<HTMLElement>();

const employeeEventTimeline = ref<{
  list: Api.Hrm.EmployeeEventTimelineRespVO[];
  total: number;
}>({
  list: [],
  total: 0
});

// 控制加载状态
const loading = ref(false);
const finished = ref(false);

// 下次请求参数
const employeeEventTimelineSearchParams = reactive({
  pageNo: 1,
  pageSize: 10
});

// 加载更多数据
async function loadMore() {
  if (loading.value || finished.value) return;

  loading.value = true;
  const { data, error } = await fetchGetEmployeeEventTimeline(
    route.query.id as string,
    employeeEventTimelineSearchParams
  );
  loading.value = false;

  if (error) return;

  if (data.list.length > 0) {
    employeeEventTimeline.value.list.push(...data.list);
    employeeEventTimeline.value.total = data.total;
    employeeEventTimelineSearchParams.pageNo += 1;
  }

  if (employeeEventTimeline.value.list.length >= data.total) {
    finished.value = true;
  }
}

// 初始加载
onMounted(() => {
  loadMore();
});

// 处理点击文件链接
const handleClick = (url: string) => {
  // 如果包含dingtalk 就不拼接token
  if (url.includes('dingtalk')) {
    window.open(url, '_blank');
  } else {
    window.open(`${url}?token=${getToken()}`, '_blank');
  }
};

// 滚动触发懒加载
const handleScroll = useThrottleFn(
  () => {
    const el = scrollContainerRef.value;
    if (!el || loading.value || finished.value) return;

    const threshold = 100;
    if (el.scrollHeight - el.scrollTop - el.clientHeight <= threshold) {
      loadMore();
    }
  },
  { wait: 500, trailing: true }
);
</script>

<template>
  <ModuleHeader title="事件记录" />

  <!-- 列表容器，具有滚动条 -->
  <div
    ref="scrollContainerRef"
    class="mx-auto max-h-[480px] w-[70%] overflow-auto border rounded bg-white shadow-lg"
    @scroll="handleScroll"
  >
    <div v-if="employeeEventTimeline.list.length > 0">
      <div
        v-for="(item, index) in employeeEventTimeline.list"
        :key="index"
        class="item min-h-[60px] flex items-start gap-4 border-b border-gray-200 px-4 py-3"
      >
        <!-- 日期列 -->
        <div class="w-1/5 text-sm text-gray-600">{{ item.operationDate }}</div>
        <!-- 内容列 -->
        <div class="flex-1">
          <div class="mb-1 flex items-center text-base text-gray-900 font-semibold">
            <span>{{ item.title }}</span>
            <div @click="handleClick(item.url)">
              <SvgIcon v-if="item.url" class="ml-2 cursor-pointer text-[#0089ff]" icon="ic:outline-insert-drive-file" />
            </div>
          </div>
          <div class="text-sm text-gray-500">{{ item.description }}</div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="py-10">
      <NEmpty description="暂无数据" />
    </div>

    <!-- 加载提示 -->
    <div v-if="loading" class="mt-2 text-center text-sm text-gray-500">加载中...</div>
    <div v-else-if="finished" class="mt-2 text-center text-sm text-gray-400">已加载全部</div>
  </div>
</template>
