<script setup lang="ts">
import { storeToRefs } from 'pinia';

import Avatar from '@/assets/imgs/avatar.png';
import { useRosterStore } from '@/store/modules/roster';
import { getFileUrl } from '@/utils/get-file-url';
const useRoster = useRosterStore();
const { baseInfo } = storeToRefs(useRoster);
</script>

<template>
  <div class="w-full flex flex-col items-center gap-2 py-6">
    <!-- 头像 -->
    <div class="overflow-hidden rounded-3">
      <NImage
        :src="baseInfo.electronicPhotoId ? getFileUrl(baseInfo.electronicPhotoId) : Avatar"
        class="h-120px max-w-120px"
      />
    </div>

    <!-- 姓名 + 状态 -->
    <div class="text-center">
      <div class="flex items-center justify-center gap-2 text-lg text-gray-900 font-semibold">
        <span>{{ baseInfo.name }}</span>
        <NTag type="success" size="small">{{ baseInfo.status?.name }}({{ baseInfo.status?.data?.name }})</NTag>
      </div>
    </div>

    <!-- 部门信息 -->
    <div class="px-4 text-center text-sm text-gray-700 leading-snug">
      <div>
        <span class="text-primary font-medium">{{ baseInfo.deptName }}</span>
        <NTag type="success" size="small" class="ml-1">主部门</NTag>
        <template v-if="baseInfo.companyEntity">
          <span class="mx-1 text-gray-400">/</span>
          {{ baseInfo.companyEntity }}
        </template>
      </div>
    </div>

    <!-- 岗位 -->
    <div v-if="baseInfo.entryPosition" class="text-sm text-gray-800">{{ baseInfo?.position?.name }}</div>

    <!-- 入职天数 -->
    <div v-if="baseInfo.workDay" class="text-xs text-gray-500">
      在{{ baseInfo.companyEntity }}工作了{{ baseInfo.workDay }}天
    </div>
  </div>
</template>
