<script lang="tsx" setup>
import type { ProSearchFormColumns } from 'pro-naive-ui';
import { createProSearchForm } from 'pro-naive-ui';

import { renderModalBtn } from '@/components/re-modal';
import { editRoleBtn } from '@/directives/permission/permi-btn';
import { useTable } from '@/hooks/common/table';
import { $t } from '@/locales';
import { fetchGetCompanyList } from '@/service/api/hrm/company-management';
import { useAppStore } from '@/store/modules/app';
import Form from '@/views/hrm/company-management/modules/form.vue';

const appStore = useAppStore();
const {
  columns,
  columnChecks,
  data,
  getData,
  loading,
  mobilePagination,
  resetSearchParams,
  getDataByPage,
  updateSearchParams
} = useTable({
  apiFn: fetchGetCompanyList,
  showTotal: true,
  apiParams: {
    pageNo: 1,
    pageSize: 10,
    name: null,
    legalPersonName: null,
    contactWay: null
  },
  columns: () => [
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'name',
      title: '公司主体',
      align: 'center'
    },
    {
      key: 'address',
      title: '地址',
      align: 'center'
    },
    {
      key: 'legalPersonName',
      title: '法人姓名',
      align: 'center'
    },
    {
      key: 'contactWay',
      title: '联系方式',
      align: 'center'
    },
    {
      key: 'createTime',
      title: '创建时间',
      align: 'center'
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 130,
      render: (row: Api.Hrm.CompanyListRespVO) => (
        <div class="flex-center gap-8px">{editRoleBtn(['hrm:company:update'], () => handleOperate('edit', row))}</div>
      )
    }
  ]
});

function handleOperate(type: NaiveUI.TableOperateType, row: undefined | Api.Hrm.CompanyListRespVO = undefined) {
  renderModalBtn(
    Form,
    { operateType: type, rowData: row && row },
    {
      title: type === 'add' ? '创建分公司' : '编辑分公司',
      style: {
        width: '40%'
      },
      func: getData
    }
  );
}

const searchColumns = ref<ProSearchFormColumns<Api.Hrm.CompanyListRespParams>>([
  {
    title: '公司主体',
    path: 'name'
  },
  {
    title: '法人姓名',
    path: 'legalPersonName'
  },
  {
    title: '联系方式',
    path: 'contactWay'
  }
]);

const form = createProSearchForm({
  defaultCollapsed: true,
  onReset: () => {
    resetSearchParams();
    getDataByPage();
  },
  onSubmit: () => {
    updateSearchParams({
      pageNo: 1,
      ...form.fieldsValue.value
    });
    getDataByPage();
  }
});
</script>

<template>
  <div class="min-h-500px flex-col-stretch overflow-hidden lt-sm:overflow-auto">
    <ProCard class="mb-10px" :show-collapse="false" content-class="!pb-[0px]">
      <ProSearchForm :form="form" :columns="searchColumns" />
    </ProCard>
    <NCard :bordered="false" class="sm:flex-1-hidden card-wrapper" size="small" title="分公司">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :loading="loading"
          :permission="['hrm:company:create']"
          @add="handleOperate('add')"
          @refresh="getData"
        />
      </template>
      <NDataTable
        :columns="columns"
        :data="data"
        :loading="loading"
        :pagination="mobilePagination"
        :row-key="row => row.id"
        :flex-height="!appStore.isMobile"
        remote
        size="small"
        class="sm:h-full"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
