<script setup lang="ts">
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { fetchPostCompany, fetchPutCompany } from '@/service/api/hrm/company-management';
defineOptions({
  name: 'CompanyManagementForm'
});

interface IProps {
  rowData?: Api.Hrm.SimpleEmployeeInfo;
  operateType: NaiveUI.TableOperateType;
}
const props = defineProps<IProps>();
type RuleKey = keyof Omit<Api.Hrm.CompanyListRespVO, 'id'>;
const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();
const rules = ref<Record<RuleKey, App.Global.FormRule>>({
  address: defaultRequiredRule,
  contactWay: defaultRequiredRule,
  legalPersonName: defaultRequiredRule,
  name: defaultRequiredRule
});
const model = reactive<Api.Hrm.CompanyListRespVO>({
  address: '',
  contactWay: '',
  id: undefined,
  legalPersonName: '',
  name: ''
});
async function handleSubmit() {
  try {
    await validate();

    let result;
    if (props.operateType === 'edit') {
      result = await fetchPutCompany(model.id, model);
    } else if (props.operateType === 'add') {
      result = await fetchPostCompany(model);
    } else {
      console.warn('未知操作类型:', props.operateType);
      return false;
    }

    if (result.error) return false;

    const successMsg = props.operateType === 'edit' ? $t('common.updateSuccess') : $t('common.addSuccess');
    window.$message?.success(successMsg);
    return true;
  } catch (e) {
    console.error('提交失败:', e);
    return false;
  }
}

defineExpose({
  handleSubmit
});
onMounted(() => {
  restoreValidation();
  if (props.operateType === 'edit') {
    Object.assign(model, props.rowData);
  }
});
</script>

<template>
  <NForm ref="formRef" :model="model" :rules="rules">
    <NAlert type="warning" class="mb-10px">
      公司信息将作为合同签订时的合同主体内容，请准确填写，避免错别字或信息错误！
    </NAlert>
    <NFormItem span="24 s:12 m:6" label="公司主体" path="name" class="pr-24px">
      <NInput v-model:value="model.name" clearable placeholder="请输入公司主体" />
    </NFormItem>
    <NFormItem span="24 s:12 m:6" label="地址" path="address" class="pr-24px">
      <NInput v-model:value="model.address" clearable placeholder="请输入地址" />
    </NFormItem>
    <NFormItem span="24 s:12 m:6" label="法人姓名" path="reason" class="pr-24px">
      <NInput v-model:value="model.legalPersonName" clearable placeholder="请输入法人姓名" />
    </NFormItem>
    <NFormItem span="24 s:12 m:6" label="联系方式" path="contactWay" class="pr-24px">
      <NInput v-model:value="model.contactWay" clearable placeholder="请输入联系方式" />
    </NFormItem>
  </NForm>
</template>

<style scoped></style>
