<script lang="tsx" setup>
import { NTag } from 'naive-ui';
import { useRouter } from 'vue-router';

import { renderModal, renderModalBtn } from '@/components/re-modal';
import { roleBtn } from '@/directives/permission/permi-btn';
import { PreEntryStatus } from '@/enum';
import { useDict } from '@/hooks/business/useDict';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { useEmployeeSearchForm } from '@/hooks/hrm/useEmployeeSearchForm';
import { $t } from '@/locales';
import { fetchGetPreEntryList } from '@/service/api/hrm/roster_entry-pending';
import { hrmBaseUrl } from '@/service/request';
import { useAppStore } from '@/store/modules/app';
import { getToken } from '@/store/modules/auth/shared';
import { getAllPositionOptions } from '@/utils/async-functions';
import { getProxyUrl } from '@/utils/get-file-url';
import GiveupForm from '@/views/hrm/roster_entry-pending/modules/give-up-form.vue';
import InvitationForm from '@/views/hrm/roster_entry-pending/modules/invitation-form.vue';
import PreviewInfo from '@/views/hrm/roster_review/modules/preview-info.vue';

const appStore = useAppStore();
const router = useRouter();
const { hrm_pre_entry_status, hrm_position_property } = useDict(['hrm_pre_entry_status', 'hrm_position_property']);
const {
  columns,
  columnChecks,
  data,
  getData,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams,
  getDataByPage,
  updateSearchParams
} = useTable({
  apiFn: fetchGetPreEntryList,
  showTotal: true,
  apiParams: {
    positionId: null,
    deptId: null,
    jobNumber: null,
    name: null,
    subStatus: null,
    positionProperty: null
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 64
    },
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'name',
      title: '姓名',
      align: 'center'
    },
    {
      key: 'dept.name',
      title: '部门',
      align: 'center'
    },
    {
      key: 'position.name',
      title: '职位',
      align: 'center'
    },
    {
      key: 'positionProperty',
      title: '岗位属性',
      align: 'center',
      render: (row: Api.Hrm.PreEntryRespVO) => <PositionPro positionProperty={row.positionProperty} />
    },
    {
      key: 'idNumber',
      title: '身份证号',
      align: 'center'
    },
    {
      key: 'status',
      title: '员工状态',
      align: 'center',
      render: (rowData: Api.Hrm.PreEntryRespVO) => (
        <div>
          <NTag type="success">{rowData.subStatus?.name}</NTag>
        </div>
      )
    },

    {
      key: 'createTime',
      title: '创建时间',
      align: 'center'
    },
    {
      key: 'reminderDateForClassOpening',
      title: '开班时间',
      align: 'center'
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 180,
      render: (rowData: Api.Hrm.PreEntryRespVO) => {
        const reviewBtnNameMap: Record<number, string> = {
          [PreEntryStatus.Reviewing]: '审核',
          [PreEntryStatus.PendingContracts]: '发起合同'
        };

        const reviewButtonLabel = reviewBtnNameMap[rowData.subStatusId] || '详情';

        return (
          <div class="flex flex-wrap gap-2">
            {roleBtn(reviewButtonLabel, ['hrm:employee:review'], 'info', () =>
              router.push({ path: '/hrm/roster/review', query: { id: rowData.id } })
            )}
            {[PreEntryStatus.WaitingForSign].includes(rowData.subStatusId) && rowData.contractId
              ? roleBtn('预览合同', ['hrm:contact:update'], 'success', () => handleProView(rowData))
              : null}

            {!rowData.inDingTalk
              ? roleBtn('入职邀请', ['hrm:employee:review'], 'success', () => handleInvitation(rowData.id))
              : null}

            {![PreEntryStatus.WaitingForContract].includes(rowData.subStatusId)
              ? roleBtn('放弃入职', ['hrm:onboarding:giveup'], 'info', () => handleGiveup(rowData))
              : null}
          </div>
        );
      }
    }
  ]
});
const { checkedRowKeys } = useTableOperate(data, getData);

async function handleInvitation(userIds: number | number[]) {
  renderModalBtn(
    InvitationForm,
    { useIds: Array.isArray(userIds) ? userIds : [userIds], type: Array.isArray(userIds) ? 'multi' : 'single' },
    {
      title: '入职邀请',
      style: {
        width: '50%'
      },
      func: getData
    }
  );
}

async function handleGiveup(rowData: Api.Hrm.PreEntryRespVO) {
  renderModalBtn(
    GiveupForm,
    { rowData },
    {
      title: '放弃入职',
      style: {
        width: '50%'
      },
      func: getData
    }
  );
}

async function handleProView(rowData: Api.Hrm.PreEntryRespVO) {
  renderModal(
    PreviewInfo,
    {
      data: getProxyUrl(`${hrmBaseUrl(`/contract/preview/${rowData.contractId}?token=${getToken()}`)}`),
      hideBtn: true
    },
    {
      title: '预览合同',
      style: {
        width: '80%'
      }
    }
  );
}

const { form, searchColumns } = useEmployeeSearchForm<Api.Hrm.SimpleEmployeeInfoSearch>({
  searchParams,
  resetSearchParams,
  updateSearchParams,
  getDataByPage,
  newColumns: [
    {
      title: '员工姓名',
      path: 'name'
    },
    {
      title: '员工职位',
      path: 'positionId',
      field: 'select-with-search',
      fieldProps: {
        apiFunc: getAllPositionOptions,
        selectedOptions: [],
        pageSize: 0,
        placeholder: '职位'
      }
    },
    {
      title: '状态',
      path: 'subStatus',
      field: 'select',
      fieldProps: {
        options: hrm_pre_entry_status
      }
    } as any,
    {
      title: '岗位属性',
      path: 'positionProperty',
      field: 'select',
      fieldProps: {
        options: hrm_position_property
      }
    },
    {
      title: '所属部门',
      path: 'deptId',
      field: 'dept-tree-select',
      fieldProps: {
        placeholder: '请选择部门'
      }
    }
  ]
});
</script>

<template>
  <div class="min-h-500px flex-col-stretch overflow-hidden lt-sm:overflow-auto">
    <ProCard class="mb-10px" :show-collapse="false" content-class="!pb-[0px]">
      <ProSearchForm :form="form" :columns="searchColumns" />
    </ProCard>
    <NCard :bordered="false" class="sm:flex-1-hidden card-wrapper" size="small" title="待入职">
      <template #header-extra>
        <NButton
          size="small"
          ghost
          type="primary"
          :disabled="checkedRowKeys.length === 0"
          class="mr-3"
          @click="handleInvitation(checkedRowKeys)"
        >
          <template #icon>
            <icon-ic-round-check class="text-icon" />
          </template>
          批量入职邀请
        </NButton>
        <TableHeaderOperation v-model:columns="columnChecks" :loading="loading" :show-add="false" @refresh="getData" />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        :loading="loading"
        :pagination="mobilePagination"
        :row-key="row => row.id"
        :flex-height="!appStore.isMobile"
        remote
        size="small"
        class="sm:h-full"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
