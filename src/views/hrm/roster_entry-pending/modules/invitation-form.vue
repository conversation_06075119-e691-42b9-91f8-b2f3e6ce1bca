<script setup lang="ts">
import { NSelect } from 'naive-ui';
import { ref } from 'vue';

import DateTimePicker from '@/components/common/date-time-picker.vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { fetchPutCompanyOptions } from '@/service/api/hrm/company-management';
import { fetchGetPreEntryDetail, fetchPutInviteToDingtalk } from '@/service/api/hrm/contract_info';
import { getFormatDictData } from '@/utils/async-functions';
import { calculateTrialEndDate } from '@/utils/date-format';

interface IProps {
  useIds: number[];
  type: 'single' | 'multi';
}
const props = defineProps<IProps>();
const { formRef, validate, restoreValidation } = useNaiveForm();
const companyOptions = ref<CommonType.Option<number>[]>([]);
const hrmProbationPeriodTypeOptions = ref<Api.System.FormattedOption[]>([]);
const hrmEmployeeTypeOptions = ref<Api.System.FormattedOption[]>([]);
const hrmPositionPropertyOptions = ref<Api.System.FormattedOption[]>([]);
const model = reactive<Api.Hrm.InviteToDingTalkReqVOData>({
  companyId: null,
  employeeType: null,
  entryDate: null,
  planRegularTime: null,
  probationPeriodType: null,
  reminderDateForClassOpening: null
});
const { defaultRequiredRule } = useFormRules();
type RuleKey = keyof Omit<Api.Hrm.InviteToDingTalkReqVOData, 'reminderDateForClassOpening' | 'reviewId'>;

const rules = ref<Record<RuleKey, App.Global.FormRule>>({
  companyId: defaultRequiredRule,
  employeeType: defaultRequiredRule,
  entryDate: defaultRequiredRule,
  planRegularTime: defaultRequiredRule,
  probationPeriodType: defaultRequiredRule
});

async function handleSubmit(reviewId: number | null) {
  try {
    await validate();
    const promiseList: Promise<Api.Common.ApiNull>[] = props.useIds.map(id =>
      fetchPutInviteToDingtalk(id, { ...model, reviewId: reviewId || null })
    );
    const result = await Promise.all(promiseList);
    if (result.some(item => item.error)) return false;
    window.$message?.success('邀请成功');
    return true;
  } catch (e) {
    console.log(e);
    return false;
  }
}

async function getContractDict() {
  const { data } = await getFormatDictData({
    keys: ['hrm_probation_period_type', 'hrm_employee_type', 'hrm_position_property'].join(',')
  });
  hrmProbationPeriodTypeOptions.value =
    data.hrm_probation_period_type.map(item => ({
      label: item.label,
      value: Number(item.value)
    })) || [];
  hrmEmployeeTypeOptions.value =
    data.hrm_employee_type.map(item => ({
      label: item.label,
      value: Number(item.value)
    })) || [];
  hrmPositionPropertyOptions.value =
    data.hrm_position_property.map(item => ({
      label: item.label,
      value: Number(item.value)
    })) || [];
}

async function handleGetCompanyOptions() {
  const { data, error } = await fetchPutCompanyOptions();
  if (error) return;
  companyOptions.value = data.map(item => ({
    label: item.name,
    value: item.id
  }));
}

function exportFormAlidate() {
  return formRef.value?.validate();
}

function disablePreviousDate(ts: number) {
  return ts < Date.now();
}

async function handleGetDetail() {
  const { data, error } = await fetchGetPreEntryDetail(props.useIds[0]);
  if (error) return;
  Object.assign(model, data);
}

function handleEntryDateChange(entryDate: string) {
  if (Number(model.probationPeriodType) === 8) return;
  if (entryDate && model.probationPeriodType) {
    model.planRegularTime = calculateTrialEndDate(entryDate, Number(model.probationPeriodType));
  }
}

defineExpose({ handleSubmit, exportFormAlidate });
onMounted(() => {
  getContractDict();
  handleGetCompanyOptions();
  restoreValidation();
  if (props.type === 'single') {
    handleGetDetail();
  }
});
</script>

<template>
  <NAlert type="warning" class="mb-10px">此功能是登记员工必要信息，同时会发送钉钉入职邀请。</NAlert>
  <NAlert type="warning" class="mb-10px">请使用此功能邀请员工进钉钉。</NAlert>
  <NForm ref="formRef" :model="model" :rules="rules">
    <FormItemDes label="公司主体" path="companyId" scene="邀请入职">
      <NSelect v-model:value="model.companyId" :options="companyOptions" placeholder="请选择公司主体" />
    </FormItemDes>
    <!--
 <NFormItem label="岗位属性" path="positionProperty">
      <NSelect
        v-model:value="model.positionProperty"
        :options="hrmPositionPropertyOptions"
        placeholder="请选择岗位属性"
      />
    </NFormItem>
-->
    <NFormItem label="员工类型" path="employeeType">
      <NSelect v-model:value="model.employeeType" :options="hrmEmployeeTypeOptions" placeholder="请选择员工类型" />
    </NFormItem>

    <NFormItem label="试用期类型" path="probationPeriodType">
      <NSelect
        v-model:value="model.probationPeriodType"
        clearable
        :options="hrmProbationPeriodTypeOptions"
        placeholder="请选择试用期类型"
      />
    </NFormItem>
    <NFormItem label="入职时间" path="entryDate">
      <DateTimePicker
        v-model:time="model.entryDate"
        label="入职时间"
        use-for="date"
        class="w-full"
        @update:time="handleEntryDateChange"
      />
    </NFormItem>
    <NFormItem label="计划转正日期" path="planRegularTime">
      <DateTimePicker
        v-model:time="model.planRegularTime"
        label="计划转正日期"
        use-for="date"
        class="w-full"
        :is-date-disabled="disablePreviousDate"
      />
    </NFormItem>
    <FormItemDes label="开班日期" path="reminderDateForClassOpening" scene="邀请入职">
      <DateTimePicker v-model:time="model.reminderDateForClassOpening" label="开班日期" use-for="date" class="w-full" />
    </FormItemDes>
  </NForm>
</template>

<style scoped></style>
