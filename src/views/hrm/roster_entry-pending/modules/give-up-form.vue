<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';

import { PreEntryStatus } from '@/enum';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { fetchPostBlackList } from '@/service/api/hrm/roster_black';
import { fetchGiveupPreEntry } from '@/service/api/hrm/roster_entry-pending';

type RuleKey = 'reason' | 'blockReason';
const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();
const rules = ref<Record<RuleKey, App.Global.FormRule>>({
  reason: defaultRequiredRule,
  blockReason: defaultRequiredRule
});
const blockOptions = [
  { label: '是', value: true },
  { label: '否', value: false }
];

const props = defineProps<{
  rowData: Api.Hrm.PreEntryRespVO;
}>();

const model = reactive<{
  employeeId: number;
  reason: string;
  block: boolean;
  idNumber: string;
  blockReason: string;
}>({
  employeeId: 0,
  reason: '',
  block: false,
  idNumber: '',
  blockReason: ''
});

async function handleSubmit() {
  try {
    await validate();
    const { error } = await fetchGiveupPreEntry(props.rowData.id, { reason: model.reason });
    if (error) {
      return false;
    }
    if (model.block) {
      const { error } = await fetchPostBlackList({
        idNumber: model.idNumber,
        name: props.rowData.name,
        reason: model.blockReason
      });
    }
    window.$message?.success('放弃入职成功');
    return true;
  } catch (e) {
    console.log(e);
    return false;
  }
}
defineExpose({
  handleSubmit
});
onMounted(() => {
  model.name = props.rowData.name;
  model.idNumber = props.rowData.idNumber;
  restoreValidation();
});
</script>

<template>
  <NForm ref="formRef" :model="model" :rules="rules">
    <NFormItem span="24 s:12 m:6" label="姓名" path="name" class="pr-24px">
      <NText>{{ model.name }}</NText>
    </NFormItem>
    <NFormItem span="24 s:12 m:6" label="放弃原因" path="reason" class="pr-24px">
      <NInput v-model:value="model.reason" clearable placeholder="请输入放弃原因" type="textarea" />
    </NFormItem>
    <!-- 待签署合同 -->
    <NAlert v-if="rowData.subStatusId === PreEntryStatus.WaitingForSign" type="warning" class="mb-5px">
      放弃入职，将同步撤回其待签署的电子签。
    </NAlert>
    <!-- 待发起合同 -->
    <NAlert v-if="rowData.subStatusId === PreEntryStatus.WaitingForContract" type="warning" class="mb-5px">
      签署合同后无法放弃入职。
    </NAlert>

    <NDivider class="!my-[5px]">拉黑补充信息</NDivider>
    <NFormItem span="24 s:12 m:6" label="是否拉黑" path="block" class="pr-24px">
      <NRadioGroup v-model:value="model.block" name="radiobuttongroup1">
        <NRadioButton v-for="(item, index) in blockOptions" :key="index" :value="item.value" :label="item.label" />
      </NRadioGroup>
    </NFormItem>
    <NFormItem v-if="model.block" span="24 s:12 m:6" label="身份证号" path="idNumber" class="pr-24px">
      <NInput v-model:value="model.idNumber" clearable placeholder="请输入身份证号" />
    </NFormItem>
    <NFormItem v-if="model.block" span="24 s:12 m:6" label="拉黑原因" path="blockReason" class="pr-24px">
      <NInput v-model:value="model.blockReason" clearable placeholder="请输入拉黑原因" type="textarea" />
    </NFormItem>
  </NForm>
</template>

<style scoped></style>
