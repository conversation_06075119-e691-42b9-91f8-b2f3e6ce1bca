<script lang="tsx" setup>
import { NButton, NDropdown, NTag } from 'naive-ui';
import { useRouter } from 'vue-router';

import PositionPro from '@/components/common/position-pro.vue';
import { useDict } from '@/hooks/business/useDict';
import { useTable } from '@/hooks/common/table';
import { useEmployeeSearchForm } from '@/hooks/hrm/useEmployeeSearchForm';
import { $t } from '@/locales';
import { fetchGetProbationList } from '@/service/api/hrm/roster_probation';
import { useAppStore } from '@/store/modules/app';

const appStore = useAppStore();
const router = useRouter();
const { hrm_probation_status, hrm_position_property } = useDict(['hrm_probation_status', 'hrm_position_property']);
const {
  columns,
  columnChecks,
  data,
  getData,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams,
  getDataByPage,
  updateSearchParams
} = useTable({
  apiFn: fetchGetProbationList,
  showTotal: true,
  apiParams: {
    positionId: null,
    deptId: null,
    jobNumber: null,
    name: null,
    subStatus: null,
    positionProperty: null
  },
  columns: () => [
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'name',
      title: '姓名',
      align: 'center'
    },
    {
      key: 'dept.name',
      title: '部门',
      align: 'center'
    },
    {
      key: 'position.name',
      title: '职位',
      align: 'center'
    },
    {
      key: 'jobNumber',
      title: '工号',
      align: 'center'
    },
    {
      key: 'subStatus',
      title: '员工状态',
      align: 'center',
      render: (row: Api.Hrm.ProbationRespVO) => (
        <div>
          <NTag type="info">{row.subStatus?.name}</NTag>
        </div>
      )
    },
    {
      key: 'positionProperty',
      title: '岗位属性',
      align: 'center',
      render: (row: Api.Hrm.ProbationRespVO) => <PositionPro positionProperty={row.positionProperty} />
    },
    {
      key: 'entryDate',
      title: '入职日期',
      align: 'center'
    },
    {
      key: 'trialPeriod',
      title: '试用期',
      align: 'center'
    },
    {
      key: 'planRegularDate',
      title: '计划转正日期',
      align: 'center'
    },
    {
      key: 'regularDate',
      title: '实际转正日期',
      align: 'center'
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 180,
      render: (rowData: Api.Hrm.ProbationRespVO) => {
        const options = optionsFilter(rowData);
        return (
          <div class="flex-center gap-8px">
            <NButton
              type="primary"
              ghost
              size="small"
              onClick={() => router.push({ path: '/hrm/roster/profile', query: { id: rowData.id } })}
            >
              详情
            </NButton>
            {options.length ? (
              <NDropdown
                trigger="hover"
                placement="right-start"
                options={options}
                onSelect={(key: string) => {
                  if (key === 'apply') {
                    window.open(rowData.dingTalkUrl);
                  } else if (key === 'contract') {
                    router.push({ path: '/hrm/roster/resign-contract', query: { id: rowData.id } });
                  }
                }}
              >
                <NButton size="small" type="primary" ghost>
                  <icon-ic-outline-more-horiz />
                </NButton>
              </NDropdown>
            ) : null}
          </div>
        );
      }
    }
  ]
});

function optionsFilter(rowData: Api.Hrm.ProbationRespVO) {
  const options = [];
  if (rowData.dingTalkUrl) {
    options.push({ label: '钉钉审批详情', key: 'apply' });
  }
  options.push({ label: '发起签约', key: 'contract', auth: ['hrm:contact:update'] });
  return options;
}

const { form, searchColumns } = useEmployeeSearchForm<Api.Hrm.SimpleEmployeeInfoSearch>({
  searchParams,
  resetSearchParams,
  updateSearchParams,
  getDataByPage,
  shiftIndex: 2,
  extendColumns: [
    {
      title: '状态',
      path: 'subStatus',
      field: 'select',
      fieldProps: {
        options: hrm_probation_status
      }
    },
    {
      title: '岗位属性',
      path: 'positionProperty',
      field: 'select',
      fieldProps: {
        options: hrm_position_property
      }
    }
  ] as any
});
</script>

<template>
  <div class="min-h-500px flex-col-stretch overflow-hidden lt-sm:overflow-auto">
    <ProCard class="mb-10px" :show-collapse="false" content-class="!pb-[0px]">
      <ProSearchForm :form="form" :columns="searchColumns" />
    </ProCard>
    <NCard :bordered="false" class="sm:flex-1-hidden card-wrapper" size="small" title="试用期">
      <template #header-extra>
        <TableHeaderOperation v-model:columns="columnChecks" :loading="loading" :show-add="false" @refresh="getData" />
      </template>
      <NDataTable
        :columns="columns"
        :data="data"
        :loading="loading"
        :pagination="mobilePagination"
        :row-key="row => row.id"
        :flex-height="!appStore.isMobile"
        remote
        size="small"
        class="sm:h-full"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
