<script setup lang="tsx">
import dayjs from 'dayjs';
import { NButton, NTag } from 'naive-ui';

import { useDict } from '@/hooks/business/useDict';
import { useTable } from '@/hooks/common/table';
import { useTreeTable } from '@/hooks/common/tree-table';
import { $t } from '@/locales';
import { exportEmployeeRoster, fetchGetEmployeeList } from '@/service/api/hrm/roster_user';
import { fetchGetDeptList } from '@/service/api/kip/department';
import { useAppStore } from '@/store/modules/app';
import download from '@/utils/download';

const appStore = useAppStore();
const searchParams = ref<
  Api.Hrm.SimpleEmployeeInfoSearch & {
    entryDate: [string, string] | null;
    deptIds: CommonType.IdType[];
    statusList: number[];
  }
>({
  deptIds: [],
  statusList: [],
  entryDate: [
    dayjs().subtract(7, 'day').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
    dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
  ]
});
const exportLoading = ref(false);
// 用于日期选择器的值，需要是number类型
const datePickerValue = ref<[number, number] | null>([
  dayjs().subtract(7, 'day').startOf('day').valueOf(),
  dayjs().endOf('day').valueOf()
]);
const { hrm_employee_status } = useDict(['hrm_employee_status']);
const { data, expandedRowKeys } = useTreeTable({
  apiFn: fetchGetDeptList,
  idField: 'id',
  columns: () => []
});

const {
  columns: employeeColumns,
  data: employeeData,
  getData: getEmployeeData,
  loading,
  mobilePagination,
  updateSearchParams: updateEmployeeSearchParams
} = useTable({
  apiFn: fetchGetEmployeeList,
  showTotal: true,
  apiParams: {
    deptIds: null,
    statusList: null,
    entryDate: [
      dayjs().subtract(7, 'day').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
      dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
    ]
  },
  columns: () => [
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'name',
      title: '姓名',
      align: 'center'
    },
    {
      key: 'dept',
      title: '部门',
      align: 'center',
      render: (rowData: Api.Hrm.SimpleEmployeeInfo) => rowData.dept?.name || '-'
    },
    {
      key: 'position',
      title: '职位',
      align: 'center',
      render: (rowData: Api.Hrm.SimpleEmployeeInfo) => rowData.position?.name || '-'
    },
    {
      key: 'entryDate',
      title: '入职时间',
      align: 'center'
    },
    {
      key: 'status',
      title: '员工状态',
      align: 'center',
      render: (rowData: Api.Hrm.SimpleEmployeeInfo) => (
        <div>
          <NTag type="info">
            {rowData.status?.name}({rowData.status?.data?.name})
          </NTag>
        </div>
      )
    },
    {
      key: 'phoneNumber',
      title: '手机号',
      align: 'center'
    },
    {
      key: 'jobNumber',
      title: '工号',
      align: 'center'
    }
  ]
});

function resetEmployeeSearch() {
  searchParams.value = {
    deptIds: [],
    statusList: [],
    entryDate: [
      dayjs().subtract(7, 'day').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
      dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
    ]
  };
  // 重置日期选择器的值
  datePickerValue.value = [dayjs().subtract(7, 'day').startOf('day').valueOf(), dayjs().endOf('day').valueOf()];
  updateEmployeeSearchParams({
    ...searchParams.value
  });
  // 清空保存的搜索参数
  getEmployeeData();
}

function searchEmployee() {
  const params = { ...searchParams.value };

  // 同步日期选择器的值到搜索参数
  if (datePickerValue.value && Array.isArray(datePickerValue.value)) {
    const [start, end] = datePickerValue.value;
    params.entryDate = [
      dayjs(start).startOf('day').format('YYYY-MM-DD HH:mm:ss'),
      dayjs(end).endOf('day').format('YYYY-MM-DD HH:mm:ss')
    ];
  } else {
    params.entryDate = null;
  }

  updateEmployeeSearchParams(params);
  getEmployeeData();
}

async function exportEmployeeData() {
  if (employeeData.value.length === 0) {
    window.$message?.error('暂无可导出数据');
    return;
  }
  exportLoading.value = true;
  try {
    const params = { ...searchParams.value };
    if (datePickerValue.value && Array.isArray(datePickerValue.value)) {
      const [start, end] = datePickerValue.value;
      params.entryDate = [
        dayjs(start).startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        dayjs(end).endOf('day').format('YYYY-MM-DD HH:mm:ss')
      ];
    } else {
      params.entryDate = null;
    }
    const { data: blob } = await exportEmployeeRoster(params);
    download.excel(blob!, `员工花名册-${dayjs().format('YYYYMMDD-HHmmss')}.xlsx`);
  } catch (error) {
    console.error(error);
  } finally {
    exportLoading.value = false;
  }
}
</script>

<template>
  <TableSiderLayout default-expanded>
    <template #header>筛选条件</template>
    <template #header-extra>
      <div class="flex gap-6px">
        <NButton size="small" type="primary" @click="searchEmployee">搜索</NButton>
        <NButton size="small" @click="resetEmployeeSearch">重置</NButton>
      </div>
    </template>
    <template #sider>
      <NForm size="small">
        <NFormItem label="员工状态">
          <NSelect
            v-model:value="searchParams.statusList"
            size="small"
            multiple
            placeholder="请选择员工状态"
            :options="hrm_employee_status"
          />
        </NFormItem>
        <NFormItem label="入职时间">
          <NDatePicker
            v-model:value="datePickerValue"
            type="daterange"
            size="small"
            clearable
            placeholder="请选择入职时间"
          />
        </NFormItem>
        <FormItemDes label="部门" path="deptIds" scene="花名册导出">
          <NTree
            v-model:checked-keys="searchParams.deptIds"
            v-model:default-expanded-keys="expandedRowKeys"
            block-line
            :data="data"
            checkable
            cascade
            label-field="name"
            key-field="id"
          />
        </FormItemDes>
      </NForm>
    </template>
    <div class="h-full flex-col">
      <div class="h-full min-h-500px flex-col-stretch overflow-hidden lt-sm:overflow-auto">
        <NCard :bordered="false" class="h-full sm:flex-1-hidden card-wrapper" size="small" title="花名册">
          <template #header-extra>
            <NPopconfirm placement="bottom-start" @positive-click="exportEmployeeData">
              <template #trigger>
                <NButton type="primary" size="small" tooltip="导出" :loading="exportLoading">导出</NButton>
              </template>
              是否确定导出？
            </NPopconfirm>
          </template>
          <NDataTable
            :columns="employeeColumns"
            :data="employeeData"
            :loading="loading"
            :pagination="mobilePagination"
            :row-key="row => row.id"
            :flex-height="!appStore.isMobile"
            remote
            size="small"
            class="sm:h-full"
          />
        </NCard>
      </div>
    </div>
  </TableSiderLayout>
</template>

<style scoped>
:deep(.n-form-item-feedback-wrapper) {
  display: none !important;
}
:deep(.n-form-item) {
  margin-bottom: 10px !important;
}
</style>
