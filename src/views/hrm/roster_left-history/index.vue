<script lang="tsx" setup>
import dayjs from 'dayjs';
import { NTag } from 'naive-ui';
import type { ProSearchFormColumns } from 'pro-naive-ui';
import { createProSearchForm } from 'pro-naive-ui';

import { useDict } from '@/hooks/business/useDict';
import { useTable } from '@/hooks/common/table';
import { $t } from '@/locales';
import { fetchGetResigningHistoryList } from '@/service/api/hrm/roster_left';
import { useAppStore } from '@/store/modules/app';

const appStore = useAppStore();
const { hrm_position_property } = useDict(['hrm_position_property']);
const props = defineProps<{
  name: string;
}>();
const searchColumns: ProSearchFormColumns<Api.Hrm.ResignedHistoryReqVOData> = [
  {
    title: '员工姓名',
    path: 'name'
  },
  {
    title: '离职日期',
    path: 'leaveDate',
    field: 'date-range'
  },
  {
    title: '岗位属性',
    path: 'positionProperty',
    field: 'select',
    fieldProps: () => ({
      options: hrm_position_property.value
    })
  }
];
const {
  columns,
  columnChecks,
  data,
  getData,
  loading,
  mobilePagination,
  searchParams,
  getDataByPage,
  updateSearchParams
} = useTable({
  apiFn: fetchGetResigningHistoryList,
  showTotal: true,
  apiParams: {
    pageNo: 1,
    pageSize: 10,
    name: props.name || null,
    leaveDate: null,
    positionProperty: null
  },
  columns: () => [
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'name',
      title: '员工姓名',
      align: 'center',
      minWidth: 100
    },
    {
      key: 'positionProperty',
      title: '岗位属性',
      align: 'center',
      render: (row: Api.Hrm.ResignationRespVO) => <PositionPro positionProperty={row.positionProperty} />
    },
    {
      key: 'deptList',
      title: '离职时部门',
      align: 'center',
      render: (row: Api.Hrm.ResignationRespVO) => {
        return row.deptList?.map(dept => dept.deptName).join(',');
      },
      minWidth: 100
    },
    {
      key: 'mainDeptName',
      title: '主要部门',
      align: 'center',
      minWidth: 100
    },
    {
      key: 'leaveDate',
      title: '离职日期',
      align: 'center',
      minWidth: 100
    },
    {
      key: 'resignationReason',
      title: '离职原因',
      align: 'center',
      ellipsis: {
        tooltip: true
      },
      minWidth: 100
    },
    {
      key: 'reasonMemo',
      title: '离职原因备注',
      align: 'center',
      ellipsis: {
        tooltip: {
          style: {
            maxWidth: '500px',
            whiteSpace: 'pre-line',
            maxHeight: '300px',
            overflow: 'auto'
          }
        }
      },
      width: 300
    },

    {
      key: 'voluntaryResignated',
      title: '是否主动离职',
      align: 'center',
      minWidth: 100,
      render: (row: Api.Hrm.ResignationHistoryRespVO) => {
        return row.voluntaryResignated ? <NTag type="success">是</NTag> : <NTag type="error">否</NTag>;
      }
    }
  ]
});

const form = createProSearchForm({
  defaultCollapsed: true,
  initialValues: searchParams,
  onReset: () => {
    updateSearchParams({
      leaveDate: null,
      name: props.name || null,
      positionProperty: null
    });
    getDataByPage();
  },
  onSubmit: () => {
    const formValues = form.fieldsValue.value;
    const [start, end] = formValues.leaveDate || [];

    updateSearchParams({
      ...formValues,
      leaveDate: formValues.leaveDate
        ? [
            dayjs(start).startOf('day').format('YYYY-MM-DD HH:mm:ss'),
            dayjs(end).endOf('day').format('YYYY-MM-DD HH:mm:ss')
          ]
        : null
    });

    getDataByPage();
  }
});
</script>

<template>
  <div class="min-h-500px flex-col-stretch flex-1 overflow-hidden lt-sm:overflow-auto">
    <ProCard class="mb-10px" :show-collapse="false" content-class="!pb-[0px]">
      <ProSearchForm :form="form" :columns="searchColumns" />
    </ProCard>
    <NCard :bordered="false" class="sm:flex-1-hidden card-wrapper" size="small" title="已离职">
      <template #header-extra>
        <TableHeaderOperation v-model:columns="columnChecks" :loading="loading" :show-add="false" @refresh="getData" />
      </template>
      <NDataTable
        :columns="columns"
        :data="data"
        :loading="loading"
        :pagination="mobilePagination"
        :row-key="row => row.id"
        :flex-height="!appStore.isMobile"
        remote
        size="small"
        :scroll-x="1480"
        class="sm:h-full"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
