<script setup lang="tsx">
import { type DataTableColumns, NButton } from 'naive-ui';
import { storeToRefs } from 'pinia';

import ContractForm from './modules/contract-form.vue';

import { renderModal } from '@/components/re-modal';
import { roleBtn } from '@/directives/permission/permi-btn';
import { FileStatus } from '@/enum';
import { $t } from '@/locales';
import { fetchPutContactPreview } from '@/service/api/hrm/contract_info';
import { hrmBaseUrl } from '@/service/request';
import { getToken } from '@/store/modules/auth/shared';
import { useRosterStore } from '@/store/modules/roster';
import { getProxyUrl } from '@/utils/get-file-url';
import PreviewInfo from '@/views/hrm/roster_review/modules/preview-info.vue';

const useRoster = useRosterStore();
const { contractHistory } = storeToRefs(useRoster);
const route = useRoute();
const columns = ref<DataTableColumns<Api.Hrm.SimpleContractRespVO>>([
  {
    title: '业务分类',
    key: 'categoryName',
    align: 'center'
  },
  {
    title: '公司主体',
    key: 'companyName',
    align: 'center'
  },
  {
    title: '合同状态',
    key: 'status.name',
    align: 'center'
  },
  {
    title: '合同创建时间',
    key: 'createTime',
    align: 'center'
  },
  {
    title: $t('common.action'),
    key: 'action',
    align: 'center',
    width: 200,
    render: (row: Api.Hrm.SimpleContractRespVO) => {
      return (
        <div class="flex items-center gap-2">
          {roleBtn('预览合同', ['hrm:contact:update'], 'primary', () => handleProView(row))}
          {row.statusCode === FileStatus.Draft && (
            <NButton type="success" size="small" ghost onClick={() => handleSubmit(row)}>
              发送合同
            </NButton>
          )}
        </div>
      );
    }
  }
]);

async function handleProView(row: Api.Hrm.SimpleContractRespVO) {
  renderModal(
    PreviewInfo,
    {
      data: getProxyUrl(`${hrmBaseUrl(`/contract/preview/${row.contractId}?token=${getToken()}`)}`),
      hideBtn: true,
      bizTypeLabel: row.categoryName,
      companyLabel: row.companyName
    },
    {
      title: '预览合同',
      style: {
        width: '80%'
      }
    }
  );
}

async function handleSubmit(row: Api.Hrm.SimpleContractRespVO) {
  const d = window.$dialog?.info({
    title: '系统提示',
    maskClosable: false,
    content: () =>
      h('div', [
        '请您确认以下关键信息是否准确：',
        h('br'),
        '合同主体：',
        h(
          'span',
          {
            style: {
              fontWeight: 'bold',
              fontSize: '16px',
              color: '#333',
              marginLeft: '4px'
            }
          },
          row.companyName
        ),
        h('br'),
        '业务分类：',
        h(
          'span',
          {
            style: {
              fontWeight: 'bold',
              fontSize: '16px',
              color: '#333',
              marginLeft: '4px'
            }
          },
          row.categoryName
        ),
        h('br'),
        h('br'),
        '上述信息将直接影响合同审批流程，如填写错误将导致退回并延误整体进度。请务必核对无误后再提交。'
      ]),
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        d.loading = true;
        const { error } = await fetchPutContactPreview(row.contractId, Number(useRoster.userId));
        if (error) return;
        setTimeout(() => {
          useRoster.handleGetContractHistory(useRoster.userId as string);
        }, 500);
        window.$message?.success('提交成功');
        window.$modal?.destroyAll();
      } catch (e) {
        console.log(e);
      } finally {
        d.loading = false;
      }
    }
  });
}
onMounted(() => {
  useRoster.userId = route.query.id as string;
  useRoster.getDictData();
  useRoster.handleGetHrmEducationExperience(useRoster.userId);
  useRoster.handleGetHrmBasicInfo(useRoster.userId);
  useRoster.handleGetContractHistory(useRoster.userId);
});
</script>

<template>
  <div>
    <ContractForm>
      <template #middle>
        <NCard title="历史合同" content-class="!pb-2" class="my-2" header-class="!p-3">
          <NDataTable :columns="columns" :data="contractHistory" max-height="280" />
        </NCard>
      </template>
    </ContractForm>
  </div>
</template>
