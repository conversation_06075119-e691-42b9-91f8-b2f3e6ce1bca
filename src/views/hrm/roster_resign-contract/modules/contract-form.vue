<script setup lang="ts">
import { useTemplateRef } from 'vue';

import { useRosterStore } from '@/store/modules/roster';
import ContractInfoForm from '@/views/hrm/roster_review/modules/contract-info-form.vue';
import { useContractActions } from '@/views/hrm/roster_review/modules/useContractActions';

interface IProps {
  isOnJob?: boolean;
  isLaunch?: boolean;
}

defineProps<IProps>();
const useRoster = useRosterStore();
const contractFormRef = useTemplateRef<InstanceType<typeof ContractInfoForm>>('contractFormRef');
const hasContractData = ref(false);
const { previewContract, getValidatedContractData } = useContractActions();

// 处理合同选择完成事件
function handleContractSelected(data: any) {
  hasContractData.value = data.formModel?.modelList?.length > 0;
}

// 预览合同
async function handlePreview() {
  try {
    await previewContract(contractFormRef.value, true);
    useRoster.handleGetContractHistory(useRoster.userId as string);
  } catch (error) {
    // 错误已在 useContractActions 中处理
  }
}

// 暴露方法给外部调用
function getContractData() {
  return contractFormRef.value?.getContractData();
}

defineExpose({
  topFormValidate: () => contractFormRef.value?.topFormValidate(),
  formRefValidate: () => contractFormRef.value?.formRefValidate(),
  getContractData
});
const router = useRouter();

function handleBack() {
  router.back();
}
</script>

<template>
  <div>
    <NButton type="primary" ghost size="small" class="mb-4" @click="handleBack">
      <icon-ic-round-arrow-back />
    </NButton>
    <ContractInfoForm
      ref="contractFormRef"
      :is-on-job="isOnJob"
      :is-launch="isLaunch"
      @contract-selected="handleContractSelected"
    >
      <template #middle>
        <slot name="middle" />
      </template>
    </ContractInfoForm>

    <!-- 按钮区域 - 只在有合同数据且不是批量发起时显示 -->
    <div v-if="hasContractData && !isLaunch" v-hasPermi="['hrm:contact:update']" class="mt-4 flex justify-end gap-3">
      <ReButton type="info" @click="handlePreview">预览合同</ReButton>
    </div>
  </div>
</template>

<style scoped></style>
