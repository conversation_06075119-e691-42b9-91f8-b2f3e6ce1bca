<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';

import { EmployeeStatus, EmployeeSubStatus } from '@/enum';
import { getEmployeeStatusCount } from '@/service/api/hrm/roster_user';

defineOptions({
  name: 'UserSearch'
});

const router = useRouter();
const items = ref<
  {
    id: number;
    status: number | null;
    subStatus: number | null;
    label: string;
    count: number;
    tooltip: string;
    path: string;
  }[]
>([
  {
    id: 1,
    status: EmployeeStatus.OnJob,
    subStatus: null,
    label: '在职',
    count: 0,
    tooltip: '在职：0人',
    path: '/hrm/roster/on-job'
  },
  {
    id: 2,
    status: EmployeeStatus.EntryPending,
    subStatus: null,
    label: '待入职',
    count: 0,
    tooltip: '待入职：0人',
    path: '/hrm/roster/entry-pending'
  },
  {
    id: 3,
    status: EmployeeStatus.Probation,
    subStatus: null,
    label: '试用期',
    count: 0,
    tooltip: '试用期：0人',
    path: '/hrm/roster/probation'
  },
  {
    id: 4,
    status: EmployeeStatus.OnJob,
    subStatus: EmployeeSubStatus.Transfer,
    label: '调岗',
    count: 0,
    tooltip: '调岗：0人',
    path: '/hrm/roster/transfer'
  },
  {
    id: 5,
    status: EmployeeStatus.Left,
    subStatus: null,
    label: '离职',
    count: 0,
    tooltip: '离职：0人',
    path: '/hrm/roster/left'
  },
  {
    id: 6,
    status: EmployeeStatus.Left,
    subStatus: null,
    label: '异常',
    count: 0,
    tooltip: '异常：0人',
    path: '/hrm/roster/abnormal'
  }
]);

function updateTooltips(updates: Record<string, number>) {
  items.value.forEach(item => {
    if (updates[item.label] !== undefined) {
      item.count = updates[item.label] || 0;
      item.tooltip = `${item.label}：${item.count ?? 0}人`;
    }
  });
}

function select(path: string) {
  router.push({ path });
}

async function handleGetEmployeeStatusCount() {
  try {
    const { data } = await getEmployeeStatusCount();
    if (data) {
      updateTooltips(data);
    }
  } catch (e) {
    console.log(e);
  }
}
onMounted(() => {
  handleGetEmployeeStatusCount();
});
</script>

<template>
  <NFlex gap="16px" align="stretch" class="p-2">
    <div v-for="item in items" :key="item.id" class="h-[100px] flex-1 cursor-pointer" @click="select(item.path)">
      <div class="h-full w-full flex items-center justify-center rounded-2 p-2">
        <NTooltip trigger="hover">
          <template #trigger>
            <div class="text-center">
              <div>{{ item.label }}</div>
              <div class="text-2xl">{{ item.count }}</div>
            </div>
          </template>
          <span>{{ item.tooltip }}</span>
        </NTooltip>
      </div>
    </div>
  </NFlex>
</template>

<style scoped>
:deep(.n-collapse-item__content-inner) {
  padding-top: 12px !important;
}
</style>
