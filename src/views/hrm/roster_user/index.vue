<script lang="tsx" setup>
import { NTag } from 'naive-ui';
import { useRouter } from 'vue-router';

import TopNum from './modules/top-num.vue';

import { infoRoleBtn } from '@/directives/permission/permi-btn';
import { useDict } from '@/hooks/business/useDict';
import { useTable } from '@/hooks/common/table';
import { useEmployeeSearchForm } from '@/hooks/hrm/useEmployeeSearchForm';
import { $t } from '@/locales';
import { fetchGetEmployeeList } from '@/service/api/hrm/roster_user';
import { useAppStore } from '@/store/modules/app';

const appStore = useAppStore();
const router = useRouter();
const { hrm_employee_status } = useDict(['hrm_employee_status']);

const {
  columns,
  columnChecks,
  data,
  getData,
  loading,
  mobilePagination,
  resetSearchParams,
  getDataByPage,
  searchParams,
  updateSearchParams
} = useTable({
  apiFn: fetchGetEmployeeList,
  showTotal: true,
  apiParams: {
    positionId: null,
    deptId: null,
    jobNumber: null,
    name: null,
    status: null,
    subStatus: null
  },
  columns: () => [
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'name',
      title: '姓名',
      align: 'center'
    },
    {
      key: 'dept.name',
      title: '部门',
      align: 'center'
    },
    {
      key: 'position.name',
      title: '职位',
      align: 'center'
    },
    {
      key: 'entryDate',
      title: '入职时间',
      align: 'center'
    },
    {
      key: 'status.name',
      title: '员工状态',
      align: 'center',
      render: (rowData: Api.Hrm.SimpleEmployeeInfo) => (
        <div>
          <NTag type="info">
            {rowData.status?.name}({rowData.status?.data?.name})
          </NTag>
        </div>
      )
    },
    {
      key: 'phoneNumber',
      title: '手机号',
      align: 'center'
    },
    {
      key: 'jobNumber',
      title: '工号',
      align: 'center'
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 180,
      render: (rowData: Api.Hrm.SimpleEmployeeInfo) => (
        <div class="flex-center gap-8px">
          {infoRoleBtn(['hrm:employee:query'], 'primary', () =>
            router.push({ path: '/hrm/roster/profile', query: { id: rowData.id } })
          )}
        </div>
      )
    }
  ]
});

const { form, searchColumns } = useEmployeeSearchForm<Api.Hrm.SimpleEmployeeInfoSearch>({
  searchParams,
  resetSearchParams,
  updateSearchParams,
  getDataByPage,
  shiftIndex: 2,
  extendColumns: [
    {
      title: '状态',
      path: 'status',
      field: 'select',
      fieldProps: {
        options: hrm_employee_status
      }
    }
  ] as any
});
</script>

<template>
  <div class="min-h-500px flex-col-stretch overflow-hidden lt-sm:overflow-auto">
    <ProCard class="mb-10px" :show-collapse="false" content-class="!pb-[0px]">
      <ProSearchForm :form="form" :columns="searchColumns" />
      <TopNum />
    </ProCard>
    <NCard :bordered="false" class="sm:flex-1-hidden card-wrapper" size="small" title="花名册">
      <template #header-extra>
        <TableHeaderOperation v-model:columns="columnChecks" :loading="loading" :show-add="false" @refresh="getData" />
      </template>
      <NDataTable
        :columns="columns"
        :data="data"
        :loading="loading"
        :pagination="mobilePagination"
        :row-key="row => row.id"
        :flex-height="!appStore.isMobile"
        remote
        size="small"
        class="sm:h-full"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
