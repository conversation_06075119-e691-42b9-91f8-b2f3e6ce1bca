<script setup lang="ts">
import { NDescriptions, NDescriptionsItem, NEmpty } from 'naive-ui';
import { storeToRefs } from 'pinia';

import { useRosterStore } from '@/store/modules/roster';

const useRoster = useRosterStore();
const { workInfo } = storeToRefs(useRoster);
</script>

<template>
  <div v-if="!workInfo.details.length" class="h-[200px] flex items-center justify-center">
    <NEmpty description="无工作经历" />
  </div>
  <div v-else>
    <NDescriptions bordered label-placement="left" class="mb-16px">
      <NDescriptionsItem label="工作年限">
        <div>{{ workInfo.year }}</div>
      </NDescriptionsItem>
      <NDescriptionsItem label="专业能力">
        <div>{{ workInfo.description }}</div>
      </NDescriptionsItem>
    </NDescriptions>
    <div
      v-for="(item, index) in workInfo.details"
      :key="index"
      class="mb-16px border border-gray-200 rounded-lg bg-white p-16px shadow-sm"
    >
      <NDescriptions bordered label-placement="left" :title="item.companyName">
        <NDescriptionsItem label="起止日期">
          <div>{{ item.time }}</div>
        </NDescriptionsItem>
        <NDescriptionsItem label="公司名称">
          <div>{{ item.companyName }}</div>
        </NDescriptionsItem>
        <NDescriptionsItem label="项目名称">
          <div>{{ item.projectName }}</div>
        </NDescriptionsItem>
        <NDescriptionsItem label="项目规模">
          <div>{{ item.projectScale }}</div>
        </NDescriptionsItem>
        <NDescriptionsItem label="担任职位">
          <div>{{ item.position }}</div>
        </NDescriptionsItem>
        <NDescriptionsItem label="工作业绩/奖项/项目成效">
          <div>{{ item.performanceSummary }}</div>
        </NDescriptionsItem>
      </NDescriptions>
    </div>
  </div>
</template>

<style scoped></style>
