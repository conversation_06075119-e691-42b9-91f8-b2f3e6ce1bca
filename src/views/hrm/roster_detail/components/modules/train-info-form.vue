<script setup lang="ts">
import { NDescriptions, NDescriptionsItem, NEmpty } from 'naive-ui';
import { storeToRefs } from 'pinia';

import { useRosterStore } from '@/store/modules/roster';

const useRoster = useRosterStore();
const { trainInfo } = storeToRefs(useRoster);
</script>

<template>
  <div v-if="!trainInfo.length" class="h-[200px] flex items-center justify-center">
    <NEmpty description="无培训经历" />
  </div>
  <div v-else>
    <div
      v-for="(item, index) in trainInfo"
      :key="index"
      class="mb-16px border border-gray-200 rounded-lg bg-white p-16px shadow-sm"
    >
      <NDescriptions bordered label-placement="left" :title="item.certificateTypeText">
        <NDescriptionsItem label="证书类型">
          <div>{{ item.certificateTypeText }}</div>
        </NDescriptionsItem>
        <NDescriptionsItem v-if="item.certificateType <= 3" label="证书等级">
          <div>{{ item.certificateLevelText }}</div>
        </NDescriptionsItem>
        <NDescriptionsItem label="证书名称">
          <div>{{ item.certificateName }}</div>
        </NDescriptionsItem>
        <NDescriptionsItem v-if="item.certificateTime" label="证书获取时间">
          <div>{{ item.certificateTime }}</div>
        </NDescriptionsItem>
        <NDescriptionsItem label="证书照片">
          <ImageDocuments v-if="item.certificateFileId" :file-id="item.certificateFileId" />
        </NDescriptionsItem>
        <NDescriptionsItem v-if="![1, 2, 3].includes(item.certificateType)" label="课程名称">
          <div>{{ item.trainName }}</div>
        </NDescriptionsItem>
        <NDescriptionsItem v-if="![1, 2, 3].includes(item.certificateType)" label="课程时间">
          <div>{{ item.trainTime }}</div>
        </NDescriptionsItem>
      </NDescriptions>
    </div>
  </div>
</template>

<style scoped></style>
