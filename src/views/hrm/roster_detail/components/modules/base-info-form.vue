<script setup lang="ts">
import { storeToRefs } from 'pinia';

import { useRosterStore } from '@/store/modules/roster';

const useRoster = useRosterStore();
const { baseInfo } = storeToRefs(useRoster);
</script>

<template>
  <NDescriptions bordered label-placement="left">
    <NDescriptionsItem>
      <template #label>姓名</template>
      <div>{{ baseInfo.name }}</div>
    </NDescriptionsItem>
    <NDescriptionsItem>
      <template #label>性别</template>
      <div>{{ baseInfo.genderText }}</div>
    </NDescriptionsItem>
    <NDescriptionsItem>
      <template #label>民族</template>
      <div>{{ baseInfo.ethnicityText }}</div>
    </NDescriptionsItem>
    <NDescriptionsItem>
      <template #label>身份证号</template>
      <div>{{ baseInfo.idNumber }}</div>
    </NDescriptionsItem>
    <NDescriptionsItem>
      <template #label>出生日期</template>
      <div>{{ baseInfo.birthday }}</div>
    </NDescriptionsItem>
    <NDescriptionsItem>
      <template #label>手机号</template>
      <div>{{ baseInfo.phoneNumber }}</div>
    </NDescriptionsItem>
    <NDescriptionsItem>
      <template #label>邮箱</template>
      <div>{{ baseInfo.email }}</div>
    </NDescriptionsItem>
    <NDescriptionsItem>
      <template #label>婚姻状况</template>
      <div>{{ baseInfo.maritalStatusText }}</div>
    </NDescriptionsItem>
    <NDescriptionsItem>
      <template #label>工服尺码</template>
      <div>{{ baseInfo.workClothesSizeText }}</div>
    </NDescriptionsItem>
    <NDescriptionsItem>
      <template #label>现居住地址</template>
      <div>{{ baseInfo.currentResidence }}</div>
    </NDescriptionsItem>
    <NDescriptionsItem>
      <template #label>政治面貌</template>
      <div>{{ baseInfo.politicalStatusText }}</div>
    </NDescriptionsItem>
    <NDescriptionsItem>
      <template #label>特长爱好</template>
      <div>{{ baseInfo.hobbies }}</div>
    </NDescriptionsItem>
    <NDescriptionsItem>
      <template #label>住址</template>
      <div class="w-[140px]">{{ baseInfo.householdRegistration }}</div>
    </NDescriptionsItem>
    <NDescriptionsItem>
      <template #label>身份证(人像面)</template>
      <ImageDocuments v-if="baseInfo.frontIdCardId" :file-id="baseInfo.frontIdCardId" />
    </NDescriptionsItem>
    <NDescriptionsItem>
      <template #label>身份证(国徽面)</template>
      <ImageDocuments v-if="baseInfo.frontIdCardId" :file-id="baseInfo.backIdCardId" />
    </NDescriptionsItem>
  </NDescriptions>
</template>

<style scoped></style>
