<script setup lang="ts">
import { useRosterStore } from '@/store/modules/roster';

const useRoster = useRosterStore();

function scrollToNAnchor(href) {
  const targetId = href.replace(/^#/, '');
  const el = document.getElementById(targetId);
  if (el) {
    el.scrollIntoView({ behavior: 'smooth' });
  }
}
</script>

<template>
  <NAnchor affix :trigger-top="24" :top="88" style="z-index: 1" :bound="24" :scroll-to="scrollToNAnchor">
    <NAnchorLink v-for="item in useRoster.sections" :key="item.id" :title="item.title" :href="`#section-${item.id}`" />
  </NAnchor>
</template>
