<script setup lang="ts">
import { NDescriptions, NDescriptionsItem, NEmpty } from 'naive-ui';
import { storeToRefs } from 'pinia';

import { useRosterStore } from '@/store/modules/roster';

const useRoster = useRosterStore();
const { familyInfo } = storeToRefs(useRoster);
</script>

<template>
  <div>
    <div v-if="!familyInfo.details.length" class="h-[200px] flex items-center justify-center">
      <NEmpty description="无家庭信息" />
    </div>
    <div v-else>
      <NDescriptions bordered label-placement="left" class="mb-16px">
        <NDescriptionsItem label="紧急联系人">
          <div>{{ familyInfo.emergencyContact }}</div>
        </NDescriptionsItem>
        <NDescriptionsItem label="紧急联系方式">
          <div>{{ familyInfo.emergencyContactPhone }}</div>
        </NDescriptionsItem>
        <NDescriptionsItem label="是否有亲友在公司任职">
          <div>{{ familyInfo.hasFamilyOrFriendInCompany ? '有' : '无' }}</div>
        </NDescriptionsItem>
        <NDescriptionsItem v-if="familyInfo.hasFamilyOrFriendInCompany" label="亲友姓名">
          <div>{{ familyInfo.familyOrFriendName }}</div>
        </NDescriptionsItem>
        <NDescriptionsItem v-if="familyInfo.hasFamilyOrFriendInCompany" label="亲友任职岗位">
          <div>{{ familyInfo.familyOrFriendPositionName }}</div>
        </NDescriptionsItem>
      </NDescriptions>
      <div
        v-for="(item, index) in familyInfo.details"
        :key="index"
        class="mb-16px border border-gray-200 rounded-lg bg-white p-16px shadow-sm"
      >
        <NDescriptions bordered label-placement="left" :title="item.relationText">
          <NDescriptionsItem label="与成员关系">
            <div>{{ item.relationText }}</div>
          </NDescriptionsItem>
          <NDescriptionsItem label="姓名">
            <div>{{ item.name }}</div>
          </NDescriptionsItem>
          <NDescriptionsItem label="年龄">
            <div>{{ item.age }}</div>
          </NDescriptionsItem>
          <NDescriptionsItem label="联系方式">
            <div>{{ item.phoneNumber }}</div>
          </NDescriptionsItem>
          <NDescriptionsItem label="单位及职位名称">
            <div>{{ item.companyAndPositionName }}</div>
          </NDescriptionsItem>
        </NDescriptions>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
