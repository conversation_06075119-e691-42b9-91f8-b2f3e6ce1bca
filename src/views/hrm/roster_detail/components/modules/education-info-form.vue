<script setup lang="ts">
import { storeToRefs } from 'pinia';

import ImageDocuments from '@/components/common/image-documents.vue'; // 确保路径正确
import { useRosterStore } from '@/store/modules/roster';

const useRoster = useRosterStore();
const { educationInfo } = storeToRefs(useRoster);

// 学历图片字段配置
const badgePhotos = [
  { label: '学信网截图', kind: 12 },
  { label: '学历证明', kind: 13 },
  { label: '学位证明', kind: 14 }
];

function getFileIdByKind(documents: any[] | null | undefined, kind: number): number | null {
  const match = documents?.find(doc => doc.kind === kind);
  return match?.fileId ?? null;
}
</script>

<template>
  <div>
    <div v-if="!educationInfo.length" class="h-[200px] flex items-center justify-center">
      <NEmpty description="无教育信息" />
    </div>

    <div v-else class="flex flex-col gap-16px">
      <div
        v-for="(item, index) in educationInfo"
        :key="index"
        class="border border-gray-200 rounded-lg bg-white p-16px shadow-sm"
      >
        <NDescriptions bordered label-placement="left" :title="item.educationLevelText">
          <NDescriptionsItem label="在校时间">{{ item.time }}</NDescriptionsItem>
          <NDescriptionsItem label="学历">{{ item.educationLevelText }}</NDescriptionsItem>
          <NDescriptionsItem label="专业">{{ item.major }}</NDescriptionsItem>
          <NDescriptionsItem label="学历状态">{{ item.educationStatusText }}</NDescriptionsItem>
          <NDescriptionsItem label="教育形式">{{ item.educationModeText }}</NDescriptionsItem>
          <NDescriptionsItem label="就读学校">{{ item.name }}</NDescriptionsItem>

          <!-- 动态渲染证明文件 -->
          <template v-for="photo in badgePhotos" :key="photo.kind">
            <NDescriptionsItem :label="photo.label" :span="1">
              <template #default>
                <ImageDocuments
                  v-if="getFileIdByKind(item.proofDocument, photo.kind)"
                  :file-id="getFileIdByKind(item.proofDocument, photo.kind)"
                />
              </template>
            </NDescriptionsItem>
          </template>
        </NDescriptions>
      </div>
    </div>
  </div>
</template>
