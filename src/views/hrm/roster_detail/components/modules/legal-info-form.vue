<script setup lang="ts">
import { NDescriptions, NDescriptionsItem, NEmpty } from 'naive-ui';
import { storeToRefs } from 'pinia';

import ImageDocuments from '@/components/common/image-documents.vue';
import { useRosterStore } from '@/store/modules/roster';

const useRoster = useRosterStore();
const { legalInfo } = storeToRefs(useRoster);
</script>

<template>
  <div v-if="!legalInfo.length" class="h-[200px] flex items-center justify-center">
    <NEmpty description="无合规文件信息" />
  </div>
  <div v-else>
    <div
      v-for="(item, index) in legalInfo"
      :key="index"
      class="mb-16px border border-gray-200 rounded-lg bg-white p-16px shadow-sm"
    >
      <NDescriptions bordered label-placement="left" :title="item.label">
        <NDescriptionsItem
          v-if="item.hasFile"
          :label-style="{
            display: 'none'
          }"
        >
          <div class="w-full flex flex-wrap">
            <NImageGroup>
              <NSpace>
                <div v-for="(file, idx) in item.fileIds as any[]" :key="idx">
                  <ImageDocuments :file-id="file" />
                </div>
              </NSpace>
            </NImageGroup>
          </div>
        </NDescriptionsItem>
        <template v-if="item.kind === 9">
          <!-- 情况 1：有 missingReason 且无 expectedProvideDate -->
          <NDescriptionsItem v-if="item.missingReason && !item.expectedProvideDate" label="无离职证明原因">
            {{ item.missingReason }}
          </NDescriptionsItem>

          <!-- 情况 2：同时有 missingReason 和 expectedProvideDate -->
          <NDescriptionsItem v-if="item.missingReason && item.expectedProvideDate" label="暂时无法提供原因">
            {{ item.missingReason }}
          </NDescriptionsItem>

          <!-- 情况 3：只要有 expectedProvideDate 就显示 -->
          <NDescriptionsItem v-if="item.expectedProvideDate" label="预计提供日期">
            {{ item.expectedProvideDate }}
          </NDescriptionsItem>
        </template>

        <template v-else>
          <NDescriptionsItem v-if="!item.hasFile" label="未提供原因">
            <div>{{ item.missingReason }}</div>
          </NDescriptionsItem>
          <NDescriptionsItem v-if="!item.hasFile" label="预计提供日期">
            <div>{{ item.expectedProvideDate }}</div>
          </NDescriptionsItem>
        </template>
      </NDescriptions>
    </div>
  </div>
</template>

<style scoped></style>
