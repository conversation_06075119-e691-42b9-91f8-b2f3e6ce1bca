<script setup lang="ts">
import { NDescriptions, NDescriptionsItem } from 'naive-ui';
import { storeToRefs } from 'pinia';

import { useRosterStore } from '@/store/modules/roster';

const useRoster = useRosterStore();
const { bankInfo } = storeToRefs(useRoster);
</script>

<template>
  <NDescriptions v-if="!bankInfo.isNotProvide" bordered label-placement="left">
    <NDescriptionsItem label="银行卡号">
      <div>{{ bankInfo.bankAccountNumber }}</div>
    </NDescriptionsItem>
    <NDescriptionsItem label="银行名称">
      <div>{{ bankInfo.bankName }}</div>
    </NDescriptionsItem>
    <NDescriptionsItem label="有效期限">
      <div>{{ bankInfo.expireDate }}</div>
    </NDescriptionsItem>
    <NDescriptionsItem label="银行卡类型">
      <div>{{ bankInfo.cardTypeText }}</div>
    </NDescriptionsItem>
    <NDescriptionsItem label="开户网点名称">
      <div>{{ bankInfo.bankBranchName }}</div>
    </NDescriptionsItem>
    <NDescriptionsItem label="银行卡正面">
      <ImageDocuments v-if="bankInfo.bankCard" :file-id="bankInfo.bankCard" class="mr-8px" />
    </NDescriptionsItem>
    <NDescriptionsItem label="银行流水">
      <div
        v-if="Array.isArray(bankInfo.bankStatementPhotos) && bankInfo.bankStatementPhotos.length"
        class="flex flex-wrap gap-2"
      >
        <NImageGroup>
          <NSpace>
            <div v-for="item in bankInfo.bankStatementPhotos" :key="item">
              <ImageDocuments :file-id="item" />
            </div>
          </NSpace>
        </NImageGroup>
      </div>
    </NDescriptionsItem>
  </NDescriptions>
  <NDescriptions v-else bordered label-placement="left">
    <NDescriptionsItem label="未提供原因">
      <div>{{ bankInfo.missingReason }}</div>
    </NDescriptionsItem>
    <NDescriptionsItem label="预计提供时间">
      <div>{{ bankInfo.expectedProvideDate }}</div>
    </NDescriptionsItem>
  </NDescriptions>
</template>

<style scoped></style>
