<script setup lang="ts">
import { onMounted, ref } from 'vue';


import { useRosterStore } from '@/store/modules/roster';

const useRoster = useRosterStore();
const containerRef = ref<HTMLElement | null>(null);
const isLoaded = ref(false); // 加载完成标志
const route = useRoute();
onMounted(async () => {
  useRoster.resetSections();
  useRoster.userId = route.query.id as string;
  await useRoster.getDictData();
  await useRoster.getAllFun();
  isLoaded.value = true; // 然后再渲染子组件
});
</script>

<template>
  <div class="h-full flex">
    <div ref="containerRef" class="scroll-anchor relative flex-1 overflow-auto px-6">
      <slot name="header" />
      <div>
        <div v-for="sec in useRoster.sections" :id="`detail-section${sec.id}`" :key="sec.id" class="mb-14">
          <h2 class="mb-3 border-l-4 border-blue-500 pl-3 text-2xl text-gray-800 font-semibold">
            {{ sec.title }}
          </h2>
          <div class="min-h-[200px] border rounded-xl bg-white p-6 shadow-md transition-shadow hover:shadow-lg">
            <component :is="sec.component" />
          </div>
        </div>
      </div>
    </div>
    <div class="w-48 pr-4">
      <NAnchor affix listen-to=".scroll-anchor" :offset-target="() => containerRef" show-background show-rail>
        <NAnchorLink
          v-for="item in useRoster.sections"
          :key="item.id"
          :title="item.title"
          :href="`#detail-section${item.id}`"
        />
      </NAnchor>
    </div>
  </div>
</template>

<style scoped>
:deep(.n-anchor-rail__bar) {
  height: 30px !important;
}
:deep(.n-anchor-link-background) {
  height: 30px !important;
}
:deep(.n-anchor-link__title) {
  height: 30px !important;
  line-height: 30px;
}
.scroll-anchor {
  position: relative; /* 关键！保证 affix 正常工作 */
}
:deep(.n-descriptions-table-header) {
  width: 200px !important;
}
:deep(.n-descriptions-table-content) {
  width: 220px !important;
}
</style>
