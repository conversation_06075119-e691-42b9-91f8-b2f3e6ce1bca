<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';

import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { fetchPostBlackList } from '@/service/api/hrm/roster_black';

type RuleKey = keyof Pick<Api.Hrm.BlackListRespVO, 'name' | 'reason'>;
const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();
const rules = ref<Record<RuleKey, App.Global.FormRule>>({
  name: defaultRequiredRule,
  reason: defaultRequiredRule
});
const model = reactive<Api.Hrm.BlackListRespVO>({
  idNumber: '',
  name: '',
  reason: ''
});

async function handleSubmit() {
  try {
    await validate();
    const { error } = await fetchPostBlackList(model);
    if (error) {
      return false;
    }
    window.$message?.success($t('common.addSuccess'));
    return true;
  } catch (e) {
    console.log(e);
    return false;
  }
}
defineExpose({
  handleSubmit
});
onMounted(() => {
  restoreValidation();
});
</script>

<template>
  <NForm ref="formRef" :model="model" :rules="rules">
    <NFormItem span="24 s:12 m:6" label="姓名" path="name" class="pr-24px">
      <NInput v-model:value="model.name" clearable placeholder="请输入姓名" />
    </NFormItem>
    <NFormItem span="24 s:12 m:6" label="身份证号" path="idNumber" class="pr-24px">
      <NInput v-model:value="model.idNumber" clearable placeholder="请输入身份证号" />
    </NFormItem>
    <NFormItem span="24 s:12 m:6" label="拉黑原因" path="reason" class="pr-24px">
      <NInput v-model:value="model.reason" clearable placeholder="请输入拉黑原因" type="textarea" />
    </NFormItem>
  </NForm>
</template>

<style scoped></style>
