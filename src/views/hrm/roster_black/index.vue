<script lang="tsx" setup>
import { N<PERSON><PERSON>on, NPopconfirm } from 'naive-ui';

import { renderModalBtn } from '@/components/re-modal';
import { useTable } from '@/hooks/common/table';
import { useEmployeeSearchForm } from '@/hooks/hrm/useEmployeeSearchForm';
import { $t } from '@/locales';
import { fetchDeleteBlackList, fetchGetBlackListList } from '@/service/api/hrm/roster_black';
import { useAppStore } from '@/store/modules/app';
import AddForm from '@/views/hrm/roster_black/modules/add-form.vue';

const appStore = useAppStore();
const {
  columns,
  columnChecks,
  data,
  getData,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams,
  getDataByPage,
  updateSearchParams
} = useTable({
  apiFn: fetchGetBlackListList,
  showTotal: true,
  apiParams: {
    pageNo: 1,
    pageSize: 10,
    idNumber: null,
    name: null
  },
  columns: () => [
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'name',
      title: '姓名',
      align: 'center'
    },
    {
      key: 'idNumber',
      title: '身份证号',
      align: 'center'
    },
    {
      key: 'reason',
      title: '拉黑原因',
      align: 'center'
    },
    {
      key: 'createTime',
      title: '创建时间',
      align: 'center'
    },
    {
      key: 'updaterName',
      title: '操作人',
      align: 'center'
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 130,
      render: (row: Api.Hrm.BlackListRespVO) => (
        <div class="flex-center gap-8px">
          <NPopconfirm onPositiveClick={() => handleDelete(row.id!)}>
            {{
              default: () => '是否确认移出黑名单？',
              trigger: () => (
                <NButton type="error" ghost size="small">
                  移出黑名单
                </NButton>
              )
            }}
          </NPopconfirm>
        </div>
      )
    }
  ]
});

async function handleDelete(id: number) {
  try {
    const { error } = await fetchDeleteBlackList(id);
    if (!error) {
      await getData();
      window?.$message?.success('移除黑名单成功');
    }
  } catch (e) {
    console.log(e);
  }
}

function handleAdd() {
  renderModalBtn(
    AddForm,
    { operateType: 'add' },
    {
      title: '创建黑名单',
      style: {
        width: '40%'
      },
      func: getData
    }
  );
}

const { form, searchColumns } = useEmployeeSearchForm<Api.Hrm.PromotionConditionsListOrDetailRespVOParams>({
  searchParams,
  resetSearchParams,
  updateSearchParams,
  getDataByPage,
  newColumns: [
    {
      title: '员工姓名',
      path: 'name'
    },

    {
      title: '身份证号',
      path: 'idNumber'
    }
  ]
});
</script>

<template>
  <div class="min-h-500px flex-col-stretch overflow-hidden lt-sm:overflow-auto">
    <ProCard class="mb-10px" :show-collapse="false" content-class="!pb-[0px]">
      <ProSearchForm :form="form" :columns="searchColumns" />
    </ProCard>
    <NCard :bordered="false" class="sm:flex-1-hidden card-wrapper" size="small" title="黑名单">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :loading="loading"
          :permission="['hrm:blacklist:update']"
          @add="handleAdd"
          @refresh="getData"
        />
      </template>
      <NDataTable
        :columns="columns"
        :data="data"
        :loading="loading"
        :pagination="mobilePagination"
        :row-key="row => row.id"
        :flex-height="!appStore.isMobile"
        remote
        size="small"
        class="sm:h-full"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
