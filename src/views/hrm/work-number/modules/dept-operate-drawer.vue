<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue';

import { useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { fetchGetDeptNameList, fetchPutJobNumber } from '@/service/api/kip/department';

defineOptions({
  name: 'DeptOperateDrawer'
});

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.System.Dept | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const tableData = ref<string[]>([]);

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const disabled = ref<boolean>(false);

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: $t('page.system.dept.addDept'),
    edit: $t('page.system.dept.editDept')
  };
  return titles[props.operateType];
});

type Model = Api.System.DeptOperateParams;

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    id: null,
    initialJobNumber: ''
  };
}

type RuleKey = Extract<keyof Model, 'jobNumberPrefix' | 'jobNumberSuffix'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  jobNumberPrefix: [
    { required: true, message: '请输入前缀' },
    {
      validator: (_rule: any, value: any, callback: any) => {
        if (value && !/^[a-zA-Z0-9]+$/.test(value)) {
          callback(new Error('前缀只能包含字母和数字'));
        }
        callback();
      }
    }
  ],
  jobNumberSuffix: [
    { required: true, message: '请输入工号' },
    {
      validator: (_rule: any, value: any, callback: any) => {
        if (value && !/^[0-9]+$/.test(value)) {
          callback(new Error('工号只能包含数字'));
        }
        callback();
      }
    }
  ]
};

function handleUpdateModelWhenEdit() {
  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model, props.rowData);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();

  if (props.operateType === 'edit') {
    const { error } = await fetchPutJobNumber(model.id, model.jobNumberPrefix, model.jobNumberSuffix);
    if (error) return;
  }

  window.$message?.success($t('common.updateSuccess'));
  closeDrawer();
  emit('submitted');
}

watch(visible, () => {
  if (visible.value) {
    disabled.value = false;
    handleUpdateModelWhenEdit();
    restoreValidation();
    tableData.value = [];
    handleBlur();
  }
});

async function handleBlur() {
  if (!model.jobNumberPrefix) {
    tableData.value = [];
    return;
  }
  const { data, error } = await fetchGetDeptNameList(model.jobNumberPrefix);
  if (error) return;
  tableData.value = data;
}
</script>

<template>
  <NDrawer v-model:show="visible" :title="title" display-directive="show" :width="810" class="max-w-90%">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm ref="formRef" :model="model" :rules="rules">
        <NFormItem :label="$t('page.system.dept.deptName')" path="deptName">
          <NInput v-model:value="model.name" disabled :placeholder="$t('page.system.dept.form.deptName.required')" />
        </NFormItem>
        <NFormItem label="当前部门最大工号" path="maxJobNumber">
          <NText>
            {{ model.maxJobNumber || '继承上级部门' }}
          </NText>
        </NFormItem>
        <NFormItem label="工号规则">
          <div class="h-full w-full flex flex-col">
            <NAlert type="warning" title="工号规则说明">
              <div>
                1. 账号规则为字母加数字组合，请直接输入当前部门已使用的最大工号，系统将自动对数字部分递增生成新账号。
              </div>
              <div>2. 若当前部门未配置独立的工号规则，系统将逐级向上继承上级部门的规则设置。</div>
              <div>
                3. 工号配置完成后即时生效，如需修改，新工号必须大于当前系统中的最大工号{{
                  model.maxJobNumber ? `(${model.maxJobNumber})` : ''
                }}。
              </div>
            </NAlert>
          </div>
        </NFormItem>
        <FormItemDes label="前缀部分" path="jobNumberPrefix" scene="工号">
          <div class="w-full flex flex-col">
            <div class="mb-10px">
              <NInput v-model:value="model.jobNumberPrefix" clearable placeholder="请输入前缀部分" @blur="handleBlur" />
            </div>
            <div v-if="tableData.length > 0" class="flex flex-col">
              <NText class="mb-10px">已有{{ tableData.length }}个部门共享此前缀：</NText>
              <div>
                <template v-for="item in tableData" :key="item">
                  <NTag type="info" class="mb-10px mr-10px">
                    {{ item }}
                  </NTag>
                </template>
              </div>
            </div>
          </div>
        </FormItemDes>
        <FormItemDes label="数字部分" path="jobNumberSuffix" scene="工号">
          <NInput
            v-model:value="model.jobNumberSuffix"
            clearable
            :placeholder="model.jobNumberSuffix ? '请输入工号规则' : '继承上级部门'"
          />
        </FormItemDes>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
