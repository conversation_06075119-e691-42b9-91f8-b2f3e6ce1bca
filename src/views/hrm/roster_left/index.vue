<script lang="tsx" setup>
import { NButton, NDropdown, NTag } from 'naive-ui';
import { useRouter } from 'vue-router';

import BlackForm from './modules/black-form.vue';

import PositionPro from '@/components/common/position-pro.vue';
import { renderModalBtn } from '@/components/re-modal';
import { useDict } from '@/hooks/business/useDict';
import { useTable } from '@/hooks/common/table';
import { useEmployeeSearchForm } from '@/hooks/hrm/useEmployeeSearchForm';
import { $t } from '@/locales';
import { fetchGetResigningList } from '@/service/api/hrm/roster_left';
import { useAppStore } from '@/store/modules/app';

const appStore = useAppStore();
const router = useRouter();
const { hrm_position_property } = useDict(['hrm_bpm_status', 'hrm_resigning_status', 'hrm_position_property']);

const {
  columns,
  columnChecks,
  data,
  getData,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams,
  getDataByPage,
  updateSearchParams
} = useTable({
  apiFn: fetchGetResigningList,
  showTotal: true,
  apiParams: {
    pageNo: 1,
    pageSize: 10,
    jobNumber: null,
    name: null,
    positionName: null,
    applyStatus: 1,
    handoverStatus: 1,
    status: null
  },
  columns: () => [
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'name',
      title: '员工姓名',
      align: 'center',
      width: 100
    },
    {
      key: 'jobNumber',
      title: '工号',
      align: 'center',
      width: 100
    },
    {
      key: 'entryDate',
      title: '入职日期',
      align: 'center',
      width: 100
    },
    {
      key: 'deptList',
      title: '离职时部门',
      align: 'center',
      render: (row: Api.Hrm.ResignationRespVO) => {
        return row.deptList?.map(dept => dept.deptName).join(',');
      },
      width: 100
    },
    {
      key: 'positionProperty',
      title: '岗位属性',
      align: 'center',
      render: (row: Api.Hrm.ResignationRespVO) => <PositionPro positionProperty={row.positionProperty} />,
      width: 100
    },
    {
      key: 'positionName',
      title: '职位',
      align: 'center',
      width: 100
    },

    {
      key: 'applyStatus',
      title: '离职状态',
      align: 'center',
      render: (row: Api.Hrm.ResignationRespVO) => {
        return <NTag type="info">{row.handoverStatus === 1 ? '离职交接中' : '离职申请中'}</NTag>;
      },
      width: 100
    },
    {
      key: 'resignationDate',
      title: '离职日期',
      align: 'center',
      width: 100
    },
    {
      key: 'resignationReason',
      title: '离职原因',
      align: 'center',
      width: 100
    },
    {
      key: 'reasonMemo',
      title: '离职原因备注',
      align: 'center',
      width: 120,
      ellipsis: {
        tooltip: {
          style: {
            maxWidth: '500px',
            whiteSpace: 'pre-line',
            maxHeight: '300px',
            overflowY: 'auto'
          }
        }
      }
    },

    {
      key: 'applyDescription',
      title: '离职申请描述',
      align: 'center',
      width: 120
    },
    {
      key: 'applyIssueDate',
      title: '离职申请时间',
      align: 'center',
      width: 120
    },
    {
      key: 'handoverDescription',
      title: '离职交接描述',
      align: 'center',
      width: 120
    },
    {
      key: 'handoverIssueDate',
      title: '离职交接时间',
      align: 'center',
      width: 120
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      fixed: 'right',
      width: 120,
      render: (rowData: Api.Hrm.ResignationRespVO, _rowIndex: number) => {
        const options = optionsFilter(rowData);
        return (
          <div class="flex-center gap-8px">
            <NButton
              type="primary"
              ghost
              size="small"
              onClick={() => router.push({ path: '/hrm/roster/profile', query: { id: rowData?.employeeId } })}
            >
              详情
            </NButton>
            {options.length ? (
              <NDropdown
                trigger="hover"
                placement="right-start"
                options={options}
                onSelect={(key: string) => {
                  if (key === 'apply') {
                    window.open(rowData.applyExternalUrl);
                  } else if (key === 'connect') {
                    window.open(rowData.handoverExternalUrl);
                  } else if (key === 'black') {
                    handleBlack(rowData);
                  }
                }}
              >
                <NButton size="small" type="primary" ghost>
                  <icon-ic-outline-more-horiz />
                </NButton>
              </NDropdown>
            ) : null}
          </div>
        );
      }
    }
  ]
});

function handleBlack(rowData: Api.Hrm.ResignationRespVO) {
  renderModalBtn(
    BlackForm,
    { rowData },
    {
      title: '拉入黑名单',
      style: {
        width: '50%'
      },
      func: getData
    }
  );
}

function optionsFilter(rowData: Api.Hrm.ResignationRespVO) {
  const options = [];
  if (rowData.applyExternalUrl) {
    options.push({ label: '钉钉审批详情', key: 'apply' });
  }
  if (rowData.handoverExternalUrl) {
    options.push({ label: '钉钉交接详情', key: 'connect' });
  }
  options.push({ label: '拉黑', key: 'black' });
  return options;
}

const { form, searchColumns } = useEmployeeSearchForm<Api.Hrm.ResignedReqVOData>({
  searchParams,
  resetSearchParams,
  updateSearchParams,
  getDataByPage,
  newColumns: [
    {
      path: 'name',
      title: '员工姓名'
    },
    {
      path: 'positionName',
      title: '岗位'
    },
    {
      path: 'status',
      title: '状态',
      field: 'select',
      fieldProps: {
        options: [
          { label: '离职申请中', value: 1 },
          { label: '离职交接中', value: 2 }
        ]
      }
    },
    {
      path: 'positionProperty',
      title: '岗位属性',
      field: 'select',
      fieldProps: {
        options: hrm_position_property
      }
    },
    {
      path: 'jobNumber',
      title: '工号'
    }
  ]
});
</script>

<template>
  <div class="min-h-500px flex-col-stretch overflow-hidden lt-sm:overflow-auto">
    <ProCard class="mb-10px" :show-collapse="false" content-class="!pb-[0px]">
      <ProSearchForm :form="form" :columns="searchColumns" />
    </ProCard>
    <NCard :bordered="false" class="sm:flex-1-hidden card-wrapper" size="small" title="离职中">
      <template #header-extra>
        <TableHeaderOperation v-model:columns="columnChecks" :loading="loading" :show-add="false" @refresh="getData" />
      </template>
      <NDataTable
        :columns="columns"
        :data="data"
        :loading="loading"
        :pagination="mobilePagination"
        :row-key="row => row.id"
        :flex-height="!appStore.isMobile"
        remote
        size="small"
        :scroll-x="1800"
        class="sm:h-full"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
