<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';

import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { fetchPostBlackList } from '@/service/api/hrm/roster_black';

interface IProps {
  rowData: Api.Hrm.SimpleEmployeeInfo;
}
const props = defineProps<IProps>();
type RuleKey = keyof Pick<Api.Hrm.BlackListRespVO, 'name' | 'reason'>;
const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();
const rules = ref<Record<RuleKey, App.Global.FormRule>>({
  name: defaultRequiredRule,
  reason: defaultRequiredRule
});
const model = reactive<Api.Hrm.BlackListRespVO>({
  employeeId: 0,
  name: '',
  reason: ''
});

async function handleSubmit() {
  try {
    await validate();
    model.employeeId = props.rowData.id;
    model.name = props.rowData.name;
    const { error } = await fetchPostBlackList(model);
    if (error) {
      return false;
    }
    window.$message?.success($t('common.addSuccess'));
    return true;
  } catch (e) {
    console.log(e);
    return false;
  }
}
defineExpose({
  handleSubmit
});
onMounted(() => {
  model.name = props.rowData.name;
  restoreValidation();
});
</script>

<template>
  <NForm ref="formRef" :model="model" :rules="rules">
    <NFormItem span="24 s:12 m:6" label="员工姓名" path="name" class="pr-24px">
      <NText>{{ model.name }}</NText>
    </NFormItem>
    <NFormItem span="24 s:12 m:6" label="拉黑原因" path="reason" class="pr-24px">
      <NInput v-model:value="model.reason" clearable placeholder="请输入拉黑原因" type="textarea" />
    </NFormItem>
  </NForm>
</template>

<style scoped></style>
