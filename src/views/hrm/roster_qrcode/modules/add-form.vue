<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';

import SelectWithSearch from '@/components/common/select-with-search.vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { fetchPostOnboarding, fetchPutOnboarding } from '@/service/api/hrm/roster_qrcode';
import type { SelectWithSearchType } from '@/typings/common-type';
import { getAllPositionOptions, getFormatDictData, getUserListByDeptOptions } from '@/utils/async-functions';
import { renderLabel, renderSingleSelectTag } from '@/utils/select-tag';
interface Props {
  /** 操作类型 */
  operateType: NaiveUI.TableOperateType;
  /** 编辑时的数据 */
  rowData?: Api.Hrm.OnboardingLinkRespVO | null;
}

type RuleKey = keyof Omit<Api.Hrm.CreateOnboardingReqVo, 'notifier' | 'id' | 'notifierUser'>;
const props = defineProps<Props>();
const hrmPositionTypeOptions = ref<Api.System.FormattedOption[]>([]);
const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

// 表单校验规则
const rules = ref<Record<RuleKey, App.Global.FormRule>>({
  deptId: defaultRequiredRule,
  positionId: defaultRequiredRule,
  user: defaultRequiredRule,
  positionProperty: defaultRequiredRule
});

// 表单模型
const model = reactive<Api.Hrm.CreateOnboardingReqVo>({
  id: null!,
  deptId: null!,
  positionId: null!,
  user: null!,
  notifier: null!,
  notifierUser: null!,
  positionProperty: null!
});

// 提交处理函数
async function handleSubmit() {
  try {
    await validate();
    const { error } =
      props.operateType === 'add'
        ? await fetchPostOnboarding(model)
        : await fetchPutOnboarding(model.id, { notifierId: model.notifier });
    if (error) return false;

    const successMessage = props.operateType === 'add' ? $t('common.addSuccess') : $t('common.updateSuccess');
    window.$message?.success(successMessage);

    return true;
  } catch (e) {
    console.error('[Onboarding Submit Error]:', e);
    return false;
  }
}

// 初始化表单（编辑模式）
function initModel() {
  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model, props.rowData);
    model.id = props.rowData.id;
    model.notifierUser = props.rowData.notifier
      ? {
          id: props.rowData.notifier?.id,
          name: props.rowData.notifier?.nickname,
          avatar: props.rowData.notifier?.avatar
        }
      : null;
    model.notifier = props.rowData.notifierId;
  }
}

async function getContractDict() {
  const { data } = await getFormatDictData({
    keys: ['hrm_position_property'].join(',')
  });

  hrmPositionTypeOptions.value =
    data.hrm_position_property.map(item => ({
      label: item.label,
      value: item.value
    })) || [];
}

const deptTref = useTemplateRef<SelectWithSearchType>('deptTref');
// 监听项目变更
watch(
  () => model.deptId,
  () => {
    deptTref.value?.reset();
  }
);
defineExpose({ handleSubmit });

onMounted(() => {
  restoreValidation();
  getContractDict();
  initModel();
});
</script>

<template>
  <NForm ref="formRef" :model="model" :rules="rules">
    <NAlert type="warning" class="mb-10px">链接创建完成之后只能修改审核人，如需修改其他信息，请删除后重新创建。</NAlert>

    <template v-if="operateType === 'add'">
      <NFormItem label="岗位属性" path="positionProperty" span="24 s:12 m:12">
        <NSelect v-model:value="model.positionProperty" :options="hrmPositionTypeOptions" />
      </NFormItem>

      <FormItemDes label="职位" path="positionId" scene="入职链接">
        <SelectWithSearch
          v-model:value="model.positionId"
          :api-func="getAllPositionOptions"
          :page-size="0"
          placeholder="职位"
          :selected-options="[]"
        />
      </FormItemDes>
      <FormItemDes label="部门" path="deptId" scene="入职链接">
        <DeptTreeSelect v-model:value="model.deptId" placeholder="请选择部门" />
      </FormItemDes>

      <FormItemDes label="直属上级" path="user" scene="入职链接">
        <SelectWithSearch
          v-model:value="model.user"
          :api-func="getUserListByDeptOptions"
          :cache="false"
          :selected-options="[]"
          :render-label="renderLabel"
          :render-tag="renderSingleSelectTag"
          placeholder="直属上级"
        />
      </FormItemDes>
    </template>

    <FormItemDes label="审核人" path="notifier" scene="入职链接">
      <SelectWithSearch
        v-model:value="model.notifier"
        :api-func="getUserListByDeptOptions"
        :selected-options="[model.notifierUser]"
        placeholder="审核人"
        :render-tag="renderSingleSelectTag"
        :render-label="renderLabel"
      />
    </FormItemDes>
  </NForm>
</template>
