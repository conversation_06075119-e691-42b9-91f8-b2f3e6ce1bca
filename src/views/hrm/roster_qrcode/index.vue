<script lang="tsx" setup>
import { NImage, NSwitch, NTag } from 'naive-ui';

import AddForm from './modules/add-form.vue';

import { renderModalBtn } from '@/components/re-modal';
import { deleteRoleBtn, editRoleBtn } from '@/directives/permission/permi-btn';
import { useAuth } from '@/hooks/business/auth';
import { useTable } from '@/hooks/common/table';
import { useEmployeeSearchForm } from '@/hooks/hrm/useEmployeeSearchForm';
import { $t } from '@/locales';
import { fetchDeleteStatus, fetchGetEmployeeList, fetchPutStatus } from '@/service/api/hrm/roster_qrcode';
import { useAppStore } from '@/store/modules/app';
import { getAllPositionOptions, getUserListByDeptOptions } from '@/utils/async-functions';
import { renderLabel, renderSingleSelectTag } from '@/utils/select-tag';
import { showUserStr } from '@/utils/useful_func';

const { hasAuth } = useAuth();
const appStore = useAppStore();
const {
  columns,
  columnChecks,
  data,
  getData,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams,
  getDataByPage,
  updateSearchParams
} = useTable({
  apiFn: fetchGetEmployeeList,
  showTotal: true,
  apiParams: {
    pageNo: 1,
    pageSize: 10,
    positionId: null,
    deptId: null,
    status: null,
    userId: null
  },
  columns: () => [
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'qrcode',
      title: '二维码',
      align: 'center',
      render: (row: Api.Hrm.OnboardingLinkRespVO) => (
        <NImage
          width="50"
          src={row.qrcode}
          render-toolbar={({ nodes }) => {
            const { rotateCounterclockwise, rotateClockwise, resizeToOriginalSize, zoomOut, zoomIn, close } = nodes;
            return [rotateCounterclockwise, rotateClockwise, resizeToOriginalSize, zoomOut, zoomIn, close];
          }}
        />
      )
    },

    {
      key: 'positionName',
      title: '职位',
      align: 'center'
    },
    {
      key: 'deptName',
      title: '部门',
      align: 'center'
    },
    {
      key: 'userName',
      title: '直属上级',
      align: 'center',
      render: (row: Api.Hrm.OnboardingLinkRespVO) => {
        return (
          <NTag type="info" size="small">
            {showUserStr(row.user)}
          </NTag>
        );
      }
    },
    {
      key: 'positionProperty',
      title: '岗位属性',
      align: 'center',
      render: (row: Api.Hrm.OnboardingLinkRespVO) => {
        return row.positionProperty === '1' ? '一线' : '非一线';
      }
    },
    {
      key: 'notifier',
      title: '审核人',
      align: 'center',
      render: (row: Api.Hrm.OnboardingLinkRespVO) => {
        return (
          <NTag type="info" size="small">
            {showUserStr(row.notifier)}
          </NTag>
        );
      }
    },
    {
      key: 'link',
      title: '链接',
      align: 'center'
    },
    {
      key: 'valid',
      title: '状态',
      align: 'center',
      render: (row: Api.Hrm.OnboardingLinkRespVO) => (
        <NSwitch
          key={row.valid + String(row.switchLoading)}
          value={row.valid}
          loading={row.switchLoading}
          disabled={!hasAuth(['hrm:onboarding:update'])}
          onUpdateValue={async (val: boolean) => await handleChange(row, val)}
        />
      )
    },
    {
      key: 'createTime',
      title: '创建时间',
      align: 'center'
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 130,
      render: row => (
        <div class="flex-center gap-8px">
          {editRoleBtn(['hrm:onboarding:update'], () => handleOperate('edit', row))}
          {deleteRoleBtn(['hrm:onboarding:delete'], () => handleDelete(row.id))}
        </div>
      )
    }
  ]
});

async function handleDelete(id: number) {
  try {
    await fetchDeleteStatus(id);
    window?.$message?.success('删除成功');
    await getData();
  } catch (e: unknown) {
    window?.$message?.error('删除失败');
  }
}

async function handleChange(row: Api.Hrm.OnboardingLinkRespVO, status: boolean) {
  row.switchLoading = true; // 开启 loading
  const oldStatus = row.valid;
  row.valid = status;

  try {
    await fetchPutStatus(row.id, { valid: status });
    window?.$message?.success('状态更新成功');
  } catch (e: unknown) {
    row.valid = oldStatus;
    window?.$message?.error('状态更新失败');
  } finally {
    row.switchLoading = false;
  }
}

function handleOperate(type: NaiveUI.TableOperateType, rowData: Api.Hrm.OnboardingLinkRespVO | undefined = undefined) {
  renderModalBtn(
    AddForm,
    { operateType: type, rowData },
    {
      title: type === 'add' ? '创建入职链接' : '编辑入职链接',
      style: {
        width: '40%'
      },
      func: getData
    }
  );
}

const { form, searchColumns } = useEmployeeSearchForm<Api.Hrm.OnboardingSearchForm>({
  searchParams,
  resetSearchParams,
  updateSearchParams,
  getDataByPage,
  newColumns: [
    {
      title: '职位',
      path: 'positionId',
      field: 'select-with-search',
      fieldProps: {
        apiFunc: getAllPositionOptions,
        selectedOptions: [],
        pageSize: 0,
        placeholder: '职位'
      }
    },
    {
      title: '部门',
      path: 'deptId',
      field: 'dept-tree-select',
      fieldProps: {
        placeholder: '请选择部门'
      }
    },
    {
      title: '直属上级',
      path: 'userId',
      field: 'select-with-search',
      fieldProps: {
        apiFunc: getUserListByDeptOptions,
        selectedOptions: [],
        renderLabel,
        renderTag: renderSingleSelectTag,
        placeholder: '直属上级'
      }
    },
    {
      title: '状态',
      path: 'status',
      field: 'select',
      fieldProps: {
        options: [
          {
            label: '是',
            value: 1
          },
          {
            label: '否',
            value: 0
          }
        ]
      }
    }
  ]
});
onMounted(() => {
  console.log('我出发了');
});
</script>

<template>
  <div class="min-h-500px flex-col-stretch overflow-hidden lt-sm:overflow-auto">
    <ProCard class="mb-10px" :show-collapse="false" content-class="!pb-[0px]">
      <ProSearchForm :form="form" :columns="searchColumns" />
    </ProCard>
    <NCard :bordered="false" class="sm:flex-1-hidden card-wrapper" size="small" title="入职链接">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :loading="loading"
          :permission="['hrm:onboarding:create']"
          @refresh="getData"
          @add="handleOperate('add')"
        />
      </template>
      <NDataTable
        :columns="columns"
        :data="data"
        :loading="loading"
        :pagination="mobilePagination"
        :row-key="row => row.id"
        :flex-height="!appStore.isMobile"
        remote
        size="small"
        class="sm:h-full"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
