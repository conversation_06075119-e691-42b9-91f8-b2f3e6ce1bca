<script lang="tsx" setup>
import { NButton, NDropdown, NTag } from 'naive-ui';
import { computed } from 'vue';
import { useRouter } from 'vue-router';

import { renderModalBtn } from '@/components/re-modal';
import { infoRoleBtn } from '@/directives/permission/permi-btn';
import { EmployeeStatus } from '@/enum';
import { useAuth } from '@/hooks/business/auth';
import { useDict } from '@/hooks/business/useDict';
import { useTable } from '@/hooks/common/table';
import { useEmployeeSearchForm } from '@/hooks/hrm/useEmployeeSearchForm';
import { $t } from '@/locales';
import { fetchGetEmployeeList } from '@/service/api/hrm/roster_user';
import { useAppStore } from '@/store/modules/app';
import BlackForm from '@/views/hrm/roster_abnormal/modules/black-form.vue';

const appStore = useAppStore();
const router = useRouter();
const { hasAuth } = useAuth();
const { hrm_on_job_status, hrm_position_property } = useDict(['hrm_on_job_status', 'hrm_position_property']);
const options = computed(() => {
  const baseOptions = [
    {
      label: '离职',
      key: 'black',
      auth: ['hrm:blacklist:update']
    },
    {
      label: '发起签约',
      key: 'contract',
      auth: ['system:permission:assign-user-role']
    }
  ];

  return baseOptions.filter(option => hasAuth(option.auth));
});
const {
  columns,
  columnChecks,
  data,
  getData,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams,
  getDataByPage,
  updateSearchParams
} = useTable({
  apiFn: fetchGetEmployeeList,
  showTotal: true,
  apiParams: {
    status: EmployeeStatus.OnJob,
    positionId: null,
    deptId: null,
    jobNumber: null,
    name: null,
    subStatus: null
  },
  columns: () => [
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'name',
      title: '姓名',
      align: 'center'
    },
    {
      key: 'dept.name',
      title: '部门',
      align: 'center'
    },
    {
      key: 'position.name',
      title: '职位',
      align: 'center'
    },
    {
      key: 'entryDate',
      title: '入职时间',
      align: 'center'
    },
    {
      key: 'status',
      title: '员工状态',
      align: 'center',
      render: (row: Api.Hrm.SimpleEmployeeInfo) => <NTag type="info">{row.status.data.name}</NTag>
    },
    {
      key: 'phoneNumber',
      title: '手机号',
      align: 'center'
    },
    {
      key: 'jobNumber',
      title: '工号',
      align: 'center'
    },
    {
      key: 'positionProperty',
      title: '岗位属性',
      align: 'center',
      render: (row: Api.Hrm.SimpleEmployeeInfo) => <PositionPro positionProperty={row.positionProperty} />
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 180,
      render: (rowData: Api.Hrm.SimpleEmployeeInfo) => (
        <div class="flex-center gap-8px">
          {infoRoleBtn(['hrm:employee:query'], 'primary', () =>
            router.push({ path: '/hrm/roster/profile', query: { id: rowData.id } })
          )}
          {options.value.length ? (
            <NDropdown
              trigger="hover"
              placement="right-start"
              options={options.value}
              onSelect={(key: string) => {
                if (key === 'black') {
                  handleBlack(rowData);
                } else if (key === 'contract') {
                  router.push({ path: '/hrm/roster/resign-contract', query: { id: rowData.id } });
                }
              }}
            >
              <NButton size="small" type="primary" ghost>
                <icon-ic-outline-more-horiz />
              </NButton>
            </NDropdown>
          ) : null}
        </div>
      )
    }
  ]
});

function handleBlack(rowData: Api.Hrm.SimpleEmployeeInfo) {
  renderModalBtn(
    BlackForm,
    { rowData },
    {
      title: '离职',
      style: {
        width: '50%'
      },
      contentClass: 'max-h-[80vh] overflow-auto',
      func: getData
    }
  );
}

const { form, searchColumns } = useEmployeeSearchForm<Api.Hrm.SimpleEmployeeInfoSearch>({
  searchParams,
  resetSearchParams,
  updateSearchParams,
  getDataByPage,
  shiftIndex: 2,
  extendColumns: [
    {
      title: '状态',
      path: 'subStatus',
      field: 'select',
      fieldProps: {
        options: hrm_on_job_status
      }
    },
    {
      title: '岗位属性',
      path: 'positionProperty',
      field: 'select',
      fieldProps: {
        options: hrm_position_property
      }
    }
  ] as any
});
</script>

<template>
  <div class="min-h-500px flex-col-stretch overflow-hidden lt-sm:overflow-auto">
    <ProCard class="mb-10px" :show-collapse="false" content-class="!pb-[0px]">
      <ProSearchForm :form="form" :columns="searchColumns" />
    </ProCard>
    <NCard :bordered="false" class="sm:flex-1-hidden card-wrapper" size="small" title="在职">
      <template #header-extra>
        <TableHeaderOperation v-model:columns="columnChecks" :loading="loading" :show-add="false" @refresh="getData" />
      </template>
      <NDataTable
        :columns="columns"
        :data="data"
        :loading="loading"
        :pagination="mobilePagination"
        :row-key="row => row.id"
        :flex-height="!appStore.isMobile"
        remote
        size="small"
        class="sm:h-full"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
