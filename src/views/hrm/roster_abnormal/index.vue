<script lang="tsx" setup>
import { NButton, NDropdown, NTag } from 'naive-ui';
import { useRouter } from 'vue-router';

import BlackForm from './modules/black-form.vue';
import ToStatusForm from './modules/to-status-form.vue';

import { renderModalBtn } from '@/components/re-modal';
import { infoRoleBtn } from '@/directives/permission/permi-btn';
import { AbnormalStatus, EmployeeStatus } from '@/enum';
import { useAuth } from '@/hooks/business/auth';
import { useDict } from '@/hooks/business/useDict';
import { useTable } from '@/hooks/common/table';
import { useEmployeeSearchForm } from '@/hooks/hrm/useEmployeeSearchForm';
import { $t } from '@/locales';
import { fetchGetEmployeeList } from '@/service/api/hrm/roster_user';
import { useAppStore } from '@/store/modules/app';

const appStore = useAppStore();
const router = useRouter();
const { hasAuth } = useAuth();
const tabValue = ref('All');
const { hrm_abnormal_status } = useDict(['hrm_abnormal_status']);
const abnormalStatus = computed(() => {
  return [
    {
      value: 'All',
      label: '全部'
    },
    ...hrm_abnormal_status.value
  ];
});

function handleOptions(rowData: Api.Hrm.SimpleEmployeeInfo) {
  const status = rowData.status.data.id;

  const OPTION_CONFIGS = {
    probation: {
      label: '转试用期',
      auth: ['hrm:employee:update']
    },
    'on-job': {
      label: '转正式',
      auth: ['hrm:employee:update']
    },
    leave: {
      label: '离职',
      auth: ['hrm:employee:update']
    }
  } as const;

  type OptionKey = keyof typeof OPTION_CONFIGS;
  const STATUS_OPTIONS_MAP: Record<AbnormalStatus, OptionKey[]> = {
    [AbnormalStatus.JoinDirectly]: ['probation', 'on-job', 'leave'],
    [AbnormalStatus.ExitDingTalk]: ['leave'],
    [AbnormalStatus.AttendanceException]: [],
    [AbnormalStatus.GiveUpEntry]: []
  };

  const optionKeys = STATUS_OPTIONS_MAP[status as unknown as AbnormalStatus] || [];

  return optionKeys.map(key => ({ key, ...OPTION_CONFIGS[key] })).filter(option => hasAuth(option.auth));
}

const {
  columns,
  columnChecks,
  data,
  getData,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams,
  getDataByPage,
  updateSearchParams
} = useTable({
  apiFn: fetchGetEmployeeList,
  showTotal: true,
  apiParams: {
    status: EmployeeStatus.Abnormal,
    positionId: null,
    deptId: null,
    jobNumber: null,
    name: null
  },
  columns: () => [
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'name',
      title: '姓名',
      align: 'center'
    },
    {
      key: 'dept.name' as keyof Api.Hrm.SimpleEmployeeInfo,
      title: '部门',
      align: 'center'
    },
    {
      key: 'position.name' as keyof Api.Hrm.SimpleEmployeeInfo,
      title: '职位',
      align: 'center'
    },
    {
      key: 'entryDate',
      title: '入职时间',
      align: 'center'
    },
    {
      key: 'status',
      title: '员工状态',
      align: 'center',
      render: (row: Api.Hrm.SimpleEmployeeInfo) => <NTag type="info">{row.status.data.name}</NTag>
    },
    {
      key: 'phoneNumber',
      title: '手机号',
      align: 'center'
    },
    {
      key: 'jobNumber',
      title: '工号',
      align: 'center'
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 180,
      render: (rowData: Api.Hrm.SimpleEmployeeInfo) => {
        const options = handleOptions(rowData);
        return (
          <div class="flex-center gap-8px">
            {infoRoleBtn(['hrm:employee:query'], 'primary', () =>
              router.push({ path: '/hrm/roster/profile', query: { id: rowData.id } })
            )}
            {options.length ? (
              <NDropdown
                trigger="hover"
                placement="right-start"
                options={options}
                onSelect={(key: string) => {
                  if (key === 'leave') {
                    handleBlack(rowData);
                  } else if (key === 'probation') {
                    handleProbation(rowData);
                  } else if (key === 'on-job') {
                    handleOnJob(rowData);
                  }
                }}
              >
                <NButton size="small" type="primary" ghost>
                  <icon-ic-outline-more-horiz />
                </NButton>
              </NDropdown>
            ) : null}
          </div>
        );
      }
    }
  ]
});

// 离职
function handleBlack(rowData: Api.Hrm.SimpleEmployeeInfo) {
  renderModalBtn(
    BlackForm,
    { rowData },
    {
      title: '离职',
      style: {
        width: '50%'
      },
      func: getData
    }
  );
}

// 转正式
function handleOnJob(rowData: Api.Hrm.SimpleEmployeeInfo) {
  renderModalBtn(
    ToStatusForm,
    { useId: rowData.id, status: EmployeeStatus.OnJob },
    {
      title: '转正式',
      style: {
        width: '50%'
      },
      func: getData
    }
  );
}

// 转试用期
function handleProbation(rowData: Api.Hrm.SimpleEmployeeInfo) {
  renderModalBtn(
    ToStatusForm,
    { useId: rowData.id, status: EmployeeStatus.Probation },
    {
      title: '转试用期',
      style: {
        width: '50%'
      },
      func: getData
    }
  );
}

const { form, searchColumns } = useEmployeeSearchForm<Api.Hrm.SimpleEmployeeInfoSearch>({
  searchParams,
  resetSearchParams,
  updateSearchParams,
  getDataByPage
});
watch(tabValue, () => {
  updateSearchParams({ subStatus: tabValue.value === 'All' ? null : tabValue.value });
  getData();
});
</script>

<template>
  <div class="min-h-500px flex-col-stretch overflow-hidden lt-sm:overflow-auto">
    <ProCard class="mb-10px" :show-collapse="false" content-class="!pb-[0px]">
      <ProSearchForm :form="form" :columns="searchColumns" />
    </ProCard>
    <NCard
      :bordered="false"
      class="sm:flex-1-hidden card-wrapper"
      size="small"
      title="异常员工"
      content-class="flex flex-col"
    >
      <template #header-extra>
        <TableHeaderOperation v-model:columns="columnChecks" :loading="loading" :show-add="false" @refresh="getData" />
      </template>

      <NTabs v-model:value="tabValue" type="card" size="small">
        <NTabPane v-for="song in abnormalStatus" :key="song.value" :name="song.value" :tab="song.label">
          <template #tab>
            <div class="flex items-center gap-4px">
              {{ song.label }}
              <FormTip v-if="song.value !== 'All'" :content="song.data" />
            </div>
          </template>
        </NTabPane>
      </NTabs>

      <NDataTable
        :columns="columns"
        :data="data"
        :loading="loading"
        :pagination="mobilePagination"
        :row-key="row => row.id"
        :flex-height="!appStore.isMobile"
        remote
        size="small"
        class="sm:h-full"
      />
    </NCard>
  </div>
</template>

<style scoped>
:deep(.n-tab-pane) {
  display: none;
}
:deep(.n-tabs-pad) {
  display: none;
}
</style>
