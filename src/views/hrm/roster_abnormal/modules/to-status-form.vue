<script setup lang="ts">
import { NSelect } from 'naive-ui';
import { ref } from 'vue';

import DateTimePicker from '@/components/common/date-time-picker.vue';
import { EmployeeStatus } from '@/enum';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { fetchPutCompanyOptions } from '@/service/api/hrm/company-management';
import { fetchPutEmployeeStatus } from '@/service/api/hrm/roster_abnormal';
import { getFormatDictData } from '@/utils/async-functions';
import { calculateTrialEndDate } from '@/utils/date-format';

interface IProps {
  useId: number;
  status: EmployeeStatus;
}
const props = defineProps<IProps>();
const { formRef, validate, restoreValidation } = useNaiveForm();
const companyOptions = ref<CommonType.Option<number>[]>([]);
const hrmProbationPeriodTypeOptions = ref<Api.System.FormattedOption[]>([]);
const hrmEmployeeTypeOptions = ref<Api.System.FormattedOption[]>([]);
const hrmPositionPropertyOptions = ref<Api.System.FormattedOption[]>([]);
const model = reactive<Api.Hrm.UpdateStatusReqVOData>({
  companyId: null,
  newStatus: null,
  employeeType: null,
  entryDate: null,
  planRegularTime: null,
  positionProperty: null,
  probationPeriodType: null,
  regularizeDate: null
});
const { defaultRequiredRule } = useFormRules();
type RuleKey = keyof Api.Hrm.UpdateStatusReqVO;

const baseRules: Partial<Record<RuleKey, App.Global.FormRule>> = {
  companyId: defaultRequiredRule,
  employeeType: defaultRequiredRule,
  entryDate: defaultRequiredRule,
  positionProperty: defaultRequiredRule,
  probationPeriodType: defaultRequiredRule
};

if (props.status === EmployeeStatus.Probation) {
  baseRules.planRegularTime = defaultRequiredRule;
}

if (props.status === EmployeeStatus.OnJob) {
  baseRules.regularizeDate = defaultRequiredRule;
}

const rules = ref(baseRules as Record<RuleKey, App.Global.FormRule>);

async function handleSubmit() {
  try {
    await validate();
    const { error } = await fetchPutEmployeeStatus(props.useId, { ...model, newStatus: props.status });
    if (error) return false;
    window.$message?.success('更新成功');
    return true;
  } catch (e) {
    return false;
  }
}

async function getContractDict() {
  const { data } = await getFormatDictData({
    keys: ['hrm_probation_period_type', 'hrm_employee_type', 'hrm_position_property'].join(',')
  });
  hrmProbationPeriodTypeOptions.value =
    data.hrm_probation_period_type.map(item => ({
      label: item.label,
      value: Number(item.value)
    })) || [];
  hrmEmployeeTypeOptions.value =
    data.hrm_employee_type.map(item => ({
      label: item.label,
      value: Number(item.value)
    })) || [];
  hrmPositionPropertyOptions.value =
    data.hrm_position_property.map(item => ({
      label: item.label,
      value: Number(item.value)
    })) || [];
}

async function handleGetCompanyOptions() {
  const { data, error } = await fetchPutCompanyOptions();
  if (error) return;
  companyOptions.value = data.map(item => ({
    label: item.name,
    value: item.id
  }));
}

function exportFormAlidate() {
  return formRef.value?.validate();
}

function disablePreviousDate(ts: number) {
  return ts < Date.now();
}
watch([() => model.entryDate, () => model.probationPeriodType], ([entryDate, probationPeriodType]) => {
  if (Number(probationPeriodType) === 8) return;
  if (entryDate && probationPeriodType) {
    model.planRegularTime = calculateTrialEndDate(entryDate, probationPeriodType);
  }
});
defineExpose({ handleSubmit, exportFormAlidate });
onMounted(() => {
  getContractDict();
  handleGetCompanyOptions();
  restoreValidation();
});
</script>

<template>
  <NForm ref="formRef" :model="model" :rules="rules">
    <NFormItem label="公司主体" path="companyId">
      <NSelect v-model:value="model.companyId" :options="companyOptions" placeholder="请选择公司主体" />
    </NFormItem>
    <NFormItem label="岗位属性" path="positionProperty">
      <NSelect
        v-model:value="model.positionProperty"
        :options="hrmPositionPropertyOptions"
        placeholder="请选择岗位属性"
      />
    </NFormItem>
    <NFormItem label="员工类型" path="employeeType">
      <NSelect v-model:value="model.employeeType" :options="hrmEmployeeTypeOptions" placeholder="请选择员工类型" />
    </NFormItem>

    <NFormItem label="试用期类型" path="probationPeriodType">
      <NSelect
        v-model:value="model.probationPeriodType"
        clearable
        :options="hrmProbationPeriodTypeOptions"
        placeholder="请选择试用期类型"
      />
    </NFormItem>
    <NFormItem label="入职时间" path="entryDate">
      <DateTimePicker v-model:time="model.entryDate" label="入职时间" use-for="date" class="w-full" />
    </NFormItem>
    <NFormItem label="计划转正日期" path="planRegularTime">
      <DateTimePicker
        v-model:time="model.planRegularTime"
        label="计划转正日期"
        use-for="date"
        class="w-full"
        :is-date-disabled="disablePreviousDate"
      />
    </NFormItem>
    <NFormItem v-if="props.status === EmployeeStatus.OnJob" label="实际转正日期" path="regularizeDate">
      <DateTimePicker
        v-model:time="model.regularizeDate"
        label="实际转正日期"
        use-for="date"
        class="w-full"
        :is-date-disabled="disablePreviousDate"
      />
    </NFormItem>
  </NForm>
</template>

<style scoped></style>
