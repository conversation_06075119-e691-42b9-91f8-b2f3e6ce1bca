<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';

import { EmployeeStatus } from '@/enum';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { fetchGetLeaveReasonOptions, fetchPutEmployeeStatus } from '@/service/api/hrm/roster_abnormal';
import { useRosterStore } from '@/store/modules/roster';

interface IProps {
  rowData: Api.Hrm.SimpleEmployeeInfo;
}
const props = defineProps<IProps>();
const useRoster = useRosterStore();
const { baseInfo } = storeToRefs(useRoster);
type RuleKey = keyof Omit<Api.Hrm.UpdateBlackListReqVO, 'newStatus' | 'idNumber' | 'resignedRemark'>;
const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();
const useInfo = reactive<Partial<Api.Hrm.SimpleEmployeeInfo>>({});
const blockOptions = [
  { label: '是', value: true },
  { label: '否', value: false }
];

const leaveReasonOptions = ref([]);

const rules = ref<Record<RuleKey, App.Global.FormRule>>({
  block: defaultRequiredRule,
  blockReason: defaultRequiredRule,
  resignedReason: defaultRequiredRule,
  leaveDate: defaultRequiredRule
});
const model = reactive<Api.Hrm.UpdateBlackListReqVO>({
  block: false,
  blockReason: null,
  idNumber: null,
  leaveDate: null,
  newStatus: null,
  resignedReason: null,
  resignedRemark: null
});

async function handleSubmit() {
  try {
    await validate();

    await fetchPutEmployeeStatus(props.rowData.id, {
      ...model,
      resignedReason: leaveReasonOptions.value
        .filter(item => model.resignedReason?.includes(item.value))
        .map(item => ({ id: item.value, name: item.label })),
      newStatus: EmployeeStatus.Left
    });

    window.$message?.success('更新成功');
    return true;
  } catch (err) {
    console.error('handleSubmit error:', err);
    return false;
  }
}

async function getLeaveReasonOptions() {
  const { data, error } = await fetchGetLeaveReasonOptions();
  if (error) {
    return;
  }
  leaveReasonOptions.value = (data || []).map(item => ({
    label: item.name,
    value: item.id
  }));
}

function getEmployeeInfo() {
  Object.assign(useInfo, props.rowData);
  model.idNumber = baseInfo.value.idNumber;
}
defineExpose({
  handleSubmit
});
onMounted(async () => {
  await useRoster.handleGetHrmBasicInfo(props.rowData.id as string);
  await getEmployeeInfo();
  restoreValidation();
  getLeaveReasonOptions();
});
</script>

<template>
  <NForm ref="formRef" :model="model" :rules="rules">
    <NDescriptions bordered :columns="5">
      <NDescriptionsItem label="员工姓名">
        <div>{{ baseInfo.name || '--' }}</div>
      </NDescriptionsItem>
      <NDescriptionsItem label="部门">
        <div>{{ baseInfo?.deptName || '--' }}</div>
      </NDescriptionsItem>
      <NDescriptionsItem label="职位">
        <div>{{ baseInfo?.position?.name || '--' }}</div>
      </NDescriptionsItem>
      <NDescriptionsItem label="入职时间">
        <div>{{ baseInfo.entryDate || '--' }}</div>
      </NDescriptionsItem>
      <NDescriptionsItem label="工号">
        <div>{{ useInfo.jobNumber || '--' }}</div>
      </NDescriptionsItem>
    </NDescriptions>

    <NDivider class="!my-[5px]">离职补充信息</NDivider>

    <NFormItem span="24 s:12 m:6" label="离职时间" path="leaveDate" class="pr-24px">
      <DateTimePicker v-model:time="model.leaveDate" label="离职时间" use-for="date" class="w-full" />
    </NFormItem>
    <NFormItem span="24 s:12 m:6" label="离职原因" path="resignedReason" class="pr-24px">
      <NSelect
        v-model:value="model.resignedReason"
        multiple
        clearable
        placeholder="请选择离职原因"
        :options="leaveReasonOptions"
      />
    </NFormItem>
    <NFormItem span="24 s:12 m:6" label="离职原因备注" path="resignedRemark" class="pr-24px">
      <NInput v-model:value="model.resignedRemark" clearable placeholder="请输入离职原因备注" type="textarea" />
    </NFormItem>
    <NDivider class="!my-[5px]">拉黑补充信息</NDivider>
    <NFormItem span="24 s:12 m:6" label="是否拉黑" path="block" class="pr-24px">
      <NRadioGroup v-model:value="model.block" name="radiobuttongroup1">
        <NRadioButton v-for="(item, index) in blockOptions" :key="index" :value="item.value" :label="item.label" />
      </NRadioGroup>
    </NFormItem>
    <NFormItem v-if="model.block" span="24 s:12 m:6" label="身份证号" path="idNumber" class="pr-24px">
      <NInput v-model:value="model.idNumber" clearable placeholder="请输入身份证号" />
    </NFormItem>
    <NFormItem v-if="model.block" span="24 s:12 m:6" label="拉黑原因" path="blockReason" class="pr-24px">
      <NInput v-model:value="model.blockReason" clearable placeholder="请输入拉黑原因" type="textarea" />
    </NFormItem>
  </NForm>
</template>

<style scoped></style>
