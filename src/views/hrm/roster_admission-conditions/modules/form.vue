<script setup lang="tsx">
import { onMounted, reactive, ref } from 'vue';
import { NButton, NInput, NInputNumber } from 'naive-ui';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { getAllPositionOptions, getCompanyOptions } from '@/utils/async-functions';
import SelectWithSearch from '@/components/common/select-with-search.vue';
import {
  fetchGetPromotionConditions,
  fetchPostPromotionConditions,
  fetchPutPromotionConditions
} from '@/service/api/hrm/roster_admission-conditions';
import { $t } from '@/locales';

interface IProps {
  rowData?: Api.Hrm.UpdateOrCreatePromotionConditionsReqVO;
  operateType: NaiveUI.TableOperateType;
}
const props = defineProps<IProps>();
const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const model = reactive<Api.Hrm.UpdateOrCreatePromotionConditionsReqVO>(createModel());
function createModel(): Api.Hrm.UpdateOrCreatePromotionConditionsReqVO {
  return {
    companyId: null,
    employeeName: '',
    idNumber: '',
    id: undefined,
    positionId: null,
    probationPerformanceCriteria: [],
    responsibilities: ['', '', ''],
    company: undefined,
    position: undefined
  };
}
type RuleKey = keyof Pick<
  Api.Hrm.UpdateOrCreatePromotionConditionsReqVO,
  | 'companyId'
  | 'employeeName'
  | 'positionId'
  | 'idNumber'
  | 'responsibilities'
  | 'probationPerformanceCriteria'
  | 'ratingRules'
>;
const rules = ref<Record<RuleKey, App.Global.FormRule>>({
  companyId: defaultRequiredRule,
  employeeName: defaultRequiredRule,
  positionId: defaultRequiredRule,
  idNumber: defaultRequiredRule,
  ratingRules: defaultRequiredRule,
  responsibilities: {
    type: 'array',
    required: true,
    validator(_: any, value: string[]) {
      if (!value.length || value.some(v => !v.trim())) return new Error('请填写岗位职责');
      return true;
    }
  },
  probationPerformanceCriteria: {
    type: 'array',
    required: true,
    validator(_: any, value: Api.Hrm.ProbationPerformanceCriteriaReqVO[]) {
      if (!value.length) return new Error('请填写至少一项绩效考核');
      const total = value.reduce((sum, item) => sum + (item.weight ?? 0), 0);
      for (const item of value) {
        if (!item.name || !item.first || !item.second || !item.third || !item.ratingRules) {
          return new Error('每项必须完整填写');
        }
      }
      if (total !== 100) return new Error('权重总和必须为 100%');

      return true;
    }
  }
});
const columns = [
  {
    title: '考核项目',
    key: 'name',
    render(row: Api.Hrm.ProbationPerformanceCriteriaReqVO, index: number) {
      return (
        <NInput
          value={row.name}
          onUpdateValue={val => (model.probationPerformanceCriteria[index].name = val)}
          type="textarea"
        />
      );
    }
  },
  {
    title: '第一个月',
    key: 'first',
    render(row: Api.Hrm.ProbationPerformanceCriteriaReqVO, index: number) {
      return (
        <NInput
          value={row.first}
          onUpdateValue={val => (model.probationPerformanceCriteria[index].first = val)}
          type="textarea"
        />
      );
    }
  },
  {
    title: '第二个月',
    key: 'second',
    render(row: Api.Hrm.ProbationPerformanceCriteriaReqVO, index: number) {
      return (
        <NInput
          value={row.second}
          onUpdateValue={val => (model.probationPerformanceCriteria[index].second = val)}
          type="textarea"
        />
      );
    }
  },
  {
    title: '第三个月',
    key: 'third',
    render(row: Api.Hrm.ProbationPerformanceCriteriaReqVO, index: number) {
      return (
        <NInput
          value={row.third}
          onUpdateValue={val => (model.probationPerformanceCriteria[index].third = val)}
          type="textarea"
        />
      );
    }
  },
  {
    title: '评分规则',
    key: 'ratingRules',
    render(row: Api.Hrm.ProbationPerformanceCriteriaReqVO, index: number) {
      return (
        <NInput
          value={row.ratingRules}
          onUpdateValue={val => (model.probationPerformanceCriteria[index].ratingRules = val)}
          type="textarea"
        />
      );
    }
  },
  {
    title: '权重（%）',
    key: 'weight',
    render(row: Api.Hrm.ProbationPerformanceCriteriaReqVO, index: number) {
      // 计算当前行最大可填权重 = 100 - 其他行权重之和
      const maxWeight = Math.max(
        0,
        100 -
          model.probationPerformanceCriteria.reduce((sum, item, i) => {
            return i === index ? sum : sum + (item.weight ?? 0);
          }, 0)
      );

      return (
        <NInputNumber
          value={row.weight}
          min={0}
          max={maxWeight}
          onUpdateValue={newVal => {
            const val = newVal ?? 0;
            if (model.probationPerformanceCriteria[index].weight !== val) {
              model.probationPerformanceCriteria[index].weight = val;
            }
          }}
        />
      );
    }
  },
  {
    title: '操作',
    key: 'actions',
    render(_: any, index: number) {
      return (
        <NButton size="small" type="error" onClick={() => model.probationPerformanceCriteria.splice(index, 1)}>
          删除
        </NButton>
      );
    }
  }
];

function addRow() {
  model.probationPerformanceCriteria.push({
    first: '',
    name: '',
    second: '',
    third: '',
    weight: 0,
    ratingRules: ''
  });
}
async function handleSubmit() {
  try {
    await validate();

    let result;
    if (props.operateType === 'edit') {
      result = await fetchPutPromotionConditions(model.id, model);
    } else if (props.operateType === 'add') {
      result = await fetchPostPromotionConditions(model);
    }

    if (result?.error) return false;

    const successMsg = props.operateType === 'edit' ? $t('common.updateSuccess') : $t('common.addSuccess');
    window.$message?.success(successMsg);
    return true;
  } catch (e) {
    console.log(e);
    return false;
  }
}
async function getDetail() {
  try {
    const { data, error } = await fetchGetPromotionConditions(props.rowData?.id);
    if (error) {
      return;
    }
    Object.assign(model, data);
  } catch (e) {
    console.log(e);
  }
}
function initModel() {
  if (props.operateType === 'edit') {
    getDetail();
  } else {
    Object.assign(model, createModel());
  }
}
defineExpose({
  handleSubmit
});
onMounted(() => {
  restoreValidation();
  initModel();
});
</script>

<template>
  <NScrollbar :content-style="{ maxHeight: '60vh' }">
    <NForm ref="formRef" :model="model" :rules="rules" label-placement="top">
      <NFormItem label="公司主体" path="companyId">
        <SelectWithSearch
          v-model:value="model.companyId"
          :api-func="getCompanyOptions"
          :page-size="0"
          placeholder="公司主体"
          :selected-options="[model.company]"
        />
      </NFormItem>
      <NFormItem label="员工姓名" path="employeeName">
        <NInput v-model:value="model.employeeName" placeholder="请输入员工姓名" />
      </NFormItem>
      <NFormItem label="岗位" path="positionId">
        <SelectWithSearch
          v-model:value="model.positionId"
          :api-func="getAllPositionOptions"
          :page-size="0"
          placeholder="职位"
          :selected-options="[model.position]"
        />
      </NFormItem>
      <NFormItem label="身份证号" path="idNumber">
        <NInput v-model:value="model.idNumber" placeholder="请输入身份证号" />
      </NFormItem>

      <NFormItem label="岗位职责" path="responsibilities">
        <NDynamicInput v-model:value="model.responsibilities" placeholder="请输入岗位职责" :min="3" />
      </NFormItem>
      <NFormItem label="试用期绩效考核" path="probationPerformanceCriteria">
        <div class="w-full flex flex-col">
          <NDataTable
            :columns="columns"
            :data="model.probationPerformanceCriteria"
            :pagination="false"
            bordered
            size="small"
          />
          <NButton type="primary" class="mt-2 w-full" ghost size="small" @click="addRow">
            <icon-ic-round-plus class="text-icon" />
            新增考核项
          </NButton>
        </div>
      </NFormItem>
    </NForm>
  </NScrollbar>
</template>
