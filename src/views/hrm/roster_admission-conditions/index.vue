<script lang="tsx" setup>
import { renderModalBtn } from '@/components/re-modal';
import { editRoleBtn } from '@/directives/permission/permi-btn';
import { useTable } from '@/hooks/common/table';
import { useEmployeeSearchForm } from '@/hooks/hrm/useEmployeeSearchForm';
import { $t } from '@/locales';
import { fetchGetPromotionConditionsList } from '@/service/api/hrm/roster_admission-conditions';
import { useAppStore } from '@/store/modules/app';
import { getAllPositionOptions, getCompanyOptions } from '@/utils/async-functions';
import Form from '@/views/hrm/roster_admission-conditions/modules/form.vue';

const appStore = useAppStore();
const {
  columns,
  columnChecks,
  data,
  getData,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams,
  getDataByPage,
  updateSearchParams
} = useTable({
  apiFn: fetchGetPromotionConditionsList,
  showTotal: true,
  apiParams: {
    companyId: null,
    idNumber: null,
    positionId: null,
    name: null
  },
  columns: () => [
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'company.name',
      title: '公司主体',
      align: 'center'
    },
    {
      key: 'employeeName',
      title: '员工姓名',
      align: 'center'
    },
    {
      key: 'position.name',
      title: '职位',
      align: 'center'
    },
    {
      key: 'idNumber',
      title: '身份证号',
      align: 'center'
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 180,
      render: (rowData: Api.Hrm.PromotionConditionsListOrDetailRespVO) => (
        <div class="flex-center gap-8px">
          {editRoleBtn(['hrm:promotion-conditions:update'], () => handleOperate('edit', rowData))}
        </div>
      )
    }
  ]
});

function handleOperate(
  type: NaiveUI.TableOperateType,
  rowData: Api.Hrm.PromotionConditionsListOrDetailRespVO | undefined = undefined
) {
  renderModalBtn(
    Form,
    { operateType: type, rowData },
    {
      title: type === 'add' ? '添加录取条件确认函' : '编辑录取条件确认函',
      style: {
        width: '50%'
      },
      func: getData
    }
  );
}

const { form, searchColumns } = useEmployeeSearchForm<Api.Hrm.PromotionConditionsListOrDetailRespVOParams>({
  searchParams,
  resetSearchParams,
  updateSearchParams,
  getDataByPage,
  newColumns: [
    {
      title: '公司主体',
      path: 'companyId',
      field: 'select-with-search',
      fieldProps: {
        apiFunc: getCompanyOptions,
        selectedOptions: [],
        pageSize: 0,
        placeholder: '公司主体'
      }
    },
    {
      title: '员工姓名',
      path: 'name'
    },
    {
      title: '职位',
      path: 'positionId',
      field: 'select-with-search',
      fieldProps: {
        apiFunc: getAllPositionOptions,
        selectedOptions: [],
        pageSize: 0,
        placeholder: '职位'
      }
    },
    {
      title: '身份证号',
      path: 'idNumber'
    }
  ]
});
</script>

<template>
  <div class="min-h-500px flex-col-stretch overflow-hidden lt-sm:overflow-auto">
    <ProCard class="mb-10px" :show-collapse="false" content-class="!pb-[0px]">
      <ProSearchForm :form="form" :columns="searchColumns" />
    </ProCard>
    <NCard :bordered="false" class="sm:flex-1-hidden card-wrapper" size="small" title="录取条件确认函">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :loading="loading"
          :premission="['hrm:promotion-conditions:create']"
          @refresh="getData"
          @add="handleOperate('add')"
        />
      </template>
      <NDataTable
        :columns="columns"
        :data="data"
        :loading="loading"
        :pagination="mobilePagination"
        :row-key="row => row.id"
        :flex-height="!appStore.isMobile"
        remote
        size="small"
        class="sm:h-full"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
