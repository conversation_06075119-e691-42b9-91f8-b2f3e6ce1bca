<script lang="ts" setup>
import type { TreeSelectOption } from 'naive-ui';
import { NSelect } from 'naive-ui';

import DateTimePicker from '@/components/common/date-time-picker.vue';
import SelectWithSearch from '@/components/common/select-with-search.vue';
import { IsExceptedResultOptions } from '@/enum';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import {
  fetchGetKeyIssuesDetail,
  fetchPostKeyIssuesDetail,
  fetchPutKeyIssuesDetail
} from '@/service/api/kip/key-issues';
import { getBscOptions, getDeptOptions } from '@/utils/async-functions';

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  id?: Api.Kip.KeyIssuesDetail;
}
interface Emits {
  (e: 'btnClose'): void;
}

const emit = defineEmits<Emits>();
type RuleKey = keyof Pick<Api.Kip.KeyIssues, 'name' | 'metricId' | 'year'>;
const props = defineProps<Props>();
const treeSelectOptions = ref<TreeSelectOption[]>([]);
const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();
const rules = ref<Record<RuleKey, App.Global.FormRule>>({
  name: defaultRequiredRule,
  year: defaultRequiredRule,
  metricId: defaultRequiredRule
});

const model = reactive<Api.Kip.KeyIssuesFormParams>(createDefaultModel());

async function handleInitModel() {
  Object.assign(model, createDefaultModel());

  if (props.operateType === 'edit') {
    const { error, data } = await fetchGetKeyIssuesDetail(props.id);
    if (error) {
      return;
    }
    data.withDeptList =
      data.withDept?.map(item => {
        return {
          id: item.id,
          name: item.name
        };
      }) ?? [];
    data.withDept = data.withDept?.map(item => item.id) ?? [];
    data.year = data.year?.toString() ?? '';
    data.deliveredType = data.deliveredType === 0 ? null : data.deliveredType;
    Object.assign(model, data);
    model.id = data.id;
  }
}

function createDefaultModel(): Api.Kip.KeyIssuesFormParams {
  return {
    dept: null,
    name: null,
    deptId: null,
    exceptedResult: null,
    resourceDemand: null,
    deliveredType: null,
    withDept: null,
    metricId: null
  };
}

function showMonthWarningDialog(): Promise<boolean> {
  return new Promise(resolve => {
    window.$dialog?.info({
      title: '温馨提示',
      content: () =>
        h('div', {}, [
          h('div', {}, '关键事项建议避免跨月集中设置，按月拆分并及时更新完成状态，有利于精准追踪、降低遗漏风险。'),
          h('div', {}, '(点击取消返回修改，点击确定提交)')
        ]),
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: () => resolve(true),
      onNegativeClick: () => resolve(false)
    });
  });
}

async function handleSubmit(): Promise<boolean> {
  try {
    await validate();

    const newWithDept = treeSelectOptions.value
      .filter(item => model.withDept?.includes(item.value))
      .map(item => ({
        id: item.value,
        name: item.label,
        type: 'dept'
      }));

    // 弹出确认对话框
    if (Array.isArray(model.months) && model.months.length > 1) {
      const confirm = await showMonthWarningDialog();
      if (!confirm) return false;
    }

    const payload = { ...model, withDept: newWithDept };

    const requestFn = props.operateType === 'add' ? fetchPostKeyIssuesDetail : fetchPutKeyIssuesDetail;

    const { error } = await requestFn(payload);

    if (error) {
      return false;
    }

    const successMsg = props.operateType === 'add' ? $t('common.addSuccess') : $t('common.updateSuccess');

    window?.$message?.success(successMsg);
    emit('btnClose');

    return true;
  } catch (e) {
    return false;
  }
}

async function options() {
  treeSelectOptions.value = await getDeptOptions('');
}

defineExpose({
  handleSubmit
});
onMounted(() => {
  options();
  handleInitModel();
  restoreValidation();
});
</script>

<template>
  <NForm ref="formRef" :model="model" :rules="rules">
    <NGrid responsive="screen" item-responsive x-gap="20">
      <NFormItemGi label="战略指标" path="metricId" span="24 s:12 m:12">
        <SelectWithSearch
          v-model:value="model.metricId"
          :api-func="getBscOptions"
          :page-size="0"
          :selected-options="[model.metric]"
          placeholder="战略指标"
        />
      </NFormItemGi>
      <NFormItemGi label="年度" path="year" span="24 s:12 m:12">
        <div>
          <DateTimePicker v-model:time="model.year" class="w-full" label="年度" use-for="year" />
          <NCheckboxGroup v-model:value="model.months" class="mt-2">
            <NCheckbox v-for="(month, index) in 12" :key="index" :value="month" class="w-20">{{ month }}月</NCheckbox>
          </NCheckboxGroup>
        </div>
      </NFormItemGi>
      <NFormItemGi label="责任部门" path="deptId" span="24 s:12 m:12">
        <DeptTreeSelect v-model:value="model.deptId" placeholder="请选择责任部门" />
      </NFormItemGi>
      <NFormItemGi label="关键事项" path="name" span="24 s:12 m:12">
        <NInput
          v-model:value="model.name"
          clearable
          placeholder="请输入关键事项"
          type="textarea"
          :rows="2"
          :disabled="operateType === 'edit'"
        />
      </NFormItemGi>
      <NFormItemGi label="预期结果" path="exceptedResult" span="24 s:12 m:12">
        <NInput v-model:value="model.exceptedResult" clearable placeholder="请输入预期结果" type="textarea" :rows="2" />
      </NFormItemGi>
      <NFormItemGi label="协同部门" path="withDept" span="24 s:12 m:12">
        <DeptTreeSelect v-model:value="model.withDept" multiple checkable placeholder="请选择责任部门" />
      </NFormItemGi>
      <NFormItemGi v-if="operateType === 'edit'" label="是否达成预期结果" path="deliveredType" span="24 s:12 m:12">
        <NSelect
          v-model:value="model.deliveredType"
          :options="IsExceptedResultOptions"
          class="w-full"
          clearable
          placeholder="请选择"
        />
      </NFormItemGi>
      <NFormItemGi label="资源需求" path="resourceDemand" span="24 s:12 m:12">
        <NInput v-model:value="model.resourceDemand" clearable placeholder="请输入资源需求" type="textarea" :rows="2" />
      </NFormItemGi>
      <NFormItemGi v-if="operateType === 'edit'" label="达成预期结果说明" path="deliveryOutcome" span="24 s:12 m:12">
        <NInput
          v-model:value="model.deliveryOutcome"
          clearable
          placeholder="请输入达成预期结果说明"
          type="textarea"
          :rows="2"
        />
      </NFormItemGi>
    </NGrid>
  </NForm>
</template>

<style scoped></style>
