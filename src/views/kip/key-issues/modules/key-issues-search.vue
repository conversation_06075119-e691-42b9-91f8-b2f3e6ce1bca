<script lang="ts" setup>
import { useNaiveForm } from '@/hooks/common/form';
import { getBscOptions } from '@/utils/async-functions';
import SelectWithSearch from '@/components/common/select-with-search.vue';
import DateTimePicker from '@/components/common/date-time-picker.vue';
import { BscStatusOptions } from '@/enum';

defineOptions({
  name: 'KeyIssuesSearch'
});

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

const emit = defineEmits<Emits>();
const { formRef, validate, restoreValidation } = useNaiveForm();
const model = defineModel<Api.Kip.KeyIssuesFormSearch>('model', { required: true });
const initialParams = { ...model.value };

async function reset() {
  await restoreValidation();
  Object.assign(model.value, initialParams);
  emit('reset');
}

async function search() {
  await validate();
  emit('search');
}
</script>

<template>
  <NCard :bordered="false" class="card-wrapper" size="small">
    <NCollapse>
      <NCollapseItem name="search" title="搜索">
        <NForm ref="formRef" :label-width="80" class="mb--12px" :model="model" label-placement="left">
          <NGrid item-responsive responsive="screen">
            <NFormItemGi key="year" class="pr-24px" label="年度" span="24 s:12 m:6">
              <DateTimePicker v-model:time="model.year" class="w-full" label="年度" use-for="year" :clearable="false" />
            </NFormItemGi>
            <NFormItemGi key="metricId" class="pr-24px" label="战略指标" span="24 s:12 m:6">
              <SelectWithSearch
                v-model:value="model.metricId"
                :api-func="getBscOptions"
                :page-size="0"
                :selected-options="[]"
                placeholder="战略指标"
              />
            </NFormItemGi>
            <NFormItemGi key="metricId" class="pr-24px" label="层面" span="24 s:12 m:6">
              <NSelect v-model:value="model.level" :options="BscStatusOptions" placeholder="请选择层面" />
            </NFormItemGi>
            <NFormItemGi class="pr-24px" label="关键事项" path="name" span="24 s:12 m:6">
              <NInput v-model:value="model.name" placeholder="请输入关键事项" />
            </NFormItemGi>
            <NFormItemGi class="pr-24px" label="责任部门" path="deptId" span="24 s:12 m:6">
              <DeptTreeSelect v-model:value="model.deptId" placeholder="请选择责任部门" />
            </NFormItemGi>
            <NFormItemGi class="pr-24px" span="24 m:12">
              <NSpace class="w-full" justify="end">
                <NButton @click="reset">
                  <template #icon>
                    <icon-ic-round-refresh class="text-icon" />
                  </template>
                  {{ $t('common.reset') }}
                </NButton>
                <NButton ghost type="primary" @click="search">
                  <template #icon>
                    <icon-ic-round-search class="text-icon" />
                  </template>
                  {{ $t('common.search') }}
                </NButton>
              </NSpace>
            </NFormItemGi>
          </NGrid>
        </NForm>
      </NCollapseItem>
    </NCollapse>
  </NCard>
</template>

<style scoped>
:deep(.n-collapse-item__content-inner) {
  padding-top: 12px !important;
}
</style>
