<script lang="tsx" setup>
import dayjs from 'dayjs';
import type { DataTableColumns } from 'naive-ui';
import { NButton, NScrollbar, NTag, NText, NTooltip } from 'naive-ui';
import { computed, h, reactive, ref } from 'vue';

import keyIssuesForm from './modules/key-issues-form.vue';

import svgIcon from '@/components/custom/svg-icon.vue';
import { renderModalBtn } from '@/components/re-modal';
import { BscStatusOptions } from '@/enum';
import { fetchGetKeyIssuesList } from '@/service/api/kip/key-issues';
import { useAppStore } from '@/store/modules/app';
import { rowSpan } from '@/utils/agent';
import { getEnumValue } from '@/utils/useful_func';
import BscDisassembleForm from '@/views/kip/bsc/modules/bsc-disassemble-form.vue';
import Search from '@/views/kip/key-issues/modules/key-issues-search.vue';
const searchParams = reactive<Api.Kip.KeyIssuesFormSearch>({
  name: null,
  deptId: null,
  metricId: null,
  level: null,
  year: String(dayjs().format('YYYY'))
});

const loading = ref(false);
const tableData = ref<Api.Kip.KeyIssuesList>([]);
const appStore = useAppStore();
const tabValue = ref('ALL');
const songs = [
  {
    value: 'ALL',
    label: '完整内容',
    title: '所有关键事项'
  },
  {
    value: 'WEEK',
    label: '近一周内更新',
    title: '一周内有新增或更新的关键事项'
  },
  {
    value: 'EXPIRED',
    label: '过期未更新',
    title: '截止到本月仍为标记是否达成预期的关键事项'
  },
  {
    value: 'MONTH',
    label: '本月关键事项',
    title: '规划在本月的关键事项'
  }
];
const columns = computed<DataTableColumns<Api.Kip.KeyIssues>>(() => {
  const baseColumns: DataTableColumns<Api.Kip.KeyIssues> = [
    {
      title: '部门',
      key: 'deptName',
      align: 'center',
      width: 20,
      rowSpan:
        tabValue.value === 'ALL'
          ? (rowData, rowIndex) => rowSpan('deptName', rowIndex, rowData, tableData.value)
          : undefined,
      render: rowData =>
        h(
          'div',
          {
            style: {
              position: 'sticky',
              top: '50px',
              bottom: '50px',
              zIndex: 10,
              padding: '4px 0'
            }
          },
          rowData.deptName || ''
        )
    },
    {
      title: '层面',
      key: 'metric.level',
      align: 'center',
      width: 20,
      rowSpan: (rowData, rowIndex) => rowSpan('metric.level', rowIndex, rowData, tableData.value),
      render: rowData =>
        h(
          'div',
          {
            style: {
              position: 'sticky',
              top: '50px',
              bottom: '50px',
              zIndex: 10,
              padding: '4px 0'
            }
          },
          getEnumValue(BscStatusOptions, rowData?.metric?.level) || ''
        )
    },
    {
      title: () =>
        h('div', { style: { display: 'inline-flex', alignItems: 'center', gap: '4px' } }, [
          '战略指标',
          h(NTooltip, null, {
            trigger: () =>
              h('span', { style: { display: 'inline-flex', alignItems: 'center' } }, [
                h(svgIcon, { icon: 'ic-sharp-help', style: { fontSize: '18px' } })
              ]),
            default: () => '点击可对关键事项拆解或排序'
          })
        ]),
      key: 'metric.name',
      align: 'center',
      width: 90,
      rowSpan: (rowData, rowIndex) => rowSpan('metric.name', rowIndex, rowData, tableData.value),
      render: rowData =>
        h(
          'div',
          {
            style: { position: 'sticky', top: '50px', bottom: '50px', zIndex: 10, padding: '4px 0' }
          },
          [
            h(NScrollbar, { contentStyle: { maxHeight: '120px' } }, () =>
              h(
                'div',
                {
                  class: 'cursor-pointer p-4px',
                  style: { textAlign: 'center', display: 'block' },
                  onClick: () => handleDisassemble(rowData)
                },
                rowData.metric?.sort ? `${rowData.metric?.sort}、${rowData.metric?.name}` : rowData.metric?.name
              )
            )
          ]
        )
    },
    {
      title: '战略指标达成的全面规划',
      key: 'allIn',
      align: 'center',
      children: [
        {
          title: () =>
            h('div', { style: { display: 'inline-flex', alignItems: 'center', gap: '4px' } }, [
              '关键事项',
              h(NTooltip, null, {
                trigger: () =>
                  h('span', { style: { display: 'inline-flex', alignItems: 'center' } }, [
                    h(svgIcon, { icon: 'ic-sharp-help', style: { fontSize: '18px' } })
                  ]),
                default: () => '点击可编辑关键事项；如需新增，请基于BSC指标进行拆解添加'
              })
            ]),
          key: 'name',
          align: 'center',
          width: 260,
          render: rowData =>
            h(NScrollbar, { contentStyle: { maxHeight: '80px' } }, () =>
              h(NText, { type: 'info' }, () =>
                h(
                  'div',
                  {
                    class: 'cursor-pointer p-4px',
                    style: { textAlign: 'left', display: 'block' },
                    onClick: () => handleEdit(rowData.id)
                  },
                  `${rowData.issueOrder ? `${rowData.issueOrder}、` : ''}${rowData.name}`
                )
              )
            )
        },
        {
          title: '预期达成的效果',
          align: 'center',
          width: 120,
          key: 'exceptedResult',
          render: rowData => {
            const isRecent = rowData.updateDate && dayjs().diff(dayjs(rowData.updateDate), 'day') <= 7;
            const content = h(
              'div',
              {
                class: 'p-4px',
                style: { textAlign: 'left', display: 'block', color: isRecent ? 'red' : undefined }
              },
              rowData.exceptedResult
            );
            return h(NScrollbar, { contentStyle: { maxHeight: '80px' } }, () =>
              isRecent
                ? h(
                    NTooltip,
                    {
                      trigger: 'hover',
                      placement: 'top'
                    },
                    {
                      default: () => `最后更新时间：${rowData.updateDate}`,
                      trigger: () => content
                    }
                  )
                : content
            );
          }
        },
        {
          title: '资源需求',
          align: 'center',
          width: 120,
          key: 'resourceDemand',
          render: rowData =>
            h(NScrollbar, { contentStyle: { maxHeight: '80px' } }, () =>
              h(
                'div',
                {
                  style: { textAlign: 'left', display: 'block' },
                  class: 'p-4px'
                },
                rowData.resourceDemand
              )
            )
        },
        {
          title: '协同部门',
          align: 'center',
          width: 100,
          key: 'withDept',
          render: rowData =>
            h(
              'span',
              {
                class: 'p-4px',
                style: { textAlign: 'left', display: 'block' }
              },
              rowData.withDept?.map(item => item.name).join(',') || ''
            )
        }
      ]
    }
  ];

  baseColumns.push({
    title: '行事历',
    key: 'allIn-name',
    align: 'center',
    children: Array.from({ length: 12 }, (_, i) => {
      const month = i + 1;
      return {
        title: `${month}月`,
        key: `month${month}`,
        align: 'center',
        width: 30,
        render: rowData => renderMonthCell(month, rowData)
      };
    })
  });

  return baseColumns;
});
const otherColumns = computed<DataTableColumns<Api.Kip.KeyIssues>>(() => {
  const baseColumns: DataTableColumns<Api.Kip.KeyIssues> = [
    {
      title: '部门',
      key: 'deptName',
      align: 'center',
      width: 100,

      render: rowData => h('div', {}, rowData.deptName || '')
    },
    {
      title: '层面',
      key: 'metric.level',
      align: 'center',
      width: 100,
      render: rowData => h('div', {}, getEnumValue(BscStatusOptions, rowData?.metric?.level) || '')
    },
    {
      title: () =>
        h('div', { style: { display: 'inline-flex', alignItems: 'center', gap: '4px' } }, [
          '战略指标',
          h(NTooltip, null, {
            trigger: () =>
              h('span', { style: { display: 'inline-flex', alignItems: 'center' } }, [
                h(svgIcon, { icon: 'ic-sharp-help', style: { fontSize: '18px' } })
              ]),
            default: () => '点击可对关键事项拆解或排序'
          })
        ]),
      key: 'metric.name',
      align: 'center',
      width: 90,
      render: rowData =>
        h('div', {}, [
          h(NScrollbar, { contentStyle: { maxHeight: '120px' } }, () =>
            h(
              'div',
              {
                class: 'cursor-pointer p-4px',
                style: { textAlign: 'center', display: 'block' },
                onClick: () => handleDisassemble(rowData)
              },
              rowData.metric?.sort ? `${rowData.metric?.sort}、${rowData.metric?.name}` : rowData.metric?.name
            )
          )
        ])
    },
    {
      title: '战略指标达成的全面规划',
      key: 'allIn',
      align: 'center',
      children: [
        {
          title: () =>
            h('div', { style: { display: 'inline-flex', alignItems: 'center', gap: '4px' } }, [
              '关键事项',
              h(NTooltip, null, {
                trigger: () =>
                  h('span', { style: { display: 'inline-flex', alignItems: 'center' } }, [
                    h(svgIcon, { icon: 'ic-sharp-help', style: { fontSize: '18px' } })
                  ]),
                default: () => '点击可编辑关键事项；如需新增，请基于BSC指标进行拆解添加'
              })
            ]),
          key: 'name',
          align: 'center',
          width: 260,
          render: rowData =>
            h(NScrollbar, { contentStyle: { maxHeight: '80px' } }, () =>
              h(NText, { type: 'info' }, () =>
                h(
                  'div',
                  {
                    class: 'cursor-pointer p-4px',
                    style: { textAlign: 'left', display: 'block' },
                    onClick: () => handleEdit(rowData.id)
                  },
                  `${rowData.issueOrder ? `${rowData.issueOrder}、` : ''}${rowData.name}`
                )
              )
            )
        },
        {
          title: '预期达成的效果',
          align: 'center',
          width: 120,
          key: 'exceptedResult',
          render: rowData => {
            const content = h(
              'div',
              {
                class: 'p-4px',
                style: { textAlign: 'left', display: 'block' }
              },
              rowData.exceptedResult
            );
            return h(NScrollbar, { contentStyle: { maxHeight: '80px' } }, content);
          }
        },
        {
          title: '资源需求',
          align: 'center',
          width: 120,
          key: 'resourceDemand',
          render: rowData =>
            h(NScrollbar, { contentStyle: { maxHeight: '80px' } }, () =>
              h(
                'div',
                {
                  style: { textAlign: 'left', display: 'block' },
                  class: 'p-4px'
                },
                rowData.resourceDemand
              )
            )
        },
        {
          title: '协同部门',
          align: 'center',
          width: 100,
          key: 'withDept',
          render: rowData =>
            h(
              'span',
              {
                class: 'p-4px',
                style: { textAlign: 'left', display: 'block' }
              },
              rowData.withDept?.map(item => item.name).join(',') || ''
            )
        },
        {
          title: '更新时间',
          key: 'updateDate',
          align: 'center',
          width: 100,
          render: rowData => h('div', { style: { textAlign: 'center' } }, rowData.updateDate || '-')
        }
      ]
    },
    {
      title: '月份',
      key: 'month',
      width: 100,
      align: 'center',
      render: rowData =>
        h(
          'div',
          { style: { textAlign: 'center' } },
          rowData.months?.map(item =>
            h(
              NTag,
              { type: 'info', size: 'small', style: { marginRight: '4px', marginBottom: '4px' } },
              () => `${item}月`
            )
          ) || '-'
        )
    }
  ];

  return baseColumns;
});

function renderMonthCell(month: number, rowData: Api.Kip.KeyIssues) {
  const isActive = rowData.months?.includes(month);
  let backgroundColor = '';

  if (isActive) {
    switch (rowData.deliveredType) {
      case 1:
        backgroundColor = '#fff7d6'; // 黄色
        break;
      case 2:
        backgroundColor = '#d6fdd6'; // 绿色
        break;
      case 3:
        backgroundColor = '#ffd6d6'; // 红色
        break;
      default:
        backgroundColor = '';
    }
  }

  return (
    <div
      style={{
        width: '100%',
        height: '100%',
        paddingTop: '5px',
        paddingBottom: '5px',
        paddingLeft: '0px',
        borderRadius: '5px',
        backgroundColor
      }}
    ></div>
  );
}

// 新增
function handleAdd() {
  renderModalBtn(
    keyIssuesForm,
    { operateType: 'add' },
    {
      title: '新增关键事项',
      style: {
        width: '50%'
      },
      func: getData
    }
  );
}

// 修改
function handleEdit(id: number) {
  renderModalBtn(
    keyIssuesForm,
    { operateType: 'edit', id },
    {
      title: '编辑关键事项',
      style: {
        width: '50%'
      },
      func: getData
    }
  );
}

// 拆解关键事项
function handleDisassemble(row: Api.Kip.KeyIssues) {
  const { id, name } = row.metric;
  renderModalBtn(
    BscDisassembleForm,
    { id, metricName: name },
    {
      title: '拆解关键事项',
      style: {
        width: '50%'
      },
      func: getData
    }
  );
}

function addIssueOrder(data: Api.Kip.KeyIssues[]) {
  const grouped = new Map<string, number>();

  return data.map(item => {
    const metricKey = item.metric?.name || 'unknown';
    const count = grouped.get(metricKey) || 0;
    grouped.set(metricKey, count + 1);
    return {
      ...item,
      issueOrder: count + 1
    };
  });
}

// 获取数据
async function getData() {
  try {
    loading.value = true;
    const { data, error } = await fetchGetKeyIssuesList({ ...searchParams, tab: tabValue.value });
    if (error) {
      tableData.value = [];
      return;
    }
    tableData.value = addIssueOrder(data || []);
  } catch (e: any) {
    throw e.message;
  } finally {
    loading.value = false;
  }
}

function resetSearchParams() {
  Object.assign(searchParams, {});
  getData();
}

const topTitle = computed(() => {
  if (!searchParams.year) {
    return '关键事项';
  }
  return `${dayjs(searchParams.year).format('YYYY年')}关键事项`;
});
getData();
watch(tabValue, () => {
  getData();
});
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <Search v-model:model="searchParams" @reset="resetSearchParams" @search="getData" />

    <NCard :bordered="false" class="relative sm:flex-1-hidden card-wrapper" size="small" :title="topTitle">
      <template #header-extra>
        <NSpace align="center" wrap justify="end" class="lt-sm:w-200px">
          <NButton size="small" ghost type="primary" @click="handleAdd">
            <template #icon>
              <icon-ic-round-plus class="text-icon" />
            </template>
            {{ $t('common.add') }}
          </NButton>
          <NButton size="small" @click="getData">
            <template #icon>
              <icon-mdi-refresh class="text-icon" :class="{ 'animate-spin': loading }" />
            </template>
            {{ $t('common.refresh') }}
          </NButton>
        </NSpace>
      </template>
      <NRadioGroup v-model:value="tabValue" class="absolute left-50 top-2">
        <NRadioButton v-for="song in songs" :key="song.value" :value="song.value" :label="song.label">
          <div class="flex items-center gap-4px">
            {{ song.label }}
            <FormTip :content="song.title" />
          </div>
        </NRadioButton>
      </NRadioGroup>

      <NDataTable
        v-show="tabValue === 'ALL'"
        :single-line="false"
        class="w-full sm:h-full"
        :flex-height="!appStore.isMobile"
        :loading="loading"
        :columns="columns"
        :data="tableData"
        :row-key="row => row.id"
        :scroll-x="1160"
      />
      <NDataTable
        v-show="tabValue !== 'ALL'"
        :single-line="false"
        class="w-full sm:h-full"
        :flex-height="!appStore.isMobile"
        :loading="loading"
        :columns="otherColumns"
        :data="tableData"
        :row-key="row => row.id"
      />
    </NCard>
  </div>
</template>

<style scoped>
:deep(.n-data-table .n-data-table-td) {
  padding: 2px !important;
  height: 80px;
}
:deep(.n-data-table .n-data-table-tbody .n-data-table-tr) {
  height: 50px;
}
:deep(.n-data-table .n-data-table-th) {
  padding: 5px !important;
}
</style>
