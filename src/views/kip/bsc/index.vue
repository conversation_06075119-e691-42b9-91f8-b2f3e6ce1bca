<script lang="tsx" setup>
import { computed, h, reactive, ref } from 'vue';
import type { DataTableColumns } from 'naive-ui';
import { NButton, NPopconfirm, NScrollbar } from 'naive-ui';
import dayjs from 'dayjs';
import BscSearch from '@/views/kip/bsc/modules/bsc-search.vue';
import { useAppStore } from '@/store/modules/app';
import { fetchGetBscList, fetchRestoreBsc } from '@/service/api/kip/bsc';
import { getEnumValue } from '@/utils/useful_func';
import { BscOptions, BscStatusOptions, StatisticCycleOptions } from '@/enum';
import { renderModalBtn } from '@/components/re-modal';
import { $t } from '@/locales';
import BscForm from './modules/bsc-form.vue';
import BscTable from './modules/bsc-table.vue';
import BscDisassembleForm from './modules/bsc-disassemble-form.vue';
import BscDeleteForm from './modules/bsc-delete-form.vue';

const searchParams = reactive<Api.Kip.BscFormParams>({
  target: null!,
  userId: null!,
  deptId: null!,
  invalid: null!,
  metricName: null!,
  year: String(dayjs().format('YYYY'))
});
const loading = ref(false);
const tableData = ref<Api.Kip.BscList>([]);
const appStore = useAppStore();
const columns = ref<DataTableColumns<Api.Kip.BscParams>>([
  {
    type: 'expand',
    renderExpand: (rowData: Api.Kip.BscFormParams) => {
      return h(BscTable, { rowData, year: Number(searchParams.year) });
    }
  },
  {
    title: '层面',
    key: 'level',
    align: 'center',
    render: (rowData: Api.Kip.BscParams) => {
      return <div>{getEnumValue(BscStatusOptions, rowData.levelId)}</div>;
    }
  },
  {
    title: '战略目标',
    key: 'targetName',
    align: 'center',
    render: (rowData: Api.Kip.BscParams) => {
      return (
        <NScrollbar content-style={{ maxHeight: '50px' }}>
          <div style={{ textAlign: 'left', display: 'block' }}>{rowData.targetName}</div>
        </NScrollbar>
      );
    }
  },
  {
    title: '战略指标',
    key: 'metricName',
    align: 'center',
    render: (rowData: Api.Kip.BscParams) => {
      return (
        <NScrollbar content-style={{ maxHeight: '50px' }}>
          <div class="flex items-center justify-center gap-1">
            <span class="w-25px">
              {rowData.haveKeyIssues ? <icon-pajamas-issue-type-feature-flag class="text-20px text-red" /> : null}
            </span>
            <span class="text-center">
              {rowData.sort ? `${rowData.sort}、` : ''}
              {rowData.metricName}
            </span>
          </div>
        </NScrollbar>
      );
    }
  },
  {
    title: '计算口径',
    key: 'formula',
    align: 'center',
    render: (rowData: Api.Kip.BscParams) => {
      return (
        <NScrollbar content-style={{ maxHeight: '50px' }}>
          <div>{rowData.formula || '-'}</div>
        </NScrollbar>
      );
    }
  },
  {
    title: '责任部门',
    key: 'deptId',
    align: 'center',
    render: (rowData: Api.Kip.BscParams) => {
      if (rowData.dept) {
        const dept =
          rowData.dept
            ?.map(item => item.name)
            .filter(name => Boolean(name))
            .join(',') || '-';
        return (
          <NScrollbar content-style={{ maxHeight: '50px' }}>
            <div class="cursor-pointer">{dept}</div>
          </NScrollbar>
        );
      }
      return '-';
    }
  },
  {
    title: '责任人',
    key: 'userId',
    align: 'center',
    render: (rowData: Api.Kip.BscParams) => {
      if (rowData.user) {
        const name = rowData.user?.map(item => item.name).join(',') || '-';
        return (
          <NScrollbar content-style={{ maxHeight: '50px' }}>
            <div>{name}</div>
          </NScrollbar>
        );
      }
      return '-';
    }
  },
  {
    title: '数据输出部门',
    key: 'dataOutputDept',
    align: 'center',
    render: (rowData: Api.Kip.BscParams) => {
      return (
        <NScrollbar content-style={{ maxHeight: '50px' }}>
          <div>{rowData.dataOutputDept || '-'}</div>
        </NScrollbar>
      );
    }
  },
  {
    title: '数据周期',
    key: 'statisticCycle',
    align: 'center',
    render: (rowData: Api.Kip.BscParams) => {
      return <div>{getEnumValue(StatisticCycleOptions, rowData.statisticCycle)}</div>;
    }
  },
  {
    title: '单位',
    key: 'label',
    align: 'center',
    render: (rowData: Api.Kip.BscParams) => {
      return <div>{rowData.label ? getEnumValue(BscOptions, rowData.label) : '-'} </div>;
    }
  },
  {
    title: '操作',
    align: 'center',
    key: 'action',
    width: 140,
    render: (row: any) => {
      return (
        <div class="flex-center gap-8px">
          <NButton type="primary" size="small" ghost onClick={() => handleEdit(row.id)}>
            {$t('common.edit')}
          </NButton>
          {!row.invalid ? (
            <NButton type="error" size="small" ghost onClick={() => handleDelete(row)}>
              失效
            </NButton>
          ) : (
            <NPopconfirm onPositiveClick={() => handleRestore(row.id)}>
              {{
                default: () => '是否恢复此数据',
                trigger: () => (
                  <NButton type="success" ghost size="small">
                    恢复
                  </NButton>
                )
              }}
            </NPopconfirm>
          )}
        </div>
      );
    }
  }
]);
const expandedRowKeys = ref<number[]>([]);
const isAllExpanded = computed(() => {
  return expandedRowKeys.value.length === tableData.value.length;
});
// 一键切换展开/折叠
const toggleExpandAll = () => {
  if (isAllExpanded.value) {
    expandedRowKeys.value = [];
  } else {
    // 获取所有行的 id
    expandedRowKeys.value = tableData.value.map((item: Api.Kip.BscParams) => item.id);
  }
};
const handleExpand = keys => {
  expandedRowKeys.value = keys;
};

function handleDelete(row: Api.Kip.BscParams) {
  renderModalBtn(
    BscDeleteForm,
    { id: row.id, haveKeyIssues: row.haveKeyIssues, metricName: row.metricName },
    {
      title: '删除BSC指标',
      style: {
        width: '30%'
      },
      func: getData
    }
  );
}
// 恢复
async function handleRestore(id: number) {
  try {
    const { error } = await fetchRestoreBsc(id);
    if (error) return;
    window?.$message?.success('恢复成功');
    getData();
  } catch (error) {
    console.log(error);
  }
}
// 编辑
function handleEdit(id: number) {
  renderModalBtn(
    BscForm,
    { operateType: 'edit', id, year: searchParams.year },
    {
      title: '编辑BSC指标',
      style: {
        width: '50%'
      },
      func: getData
    }
  );
}
// 拆解关键事项
function handleDisassemble(row) {
  renderModalBtn(
    BscDisassembleForm,
    { id: row.id, metricName: row.metricName },
    {
      title: '拆解关键事项',
      style: {
        width: '50%'
      },
      func: getData
    }
  );
}
// 新增
function handleAdd() {
  renderModalBtn(
    BscForm,
    { operateType: 'add', year: searchParams.year },
    {
      title: '添加BSC指标',
      style: {
        width: '40%'
      },
      func: getData
    }
  );
}
// 获取数据
async function getData() {
  try {
    loading.value = true;
    const { userId, target, deptId, metricName, year, invalid } = searchParams;
    const { data, error } = await fetchGetBscList({
      userId: userId?.join(','),
      target,
      deptId: deptId?.join(','),
      metricName,
      invalid,
      year
    });
    if (error) {
      tableData.value = [];
    }
    tableData.value = data || [];
  } catch (e: any) {
    throw e.message;
  } finally {
    loading.value = false;
  }
}

const topTitle = computed(() => {
  if (!searchParams.year) {
    return 'BSC指标';
  }
  return `${dayjs(searchParams.year).format('YYYY年')} BSC指标`;
});
getData();
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <BscSearch v-model:model="searchParams" @reset="getData" @search="getData" />
    <NCard :bordered="false" class="sm:flex-1-hidden card-wrapper" size="small" :title="topTitle">
      <template #header-extra>
        <NSpace align="center" wrap justify="end" class="lt-sm:w-200px">
          <NButton size="small" ghost type="primary" @click="handleAdd">
            <template #icon>
              <icon-ic-round-plus class="text-icon" />
            </template>
            {{ $t('common.add') }}
          </NButton>
          <NButton size="small" ghost type="primary" @click="toggleExpandAll">
            <template #icon>
              <icon-ic-round-sort class="text-icon" />
            </template>
            展开/折叠
          </NButton>
          <NButton size="small" @click="getData">
            <template #icon>
              <icon-mdi-refresh class="text-icon" :class="{ 'animate-spin': loading }" />
            </template>
            {{ $t('common.refresh') }}
          </NButton>
        </NSpace>
      </template>
      <NDataTable
        :single-line="false"
        class="sm:h-full"
        :flex-height="!appStore.isMobile"
        :loading="loading"
        :columns="columns"
        :data="tableData"
        :row-key="row => row.id"
        size="small"
        :expanded-row-keys="expandedRowKeys"
        @update:expanded-row-keys="handleExpand"
      />
    </NCard>
  </div>
</template>

<style scoped>
::v-deep(.n-data-table .n-data-table-td) {
  padding: 5px !important;
}
</style>
