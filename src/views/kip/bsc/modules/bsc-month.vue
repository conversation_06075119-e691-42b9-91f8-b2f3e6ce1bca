<script lang="ts" setup>
import { onMounted, reactive } from 'vue';
import { useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { fetchUpdateYtd } from '@/service/api/kip/bsc';

interface Props {
  /** the edit row data */
  id: number;
  ytdDeviation: string;
  ytdTotal: string;
}
const props = defineProps<Props>();
const { formRef, validate } = useNaiveForm();

const model = reactive<Api.Kip.UpdateYtd>(createDefaultModel());

async function handleInitModel() {
  Object.assign(model, createDefaultModel());
  model.id = props.id;
  model.deviation = props.ytdDeviation ?? '';
  model.total = props.ytdTotal ?? '';
}

function createDefaultModel(): Api.Kip.UpdateYtd {
  return {
    id: null!,
    deviation: null!,
    total: null!
  };
}

async function handleSubmit() {
  try {
    await validate();
    const { error } = await fetchUpdateYtd(model.id, model);
    if (error) {
      return false;
    }
    window?.$message?.success($t('common.addSuccess'));
    return true;
  } catch {
    return false;
  }
}

defineExpose({
  handleSubmit
});
onMounted(() => {
  handleInitModel();
});
</script>

<template>
  <NForm ref="formRef" :model="model">
    <NFormItem label="截至本月累计" path="total">
      <NInput v-model:value="model.total" clearable placeholder="请输入截至本月累计" />
    </NFormItem>
    <NFormItem label="截至本月偏差" path="deviation">
      <NInput v-model:value="model.deviation" clearable placeholder="请输入截至本月偏差" />
    </NFormItem>
  </NForm>
</template>

<style scoped></style>
