<script setup lang="tsx">
import { nextTick, onMounted, ref } from 'vue';
import { renderModalBtn } from '@/components/re-modal';
import { fetchGetBscMetric } from '@/service/api/kip/bsc';
import { BscOptions } from '../../../../enum';
import { getEnumValue } from '../../../../utils/useful_func';
import bscMetric from './bsc-metric.vue';
import bscActual from './bsc-actual.vue';
import bscMonth from './bsc-month.vue';

interface Props {
  year: number;
  rowData: Api.Kip.BscParams;
}
const props = defineProps<Props>();
const loading = ref(false);
const tableData = ref<Api.Kip.MetricInfoRespList>([]);
const ytdDeviation = ref();
const ytdTotal = ref();
async function loadData() {
  try {
    loading.value = true;
    const { data, error } = await fetchGetBscMetric(props.rowData.id);
    if (error) {
      tableData.value = [];
      return;
    }
    tableData.value = data.data || [];
    ytdDeviation.value = data.ytdDeviation;
    ytdTotal.value = data.ytdTotal;
  } catch (err) {
    console.error('数据加载失败:', err);
  } finally {
    loading.value = false;
  }
}
// 截止本月修改
function changeMonth() {
  renderModalBtn(
    bscMonth,
    { id: props.rowData.id, ytdDeviation: ytdDeviation.value, ytdTotal: ytdTotal.value },
    {
      title: '截止本月数值',
      style: {
        width: '30%'
      },
      func: loadData
    }
  );
}
// 实际配置
function handleMetric() {
  renderModalBtn(
    bscMetric,
    { rowData: props.rowData, year: Number(props.year) },
    {
      title: '目标配置',
      style: {
        width: '30%'
      },
      func: loadData
    }
  );
}

// 完成值配置
function handleActual(rowData: Api.Kip.MetricInfoRespVo) {
  renderModalBtn(
    bscActual,
    { statisticCycle: props.rowData.statisticCycle, rowData, parentId: props.rowData.id, label: props.rowData.label },
    {
      title: '完成值配置',
      style: {
        width: '50%'
      },
      func: loadData
    }
  );
}
onMounted(() => {
  nextTick(() => {
    loadData();
  });
});
</script>

<template>
  <NSpace align="center" class="rounded-md bg-gray-50 p-3">
    <NButton type="primary" size="small" @click="handleMetric">目标配置</NButton>
    <div class="flex cursor-pointer items-center p-2 space-x-4" @click="changeMonth">
      <div class="flex items-center space-x-1">
        <div class="text-gray-800">截止本月的完成值：</div>
        <div class="text-blue-600 font-semibold">
          {{ ytdTotal ?? '-' }}{{ getEnumValue(BscOptions, rowData.label) }}
        </div>
      </div>

      <div class="h-5 border-l border-gray-300"></div>

      <div class="flex items-center space-x-1">
        <div class="text-gray-800">截止本月的偏差值：</div>
        <div class="text-red-600 font-semibold">
          {{ ytdDeviation ?? '-' }}{{ getEnumValue(BscOptions, rowData.label) }}
        </div>
      </div>
    </div>
  </NSpace>
  <template v-if="tableData.length">
    <NTable single-column :single-line="false" class="mt-2">
      <thead>
        <tr>
          <th v-for="item in tableData" :key="item.id" class="!w-[90px]">{{ item.year }}年{{ item.month }}月</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td v-for="item in tableData" :key="item.id" class="cursor-pointer !w-[90px]" @click="handleActual(item)">
            <div v-if="item.actual">
              <span>完成值：</span>
              <span>{{ item.actual }}{{ getEnumValue(BscOptions, rowData.label) }}</span>
            </div>
            <div v-if="item.excepted">
              <span>目标值：</span>
              <span>{{ item.excepted }}{{ getEnumValue(BscOptions, rowData.label) }}</span>
            </div>
            <div v-if="item.deviation">
              <span>偏差值：</span>
              <span>{{ item.deviation }}{{ getEnumValue(BscOptions, rowData.label) }}</span>
            </div>
          </td>
        </tr>
      </tbody>
    </NTable>
  </template>
</template>
