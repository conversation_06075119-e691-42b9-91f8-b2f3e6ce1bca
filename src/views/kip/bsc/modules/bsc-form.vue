<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue';
import { NForm, NSelect } from 'naive-ui';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { BscOptions, BscStatusOptions, StatisticCycleOptions } from '@/enum';
import { fetchGetBsc, fetchPostBsc, fetchPutBsc } from '@/service/api/kip/bsc';
import { getUserOptions } from '@/utils/async-functions';
import SelectWithSearch from '@/components/common/select-with-search.vue';

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  id?: Api.Kip.BscParams['id'];
  year: number;
}

interface Emits {
  (e: 'btnClose'): void;
}

const emit = defineEmits<Emits>();
type RuleKey = keyof Pick<Api.Kip.BscParams, 'levelId' | 'targetName' | 'sort' | 'statisticCycle' | 'metricName'>;
const props = defineProps<Props>();
const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();
const rules = ref<Record<RuleKey, App.Global.FormRule[]>>({
  levelId: [defaultRequiredRule],
  targetName: [defaultRequiredRule],
  sort: [
    defaultRequiredRule,
    {
      pattern: /^\d+(\.\d+)*$/,
      message: '只能输入数字和小数点，格式如 1.2.3',
      trigger: 'blur'
    }
  ],
  statisticCycle: [defaultRequiredRule],
  metricName: [defaultRequiredRule]
});

const model = reactive<Api.Kip.BscFormParams>(createDefaultModel());
async function handleInitModel() {
  Object.assign(model, createDefaultModel());

  if (props.operateType === 'edit') {
    const { error, data } = await fetchGetBsc(props.id);
    if (error) {
      return;
    }
    Object.assign(model, data);
    model.id = data.id;
  }
}

function createDefaultModel(): Api.Kip.BscFormParams {
  return {
    dataOutputDept: null,
    deptId: null,
    formula: null,
    levelId: null,
    metricName: null,
    statisticCycle: null,
    targetName: null,
    userId: null,
    sort: null,
    haveKeyIssues: false
  };
}

async function handleSubmit() {
  try {
    await validate();
    const { dept: _dept, user: _user, ...restModel } = model;
    const payload = {
      ...restModel,
      year: String(props.year)
    };

    const { error } = props.operateType === 'add' ? await fetchPostBsc(payload) : await fetchPutBsc(payload);

    if (error) return false;
    window?.$message?.success(props.operateType === 'add' ? '新增成功' : '编辑成功');
    emit('btnClose');
    return true;
  } catch {
    return false;
  }
}

defineExpose({
  handleSubmit
});
onMounted(() => {
  handleInitModel();
  restoreValidation();
});
</script>

<template>
  <NForm ref="formRef" :model="model" :rules="rules">
    <NGrid responsive="screen" item-responsive x-gap="20">
      <NFormItemGi span="24 s:12 m:12" label="层面" path="levelId">
        <NSelect v-model:value="model.levelId" :options="BscStatusOptions" />
      </NFormItemGi>
      <NFormItemGi span="24 s:12 m:12" label="战略目标" path="targetName">
        <NInput v-model:value="model.targetName" clearable placeholder="请输入战略目标" />
      </NFormItemGi>
      <NFormItemGi span="24 s:12 m:12" label="序号" path="sort">
        <NInput v-model:value="model.sort" class="w-full" clearable placeholder="请输入序号(如：1.2.3)" />
      </NFormItemGi>
      <NFormItemGi span="24 s:12 m:12" label="战略指标" path="metricName">
        <NInput v-model:value="model.metricName" clearable placeholder="请输入战略指标" />
      </NFormItemGi>
      <NFormItemGi span="24 s:12 m:12" label="计算口径" path="formula">
        <NInput v-model:value="model.formula" clearable placeholder="请输入计算口径" />
      </NFormItemGi>
      <NFormItemGi span="24 s:12 m:12" label="数据输出部门" path="dataOutputDept">
        <NInput v-model:value="model.dataOutputDept" clearable placeholder="请输入数据输出部门" />
      </NFormItemGi>

      <NFormItemGi span="24 s:12 m:12" label="责任人" path="userId">
        <SelectWithSearch
          v-model:value="model.userId"
          :api-func="getUserOptions"
          :selected-options="model.user || []"
          class="w-full"
          multiple
          placeholder="责任人"
        />
      </NFormItemGi>
      <NFormItemGi span="24 s:12 m:12" label="责任部门" path="deptId">
        <DeptTreeSelect v-model:value="model.deptId" multiple checkable placeholder="请选择责任部门" />
      </NFormItemGi>
      <NFormItemGi span="24 s:12 m:12" label="数据周期" path="statisticCycle">
        <NSelect
          v-model:value="model.statisticCycle"
          :options="StatisticCycleOptions"
          class="w-full"
          clearable
          placeholder="请选择数据周期"
        />
      </NFormItemGi>
      <NFormItemGi span="24 s:12 m:12" label="单位" path="label">
        <NSelect v-model:value="model.label" :options="BscOptions" class="w-full" clearable placeholder="请选择单位" />
      </NFormItemGi>
    </NGrid>
  </NForm>
</template>

<style scoped></style>
