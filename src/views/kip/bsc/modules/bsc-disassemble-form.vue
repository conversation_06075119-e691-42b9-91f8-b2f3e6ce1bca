<script lang="tsx" setup>
import { onMounted, reactive, ref } from 'vue';
import type { FormInst } from 'naive-ui';
import { NButton, NForm, NScrollbar } from 'naive-ui';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { fetchGetWithKeyIssues, fetchPostWithKeyIssues, fetchPutSort } from '@/service/api/kip/bsc';
import { getBscOptions, getDeptOptions } from '@/utils/async-functions';
import SelectWithSearch from '@/components/common/select-with-search.vue';
import KeyIssuesFormItem from './key-issues-form-item.vue';

interface Props {
  /** the edit row data */
  id: number;
  metricName: string;
}

interface Emits {
  (e: 'btnClose'): void;
}
const emit = defineEmits<Emits>();
const props = defineProps<Props>();
const { formRef, validate, restoreValidation } = useNaiveForm();
const loading = ref<boolean>(false);
const { defaultRequiredRule } = useFormRules();
const rules = ref({
  metricId: defaultRequiredRule,
  year: defaultRequiredRule,
  name: defaultRequiredRule
});
const createModel = () => {
  return {
    metric: null,
    metricId: null,
    strategicBlueprint: null
  };
};
const tableData = ref<Api.Kip.KeyIssues[]>([]);
const model = reactive<Api.Kip.MetricsWithKeyIssuesRespVo>(createModel());
const formRefs = ref<(FormInst | null)[]>([]);
const formModels = ref<Array<Api.Kip.KeyIssuesFormParams>>([]);

function createEmptyModel() {
  const nextSort = tableData.value.length + formModels.value.length + 1;
  return {
    deptId: null,
    dept: null,
    name: '',
    sort: String(nextSort),
    exceptedResult: '',
    resourceDemand: ''
  };
}

function removeForm(index: number) {
  formModels.value.splice(index, 1);
  formRefs.value.splice(index, 1);
  formModels.value.forEach((item, idx) => {
    item.sort = String(idx + 1);
  });
}

function addForm() {
  formModels.value.push(createEmptyModel());
}

async function handleInitModel() {
  model.metricId = props.id;
  model.metric = {
    id: props.id,
    name: props.metricName
  };
  try {
    loading.value = true;
    const { error, data } = await fetchGetWithKeyIssues(props.id);
    if (error) {
      return;
    }
    tableData.value = data?.keyIssues || [];
    addForm();
  } catch {
    tableData.value = [];
  } finally {
    loading.value = false;
  }
}

async function handleSubmit(): Promise<boolean> {
  try {
    await validate();
    const results = await Promise.all(
      formRefs.value
        .filter((form): form is FormInst => form !== null)
        .map(form =>
          form
            .validate()
            .then(() => true)
            .catch(() => false)
        )
    );
    const allPass = results.every(res => res);
    if (!allPass) return false;

    const treeSelectOptions = await getDeptOptions('');
    const keyIssues = (formModels.value || []).map(item => {
      const withDeptIds = (item.withDept || []).map(dept => {
        return typeof dept === 'object' ? dept.id : dept;
      });

      const withDept = withDeptIds
        .map(id => {
          const dept = treeSelectOptions.find(opt => opt.value === id);
          return dept
            ? {
                id: dept.value,
                name: dept.label,
                type: 'dept'
              }
            : null;
        })
        .filter(Boolean);

      const { withDept: _omit, ...itemRest } = item;
      return {
        ...itemRest,
        withDept
      };
    });

    const isOverLimit = checkMonthsLimit(keyIssues);

    if (isOverLimit) {
      const { error } = await fetchPostWithKeyIssues({ ...model, keyIssues });
      if (error) return false;
      window?.$message?.success($t('common.addSuccess'));
      emit('btnClose');
      return true;
    }

    const userConfirmed = await new Promise<boolean>(resolve => {
      window.$dialog?.info({
        title: '温馨提示',
        content: () =>
          h('div', {}, [
            h('div', {}, '关键事项建议避免跨月集中设置，按月拆分并及时更新完成状态，有利于精准追踪、降低遗漏风险。'),
            h('div', {}, '(点击取消返回修改，点击确定提交)')
          ]),
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: async () => {
          const { error } = await fetchPostWithKeyIssues({ ...model, keyIssues });
          if (error) return resolve(false);
          window?.$message?.success($t('common.addSuccess'));
          emit('btnClose');
          resolve(true);
        },
        onNegativeClick: () => {
          resolve(false);
        }
      });
    });

    return userConfirmed;
  } catch (e) {
    console.error(e);
    return false;
  }
}

function checkMonthsLimit(keyIssues: Array<{ months: number[] }>): boolean {
  const overLimit = keyIssues.some(issue => issue.months.length > 1);
  if (overLimit) {
    return false;
  }
  return true;
}
let dragIndex = -1;
const dragOverIndex = ref(-1);

const originalOrder = ref<number[]>([]);
function onDragStart(index: number) {
  dragIndex = index;
  originalOrder.value = tableData.value.map(item => item.id);
}

function onDragEnter(index: number) {
  dragOverIndex.value = index;
  if (dragIndex === index) return;

  const item = tableData.value[dragIndex];
  tableData.value.splice(dragIndex, 1);
  tableData.value.splice(index, 0, item);

  tableData.value.forEach((tableItem: Api.Kip.KeyIssues, idx) => {
    tableItem.sort = String(idx + 1);
  });
  dragIndex = index;
}

// 结束拖拽
async function onDragEnd() {
  try {
    dragIndex = -1;
    dragOverIndex.value = -1;
    const newOrder = tableData.value.map(item => item.id);

    const changed = originalOrder.value.some((id, idx) => id !== newOrder[idx]);
    if (changed) {
      const newList = tableData.value.map(item => ({
        id: item.id,
        sort: item.sort
      }));

      await fetchPutSort(newList);
    }
  } catch {
    // 还原原始顺序并重设 sort
    tableData.value = originalOrder.value.map((id, idx) => {
      const item = tableData.value.find(i => i.id === id)!;
      return { ...item, sort: idx + 1 };
    });
    window.$message?.error('排序失败，已还原');
  }
}

defineExpose({
  handleSubmit
});
onMounted(() => {
  handleInitModel();
  restoreValidation();
});
</script>

<template>
  <NScrollbar :content-style="{ maxHeight: '650px', padding: '10px' }">
    <NCollapse arrow-placement="right" default-expanded-names="name">
      <NCollapseItem name="name">
        <template #header>
          <div>
            <span>{{ `已拆解的关键事项 (${tableData.length})` }}</span>
            <span class="text-14px text-red-500">(拖动可对关键事项进行排序)</span>
          </div>
        </template>
        <div
          v-for="(item, index) in tableData"
          :key="item.id"
          class="mb-10px mr-10px transition-transform duration-300"
          draggable="true"
          @dragstart="onDragStart(index)"
          @dragover.prevent
          @dragenter.prevent="onDragEnter(index)"
          @dragend="onDragEnd"
        >
          <div class="flex">
            <icon-mdi-drag class="cursor-move text-24px" />
            <NTag type="primary" class="cursor-move">
              {{ `(${index + 1})` }} {{ item.dept?.name ? `${item.dept?.name}：` : null }}{{ item.name }}
            </NTag>
          </div>
        </div>
      </NCollapseItem>
    </NCollapse>
    <NDivider />
    <NForm ref="formRef" :model="model" :rules="rules">
      <NGrid responsive="screen" model-responsive x-gap="20">
        <NFormItemGi label="战略指标" path="metricId" span="12 s:12 m:12">
          <SelectWithSearch
            v-model:value="model.metricId"
            disabled
            :api-func="getBscOptions"
            :page-size="0"
            :selected-options="[model.metric]"
            placeholder="战略指标"
          />
        </NFormItemGi>
      </NGrid>
    </NForm>
    <div v-for="(item, index) in formModels" :key="index" class="relative mb-6 w-full border rounded p-4">
      <div class="absolute left-10px top--15px b border-none bg-#fff p-2px text-18px text-primary-500 font-bold">
        关键事项{{ tableData.length + index + 1 }}
      </div>
      <NPopconfirm v-if="index >= 1" @positive-click="removeForm(index)">
        <template #trigger>
          <icon-mdi-close-circle class="absolute right--12px top--12px text-24px text-red" />
        </template>
        是否删除本条数据？
      </NPopconfirm>
      <NForm :ref="el => (formRefs[index] = el)" :model="item" :rules="rules">
        <KeyIssuesFormItem :key="index" v-model:model="formModels[index]" />
      </NForm>
    </div>
    <div class="mt-5">
      <NButton class="w-1/1" @click="addForm">新增一行</NButton>
    </div>
  </NScrollbar>
</template>

<style scoped>
:deep(.n-data-table .n-data-table-th) {
  padding: 2px !important;
}
:deep(.n-collapse-item__header-main) {
  font-size: 16px;
}
:deep(.transition-transform) {
  transition: transform 0.3s ease;
}
</style>
