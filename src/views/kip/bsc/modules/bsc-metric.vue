<script setup lang="tsx">
import { nextTick, onMounted, ref } from 'vue';
import type { DataTableColumns } from 'naive-ui';
import { NDataTable, NInput } from 'naive-ui';
import { fetchGetBscMetric, fetchPutBscMetric } from '@/service/api/kip/bsc';
import { $t } from '@/locales';
import { getEnumValue } from '@/utils/useful_func';
import { BscOptions } from '@/enum';

interface Props {
  year: number;
  rowData: Api.Kip.BscParams;
}
const props = defineProps<Props>();
interface Emits {
  (e: 'btnClose'): void;
}

const emit = defineEmits<Emits>();
const loading = ref(false);
const tableData = ref<Api.Kip.MetricInfoRespInfo[]>([]);
const columns = ref<DataTableColumns<Api.Kip.MetricInfoRespVo>>([
  { title: '月份', key: 'monthName', width: 50, align: 'center' },
  {
    title: '目标值',
    key: 'target',
    width: 50,
    align: 'center',
    render(row: Api.Kip.MetricInfoRespVo) {
      return (
        <NInput
          style={{ width: '70%' }}
          value={row.excepted}
          onUpdateValue={(value: string) => {
            row.excepted = value;
          }}
          v-slots={{
            suffix: () => <span>{getEnumValue(BscOptions, props.rowData.label)}</span>
          }}
        />
      );
    }
  }
]);
function initData(data: []) {
  if (!Array.isArray(data)) {
    return [];
  }

  let initColData = Array.from({ length: 12 }, (_, i) => ({
    month: i + 1,
    monthName: `${i + 1}月`,
    label: null,
    target: null
  }));

  initColData = initColData.map(c => {
    const record = data.find(item => item.month === c.month);
    if (record) {
      return { ...c, ...record };
    }
    return c;
  });
  return initColData;
}

async function loadData() {
  try {
    loading.value = true;
    const { data, error } = await fetchGetBscMetric(props.rowData.id);
    if (error) {
      return;
    }
    tableData.value = initData(data.data || []);
    // 重新初始化表头
  } catch (err) {
    console.error('数据加载失败:', err);
  } finally {
    loading.value = false;
  }
}
// 提交
async function handleSubmit() {
  try {
    const { error } = await fetchPutBscMetric({
      data: tableData.value,
      parentId: props.rowData.id,
      year: Number(props.year)
    });
    if (error) {
      return false;
    }
    window?.$message?.success($t('common.updateSuccess'));
    emit('btnClose');
    return true;
  } catch (e) {
    console.log(e);
    return false;
  }
}
defineExpose({
  handleSubmit
});
onMounted(() => {
  nextTick(() => {
    loadData();
  });
});
</script>

<template>
  <div class="container">
    <NDataTable
      :columns="columns"
      :data="tableData"
      :loading="loading"
      :row-key="row => row.id"
      striped
      bordered
      size="small"
    />
  </div>
</template>
