<script lang="tsx" setup>
import { onMounted, reactive, ref } from 'vue';
import type { DataTableColumn, FormItemRule } from 'naive-ui';
import { NForm, NFormItem } from 'naive-ui';
import { useNaiveForm } from '@/hooks/common/form';
import { useAppStore } from '@/store/modules/app';
import { fetchDelBscMetric, fetchGetWithKeyIssues } from '@/service/api/kip/bsc';
import { $t } from '@/locales';
import { getBscOptions } from '@/utils/async-functions';
import SelectWithSearch from '@/components/common/select-with-search.vue';

interface Props {
  id: number;
  haveKeyIssues: boolean;
}
const props = defineProps<Props>();
const appStore = useAppStore();
const loading = ref<boolean>(false);
const { formRef, validate, restoreValidation } = useNaiveForm();

const model = reactive({
  metricId: props.id,
  keyIssues: [] as Api.Kip.KeyIssues[]
});

const requiredRule: FormItemRule = {
  required: true,
  type: 'number',
  message: '战略目标不能为空',
  trigger: ['blur', 'change']
};
const columns = ref<DataTableColumn<Api.Kip.KeyIssues>[]>([
  {
    title: '关键事项',
    key: 'name',
    align: 'center'
  },
  {
    title: '战略目标',
    key: 'metricId',
    align: 'center',
    render: (_row: Api.Kip.KeyIssues, index: number) => (
      <NFormItem path={`keyIssues[${index}].metricId`} rule={requiredRule}>
        <SelectWithSearch
          v-model:value={model.keyIssues[index].metricId}
          placeholder="战略目标"
          clearable
          onUpdate:value={val => {
            model.keyIssues[index].metricId = val as number;
          }}
          apiFunc={getBscOptions}
          pageSize={0}
          selectedOptions={[]}
        />
      </NFormItem>
    )
  }
]);

async function handleInitModel() {
  try {
    loading.value = true;
    const { error, data } = await fetchGetWithKeyIssues(props.id);
    if (error) return;
    model.keyIssues = (data?.keyIssues || []).map(item => ({
      ...item,
      metricId: null
    }));
    restoreValidation();
  } catch {
    model.keyIssues = [];
  } finally {
    loading.value = false;
  }
}

async function handleSubmit() {
  try {
    await validate();
    const data = model.keyIssues.map(item => ({
      keyIssueId: item.id,
      metricId: item.metricId
    }));
    const { error } = await fetchDelBscMetric(props.id, data);
    if (error) return false;
    window?.$message?.success('失效成功');
    return true;
  } catch {
    return false;
  }
}

defineExpose({
  handleSubmit
});

onMounted(() => {
  handleInitModel();
});
</script>

<template>
  <NAlert v-if="haveKeyIssues" type="warning" class="mb-10px">
    <div>
      当前BSC指标已拆解为若干关键事项。要删除此BSC指标，需先将这些关键事项重新关联到其他BSC指标。
      确保无关联的关键事项后，才能删除。您可在每个关键事项的编辑页面单独修改，或在下方批量处理。
    </div>
    <div>删除后仍可通过搜索原条件恢复该指标。</div>
  </NAlert>
  <NAlert v-if="!haveKeyIssues" type="warning" class="mb-10px">
    <div>删除BSC指标时，系统会同时清除与该指标相关的目标数据及每月完成值等历史记录。</div>
    <div>删除后仍可通过搜索原条件恢复该指标。</div>
  </NAlert>
  <NForm v-if="haveKeyIssues" ref="formRef" :model="model">
    <NDataTable
      :single-line="false"
      class="h-550px"
      :flex-height="!appStore.isMobile"
      :loading="loading"
      :columns="columns"
      :data="model.keyIssues"
      :row-key="row => row.id"
      size="small"
    />
  </NForm>
</template>
