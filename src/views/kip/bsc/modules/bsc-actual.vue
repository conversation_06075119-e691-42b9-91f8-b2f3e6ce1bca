<script lang="tsx" setup>
import { onMounted, ref } from 'vue';
import type { DataTableColumn } from 'naive-ui';
import { NInput } from 'naive-ui';
import dayjs from 'dayjs';
import { $t } from '@/locales';
import { fetchGetByParent, fetchUpdateByParent } from '@/service/api/kip/bsc';
import { useAppStore } from '@/store/modules/app';
import { BscOptions, StatisticCycleStatus } from '@/enum';
import { splitMonthIntoWeeks } from '@/utils/date-format';
import DateTimePicker from '@/components/common/date-time-picker.vue';
import { getEnumValue } from '@/utils/useful_func';

interface Props {
  statisticCycle: Api.Kip.BscParams['statisticCycle'];
  rowData: Api.Kip.MetricInfoRespVo;
  label: Api.Kip.BscParams['label'];
}

interface Emits {
  (e: 'btnClose'): void;
}
const emit = defineEmits<Emits>();
const props = defineProps<Props>();
const manualSum = ref<string>(null!);
const deviation = ref<string>(null!);
const columns = ref<DataTableColumn<Api.Kip.ActualDataInfoRespVo>[]>([
  {
    title: props.statisticCycle === StatisticCycleStatus.Week ? '周度' : '月度',
    key: 'date',
    width: 200,
    align: 'center',
    render: (row: Api.Kip.ActualDataInfoRespVo) => {
      const monthStart = dayjs(`${props.rowData.year}-${props.rowData.month}`).startOf('month');
      const monthEnd = dayjs(`${props.rowData.year}-${props.rowData.month}`).endOf('month');

      const isDateDisabled = (ts: number) => {
        const date = dayjs(ts);
        return date.isBefore(monthStart, 'day') || date.isAfter(monthEnd, 'day');
      };

      return (
        <DateTimePicker
          time={row.date}
          onUpdate:time={val => {
            row.date = val;
          }}
          class="w-full"
          label="年"
          use-for="daterange"
          clearable
          isDateDisabled={isDateDisabled}
        />
      );
    }
  },
  {
    title: '完成值',
    key: 'actual',
    width: 200,
    align: 'center',
    render: (row: Api.Kip.ActualDataInfoRespVo) => (
      <NInput
        value={row.actual}
        placeholder="请输入完成值"
        clearable
        onUpdateValue={(val: string) => {
          row.actual = val;
        }}
        v-slots={{
          suffix: () => <span>{getEnumValue(BscOptions, props.label)}</span>
        }}
      />
    )
  },
  ...(props.statisticCycle === StatisticCycleStatus.Month
    ? [
        {
          title: '偏差值',
          key: 'deviation',
          width: 200,
          align: 'center' as const,
          render: (row: Api.Kip.ActualDataInfoRespVo) => (
            <NInput
              value={row.deviation}
              placeholder="请输入偏差值"
              clearable
              onUpdateValue={(val: string) => {
                row.deviation = val;
              }}
              v-slots={{
                suffix: () => <span>{getEnumValue(BscOptions, props.label)}</span>
              }}
            />
          )
        }
      ]
    : [])
]);

const appStore = useAppStore();
const loading = ref<boolean>(false);
const tableData = ref<Api.Kip.ActualDataInfoRespVoList['data']>([]);

async function handleInitModel() {
  const { error, data } = await fetchGetByParent(props.rowData.id);
  if (error) return;

  if (data.data?.length) {
    tableData.value = data.data.map((item: Api.Kip.ActualDataInfoRespVo) => ({
      ...item,
      deviation: data.deviation,
      date: [item.begin, item.end]
    }));
    manualSum.value = data.sum;
    deviation.value = data.deviation;
    return;
  }

  const { year, month } = props.rowData;
  const monthStr = `${year}-${month}`;

  if (props.statisticCycle === StatisticCycleStatus.Week) {
    const list = splitMonthIntoWeeks(`${year} ${month}`);
    tableData.value = list.map(item => ({
      actual: undefined,
      begin: undefined,
      end: undefined,
      id: undefined,
      date: [dayjs(item.start, 'YYYY-MM-DD').format('YYYY-MM-DD'), dayjs(item.end, 'YYYY-MM-DD').format('YYYY-MM-DD')]
    }));
  } else {
    const startOfMonth = dayjs(monthStr, 'YYYY-MM').startOf('month').format('YYYY-MM-DD');
    const endOfMonth = dayjs(monthStr, 'YYYY-MM').endOf('month').format('YYYY-MM-DD');
    tableData.value = [{ actual: undefined, begin: undefined, end: undefined, date: [startOfMonth, endOfMonth] }];
  }
}

async function handleSubmit() {
  try {
    const data = tableData.value.map((item: Api.Kip.ActualDataInfoRespVo) => {
      return {
        ...item,
        begin: item.date[0],
        end: item.date[1]
      };
    });
    const sum = props.statisticCycle === StatisticCycleStatus.Week ? manualSum.value : tableData.value[0].actual;
    const deviationValue =
      props.statisticCycle === StatisticCycleStatus.Week ? deviation.value : tableData.value[0].deviation;
    const { error } = await fetchUpdateByParent(props.rowData.id, {
      sum,
      deviation: deviationValue,
      data
    });
    if (error) {
      return false;
    }
    window?.$message?.success($t('common.addSuccess'));
    emit('btnClose');
    return true;
  } catch (e) {
    return false;
  }
}

defineExpose({
  handleSubmit
});
onMounted(() => {
  handleInitModel();
});
</script>

<template>
  <NDataTable
    class="h-260px"
    :flex-height="!appStore.isMobile"
    :loading="loading"
    :columns="columns"
    :data="tableData"
    :row-key="row => row.id"
    size="small"
  />
  <div v-if="statisticCycle === StatisticCycleStatus.Week" class="mt-10px space-y-2">
    <div class="flex items-center justify-end text-base text-gray-800 space-x-2">
      <NTooltip trigger="hover">
        <template #trigger>
          <icon-mdi-help-circle class="cursor-pointer text-[16px] text-gray-400" />
        </template>
        <span>请手动计算公式结果后录入合计栏，系统仅支持加法运算。</span>
      </NTooltip>
      <span>月累计:</span>
      <NInput v-model:value="manualSum" placeholder="请输入总计" :show-button="false" class="!w-150px">
        <template #suffix>{{ getEnumValue(BscOptions, props.label) }}</template>
      </NInput>
    </div>

    <div class="flex items-center justify-end text-base text-gray-800 space-x-2">
      <span>月偏差:</span>
      <NInput v-model:value="deviation" placeholder="请输入偏差" :show-button="false" class="!w-150px">
        <template #suffix>{{ getEnumValue(BscOptions, props.label) }}</template>
      </NInput>
    </div>
  </div>
</template>

<style></style>
