<script lang="ts" setup>
import { useNaiveForm } from '@/hooks/common/form';
import DateTimePicker from '@/components/common/date-time-picker.vue';
import { getBscTargetOptions } from '@/utils/async-functions';
defineOptions({
  name: 'BscSearch'
});

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useNaiveForm();
const model = defineModel<Api.Kip.BscFormParams>('model', { required: true });
const initialParams = { ...model.value };

const invalidOptions = [
  { label: '失效', value: true },
  { label: '未失效', value: false }
];
async function reset() {
  await restoreValidation();

  Object.assign(model.value, initialParams);
  emit('reset');
}

async function search() {
  await validate();
  emit('search');
}
</script>

<template>
  <NCard :bordered="false" class="card-wrapper" size="small">
    <NCollapse>
      <NCollapseItem name="search" title="搜索">
        <NForm ref="formRef" :label-width="80" class="mb--12px" :model="model" label-placement="left">
          <NGrid item-responsive responsive="screen">
            <NFormItemGi key="year" class="pr-24px" label="年" span="24 s:12 m:6">
              <DateTimePicker
                v-model:time="model.year"
                class="w-full"
                label="年"
                use-for="date_with_year"
                :clearable="false"
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="责任部门" path="deptId">
              <DeptTreeSelect v-model:value="model.deptId" multiple checkable placeholder="请选择责任部门" />
            </NFormItemGi>
            <NFormItemGi key="target" class="pr-24px" label="战略目标" span="24 s:12 m:6">
              <SelectWithSearch
                v-model:value="model.target"
                :api-func="params => getBscTargetOptions(params, model.year)"
                :page-size="0"
                :selected-options="[]"
                placeholder="战略目标"
              />
            </NFormItemGi>
            <NFormItemGi key="metricId" class="pr-24px" label="战略指标" span="24 s:12 m:6">
              <NInput v-model:value="model.metricName" placeholder="请输入战略目标" />
            </NFormItemGi>
            <NFormItemGi key="metricId" class="pr-24px" label="状态" span="24 s:12 m:6">
              <NSelect v-model:value="model.invalid" :options="invalidOptions" placeholder="请选择状态" />
            </NFormItemGi>
            <NFormItemGi class="pr-24px" span="24 m:12">
              <NSpace class="w-full" justify="end">
                <NButton @click="reset">
                  <template #icon>
                    <icon-ic-round-refresh class="text-icon" />
                  </template>
                  {{ $t('common.reset') }}
                </NButton>
                <NButton ghost type="primary" @click="search">
                  <template #icon>
                    <icon-ic-round-search class="text-icon" />
                  </template>
                  {{ $t('common.search') }}
                </NButton>
              </NSpace>
            </NFormItemGi>
          </NGrid>
        </NForm>
      </NCollapseItem>
    </NCollapse>
  </NCard>
</template>

<style scoped>
:deep(.n-collapse-item__content-inner) {
  padding-top: 12px !important;
}
</style>
