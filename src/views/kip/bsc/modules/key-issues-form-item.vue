<script setup lang="ts">
import DateTimePicker from '@/components/common/date-time-picker.vue';

const model = defineModel<Api.Kip.KeyIssues>('model', { required: true });
</script>

<template>
  <NGrid responsive="screen" model-responsive x-gap="20">
    <NFormItemGi span="12 s:12 m:12" label="年度" path="year">
      <div>
        <DateTimePicker v-model:time="model.year" class="w-full" label="年度" use-for="year" />
        <NCheckboxGroup v-model:value="model.months" class="mt-2">
          <NCheckbox v-for="(month, index) in 12" :key="index" :value="month" class="w-20">{{ month }}月</NCheckbox>
        </NCheckboxGroup>
      </div>
    </NFormItemGi>
    <NFormItemGi span="12 s:12 m:12" label="责任部门" path="deptId">
      <DeptTreeSelect v-model:value="model.deptId" placeholder="请选择责任部门" />
    </NFormItemGi>

    <NFormItemGi span="12 s:12 m:12" label="关键事项" path="name">
      <NInput
        v-model:value="model.name"
        clearable
        placeholder="请输入关键事项(关键事项添加后不可修改，请仔细确认名称；无需手动编号，系统将自动处理)"
        type="textarea"
        :rows="2"
        :disabled="!!model.id"
      />
    </NFormItemGi>

    <NFormItemGi span="12 s:12 m:12" label="预期结果" path="exceptedResult">
      <NInput v-model:value="model.exceptedResult" clearable placeholder="请输入预期结果" type="textarea" :rows="2" />
    </NFormItemGi>
    <NFormItemGi span="12 s:12 m:12" label="协同部门" path="withDept">
      <DeptTreeSelect v-model:value="model.withDept" multiple checkable placeholder="请选择责任部门" />
    </NFormItemGi>
    <NFormItemGi span="12 s:12 m:12" label="资源需求" path="resourceDemand">
      <NInput v-model:value="model.resourceDemand" clearable placeholder="请输入资源需求" type="textarea" :rows="2" />
    </NFormItemGi>
  </NGrid>
</template>

<style scoped></style>
