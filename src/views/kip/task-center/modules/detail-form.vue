<script setup lang="tsx">
import { useInjectProForm } from 'pro-naive-ui';

import PreviewFile from '@/components/common/preview-file.vue';
import { renderModal } from '@/components/re-modal';
import { CompletionStatus, CompletionStatusOptions } from '@/enum';
import { useAuthStore } from '@/store/modules/auth';
import { getEnumTagType, getEnumValue, showUserStr } from '@/utils/useful_func';

const injectForm = useInjectProForm();
const useAuth = useAuthStore();
const emit = defineEmits(['confirm']);
const handleLinkClick = (event: Event) => {
  event.preventDefault();
  const link = event.currentTarget as HTMLAnchorElement;
  const fileName = link.getAttribute('download') || '文件下载';
  const href = link.getAttribute('href');
  if (href) {
    // 自定义逻辑，例如记录日志、展示弹窗等
    renderModal(
      PreviewFile,
      {
        url: href,
        fileName
      },
      {
        title: fileName,
        contentStyle: {
          width: '100%',
          height: '85vh'
        },
        style: {
          width: '80%'
        }
      }
    );
  }
};

const bindEvents = () => {
  const containers = document.querySelectorAll('.html-container');
  containers.forEach(container => {
    const links = container.querySelectorAll('a[data-w-e-type="attachment"]') as unknown as HTMLAnchorElement[];
    links.forEach(link => {
      link.addEventListener('click', handleLinkClick);
      link.style.color = '#2563eb';
      link.style.textDecoration = 'underline';
      link.style.cursor = 'pointer';
      link.title = '点击预览';
    });
  });
};

const handleConfirm = () => {
  emit('confirm', injectForm?.values.value.id);
};

const isAssignee = computed(() => {
  return (
    injectForm?.values.value.assignee.id === useAuth.userInfo.id &&
    [CompletionStatus.Processing, CompletionStatus.Unfinished].includes(injectForm?.values.value.lastProcessStatusName)
  );
});

onMounted(() => {
  nextTick(() => {
    bindEvents();
  });
});
</script>

<template>
  <NRow :gutter="12">
    <NCol :span="15">
      <NScrollbar :content-style="{ maxHeight: '550px' }">
        <NCard title="下发" size="small">
          <NDescriptions label-placement="left" :column="1">
            <NDescriptionsItem label="下发时间">{{ injectForm?.values.value.insertedTime }}</NDescriptionsItem>
            <NDescriptionsItem label="待办事项">{{ injectForm?.values.value.name }}</NDescriptionsItem>
            <NDescriptionsItem label="下发人">
              <NTag type="info">{{ showUserStr(injectForm?.values.value.assignedBy) }}</NTag>
            </NDescriptionsItem>
            <NDescriptionsItem label="责任人">
              <NTag type="info">{{ showUserStr(injectForm?.values.value.assignee) }}</NTag>
            </NDescriptionsItem>
          </NDescriptions>
        </NCard>
        <NCard title="处理详情" size="small" class="mt-2">
          <template #header-extra>
            <NButton v-if="isAssignee" type="primary" size="small" ghost @click="handleConfirm">去处理</NButton>
          </template>
          <NDescriptions v-if="injectForm?.values.value.lastProcessDescription" label-placement="left" :column="1">
            <NDescriptionsItem label="状态">
              <NTag
                size="small"
                :type="getEnumTagType(CompletionStatusOptions, injectForm?.values.value.lastProcessStatus?.id)"
              >
                {{ injectForm?.values.value.lastProcessStatus?.name }}
              </NTag>
            </NDescriptionsItem>
            <NDescriptionsItem label="时间">{{ injectForm?.values.value.lastProcessTime || '-' }}</NDescriptionsItem>
            <NDescriptionsItem label="说明">
              <div class="html-container" v-html="injectForm?.values.value.lastProcessDescription" />
            </NDescriptionsItem>
          </NDescriptions>
          <NEmpty v-else>
            <template #icon>
              <SvgIcon icon="material-symbols:schedule" class="text-[40px]" />
            </template>
            <template #default>
              <NText type="info">待{{ showUserStr(injectForm?.values.value.assignee) }}处理中</NText>
            </template>
          </NEmpty>
        </NCard>
      </NScrollbar>
    </NCol>
    <NCol :span="9">
      <NScrollbar :content-style="{ maxHeight: '550px' }">
        <NCard title="历史" size="small">
          <NText v-if="injectForm?.values.value.histories" type="info">
            累计处理{{ injectForm?.values.value.histories?.length }}次
          </NText>
          <NTimeline v-if="injectForm?.values.value.histories" class="mt-2">
            <NTimelineItem v-for="item in injectForm?.values.value.histories" :key="item.id">
              <template #default>
                <div>
                  <NDescriptions label-placement="left" :column="1">
                    <NDescriptionsItem label="状态">
                      <NTag size="small" :type="getEnumTagType(CompletionStatusOptions, item.processStatus)">
                        {{ getEnumValue(CompletionStatusOptions, item.processStatus) }}
                      </NTag>
                    </NDescriptionsItem>
                    <NDescriptionsItem label="时间">
                      {{ item.processTime || '-' }}
                    </NDescriptionsItem>
                    <NDescriptionsItem label="说明">
                      <div class="html-container" v-html="item.description" />
                    </NDescriptionsItem>
                  </NDescriptions>
                </div>
              </template>
            </NTimelineItem>
          </NTimeline>
          <NEmpty v-else />
        </NCard>
      </NScrollbar>
    </NCol>
  </NRow>
</template>

<style scoped>
:deep(a[data-w-e-type='attachment']) {
  color: #2563eb; /* 蓝色 */
  text-decoration: none;
  cursor: pointer;
  position: relative;
  transition: all 0.2s;
  margin-right: 10px;
}

:deep(a[data-w-e-type='attachment']:hover) {
  text-decoration: underline;
}

/* 可选：悬浮提示气泡 */
:deep(a[data-w-e-type='attachment']:hover::after) {
  content: '点击预览';
  position: absolute;
  top: -22px;
  left: 0;
  background-color: #444444;
  color: #fff;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 10;
}
:deep(.n-dialog__content) {
  height: 80vh !important;
  background-color: red !important;
}
</style>
