<script setup lang="tsx">
import { ProTextarea, useInjectProForm } from 'pro-naive-ui';

import { getUserListByDeptOptions } from '@/utils/async-functions';
import { renderLabel, renderSingleSelectTag } from '@/utils/select-tag';
const injectForm = useInjectProForm();
</script>

<template>
  <ProTextarea title="待办事项" path="name" required rows="3" />
  <ProField
    title="责任人"
    path="assigneeId"
    placeholder="责任人"
    required
    :field-props="{
      apiFunc: getUserListByDeptOptions,
      selectedOptions: !!injectForm?.values.value.id ? [injectForm?.values.value.assignee] : [],
      renderLabel,
      renderTag: renderSingleSelectTag,
      valueField: 'id',
      labelField: 'nickname',
      disabled: !!injectForm?.values.value.id
    }"
  >
    <template #input="{ inputProps }">
      <SelectWithSearch v-bind="inputProps" />
    </template>
  </ProField>
</template>

<style scoped></style>
