<script setup lang="tsx">
import { useInjectProForm } from 'pro-naive-ui';

import { CompletionStatus, CompletionStatusOptions } from '@/enum';
import { showUserStr } from '@/utils/useful_func';

const injectForm = useInjectProForm();
const isDisabled = injectForm?.values.value.status === CompletionStatus.Finished;
</script>

<template>
  <ProField title="待办说明" path="status">
    <template #label>
      <div class="font-bold">待办说明</div>
    </template>
    <template #input>
      <NCard size="small">
        <NDescriptions label-placement="left" :column="1">
          <NDescriptionsItem label="下发时间">{{ injectForm?.values.value.insertedTime }}</NDescriptionsItem>
          <NDescriptionsItem label="待办事项">{{ injectForm?.values.value.name }}</NDescriptionsItem>
          <NDescriptionsItem label="下发人">
            <NTag type="info">{{ showUserStr(injectForm?.values.value.assignedBy) }}</NTag>
          </NDescriptionsItem>
        </NDescriptions>
      </NCard>
    </template>
  </ProField>
  <ProField title="状态" path="status" required>
    <template #input="{ inputProps }">
      <NRadioGroup v-bind="inputProps" :disabled="isDisabled">
        <NRadioButton
          v-for="item in CompletionStatusOptions"
          :key="item.value"
          :value="item.value"
          :label="item.label"
        />
      </NRadioGroup>
    </template>
  </ProField>

  <ProField title="确认说明" path="description" placeholder="请输入确认说明" required>
    <template #input="{ inputProps }">
      <div class="flex flex-col gap-16px">
        <NAlert type="info">
          <div v-if="!isDisabled" class="inline-flex flex-wrap items-center gap-1 text-sm leading-relaxed">
            <span>您可点击下方</span>
            <SvgIcon
              icon="ic-round-attachment"
              class="inline translate-y-[1px] text-[14px] -rotate-45 sm:text-[16px]"
            />
            <span>按钮提交汇报材料，同时可补充必要的文字说明，使用 ctrl+enter 进行换行。</span>
          </div>
          <div v-else>当前待办事项已提交完成，如需补充信息，请在下方继续填写，使用 ctrl+enter 进行换行。</div>
        </NAlert>

        <WangeEditor
          v-model="injectForm.values.value.description"
          v-bind="inputProps"
          upload-type="COMPANY"
          :height="260"
          :toolbar-config="{ excludeKeys: ['group-video', 'group-image'] }"
        />
      </div>
    </template>
  </ProField>
</template>

<style scoped></style>
