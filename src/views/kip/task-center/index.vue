<script setup lang="tsx">
import { N<PERSON><PERSON>on, NTag } from 'naive-ui';
import type { ProDataTableColumns, ProSearchFormColumns } from 'pro-naive-ui';
import { createProModalForm, createProSearchForm, useNDataTable, useRequest } from 'pro-naive-ui';

import ConfirmForm from './modules/confirm-form.vue';
import DetailForm from './modules/detail-form.vue';
import ModelForm from './modules/model-form.vue';

import { deleteRoleBtn, editRoleBtn, infoRoleBtn, roleBtn } from '@/directives/permission/permi-btn';
import { CompletionStatusOptions } from '@/enum';
import { $t } from '@/locales';
import {
  fetchDeleteTodoList,
  fetchGetTodoDetail,
  fetchGetTodoList,
  fetchPostTodoList,
  fetchPutTodoList,
  fetchPutTodoProcess
} from '@/service/api/kip/task-center';
import { getUserListByDeptOptions } from '@/utils/async-functions';
import { renderLabel, renderSingleSelectTag } from '@/utils/select-tag';
import { getEnumTagType, showUserStr } from '@/utils/useful_func';

const route = useRoute();
const router = useRouter();
const searchForm = createProSearchForm();
const {
  table: { tableProps, onChange },
  search: { proSearchFormProps }
} = useNDataTable(
  async ({ current, pageSize }, values) => {
    return fetchGetTodoList({
      ...values,
      pageSize,
      pageNo: current
    }).then(({ data }) => {
      return {
        list: data?.list || [],
        total: data?.total || 0
      };
    });
  },
  {
    form: searchForm
  }
);

/* 新增 */
const { loading: fetchPostTodoLoading, runAsync: runAsyncFetchPostTodoList } = useRequest(fetchPostTodoList, {
  manual: true
});

/* 编辑 */
const { loading: fetchPutTodoLoading, runAsync: runAsyncFetchPutTodoList } = useRequest(fetchPutTodoList, {
  manual: true
});

/* 承办人确认 */
const { loading: fetchPutTodoProcessLoading, runAsync: runAsyncFetchPutTodoProcess } = useRequest(fetchPutTodoProcess, {
  manual: true
});

/* 新增 */
const modalForm = createProModalForm<Api.Kip.TaskCenterFormParams>({
  onSubmit: async values => {
    try {
      if (modalForm.values.value.id) {
        const { error } = await runAsyncFetchPutTodoList(modalForm.values.value.id, values);
        if (!error) {
          onChange();
          window.$message?.success($t('common.updateSuccess'));
          modalForm.close();
        }
      } else {
        const { error } = await runAsyncFetchPostTodoList(values);
        if (!error) {
          onChange({ page: 1 });
          window.$message?.success($t('common.addSuccess'));
          modalForm.close();
        }
      }
    } catch (e) {
      console.log(e);
    }
  }
});
/* 详情 */
const detailForm = createProModalForm<Api.Kip.TodoListDetailRespVO>();
/* 承办人确认 */
const confirmForm = createProModalForm<{
  description: string;
  id: number;
  status: string;
  name: string;
  insertedTime: string;
  assignedBy: Api.Kip.QueryTodoListRespVO['assignedBy'];
}>({
  onSubmit: async values => {
    const { error } = await runAsyncFetchPutTodoProcess(confirmForm.values.value.id, values);
    if (!error) {
      onChange();
      window.$message?.success($t('common.updateSuccess'));
      confirmForm.close();
    }
  }
});
/* 搜索 */
const searchColumns: ProSearchFormColumns<Api.Kip.TaskCenterSearchParams> = [
  {
    title: '待办事项',
    path: 'name'
  },
  {
    title: '责任人',
    path: 'assignee',
    field: 'select-with-search',
    fieldProps: {
      apiFunc: getUserListByDeptOptions,
      selectedOptions: [],
      clearable: true,
      renderLabel,
      placeholder: '责任人',
      renderTag: renderSingleSelectTag
    }
  },
  {
    title: '状态',
    path: 'processStatus',
    field: 'select',
    fieldProps: {
      options: CompletionStatusOptions
    }
  },
  {
    title: '下发人',
    path: 'assignedBy',
    field: 'select-with-search',
    fieldProps: {
      apiFunc: getUserListByDeptOptions,
      selectedOptions: [],
      clearable: true,
      renderLabel,
      placeholder: '下发人',
      renderTag: renderSingleSelectTag
    }
  }
];
/* 表格 */
const columns: ProDataTableColumns<Api.Kip.QueryTodoListRespVO> = [
  {
    type: 'index'
  },
  {
    minWidth: 260,
    title: '待办事项',
    path: 'name',
    align: 'center'
  },
  {
    title: '下发人',
    path: 'assignee.name',
    align: 'center',
    render: (row: Api.Kip.QueryTodoListRespVO) => {
      return row.assignee ? <NTag type="info">{showUserStr(row?.assignedBy)}</NTag> : '-';
    }
  },
  {
    title: '责任人',
    path: 'assignee.name',
    align: 'center',
    render: (row: Api.Kip.QueryTodoListRespVO) => {
      return row.assignee ? <NTag type="info">{showUserStr(row?.assignee)}</NTag> : '-';
    }
  },
  {
    title: '下发时间',
    path: 'insertedTime',
    align: 'center'
  },
  {
    title: '状态',
    path: 'completionStatus.name',
    align: 'center',
    render: (row: Api.Kip.QueryTodoListRespVO) => {
      return <NTag type={getEnumTagType(CompletionStatusOptions, row.processStatus.id)}>{row.processStatus.name}</NTag>;
    }
  },
  {
    title: '完成时间',
    path: 'completedTime',
    align: 'center'
  },
  {
    width: 120,
    title: $t('common.operate'),
    align: 'center',
    render: (row: Api.Kip.QueryTodoListRespVO) => {
      return (
        <div class="flex flex-wrap gap-2">
          {infoRoleBtn(['core-minder:todo:query'], 'success', () => handleOpenDetail(row.id))}

          {row.processable && roleBtn('确认', ['core-minder:todo:process'], 'primary', () => handleConfirm(row.id))}

          {row.editable && editRoleBtn(['core-minder:todo:update'], () => handleOpenModal(row))}

          {row.deletable && deleteRoleBtn(['core-minder:todo:delete'], () => handleDelete(row.id))}
        </div>
      );
    }
  }
];

/* 删除 */
async function handleDelete(id: number) {
  const { error } = await fetchDeleteTodoList(id);
  if (!error) {
    onChange();
    window.$message?.success($t('common.deleteSuccess'));
  }
}

/* 详情 */
async function handleOpenDetail(id: number) {
  const { data, error } = await fetchGetTodoDetail(id);
  if (error) {
    return;
  }
  detailForm.values.value = data;
  detailForm.open();
}

/* 新增 编辑 */
async function handleOpenModal(row?: Api.Kip.QueryTodoListRespVO) {
  if (row) {
    const { data, error } = await fetchGetTodoDetail(row.id);
    if (error) {
      return;
    }
    modalForm.values.value = data as any;
  }
  modalForm.open();
}

/* 承办人确认 */
async function handleConfirm(id: number) {
  const { data, error } = await fetchGetTodoDetail(id);
  if (error) {
    return;
  }
  confirmForm.values.value = {
    id,
    description: data.lastProcessDescription || '',
    status: data.lastProcessStatus?.id || '',
    name: data.name,
    insertedTime: data.insertedTime,
    assignedBy: data.assignedBy as unknown as Api.Kip.QueryTodoListRespVO['assignedBy']
  };
  detailForm.close();
  confirmForm.open();
}

onMounted(() => {
  if (route.query.id) {
    handleOpenDetail(Number(route.query.id));
    setTimeout(() => {
      router.replace({ path: route.path });
    }, 500);
  }
});
</script>

<template>
  <div class="h-full flex flex-col">
    <ProCard class="mb-24px" :show-collapse="false" content-class="!pb-0">
      <ProSearchForm :form="searchForm" :columns="searchColumns" v-bind="proSearchFormProps" />
    </ProCard>
    <ProDataTable title="待办事项" size="small" flex-height :columns="columns" row-key="id" v-bind="tableProps">
      <template #toolbar>
        <NButton v-hasPermi="['core-minder:todo:create']" size="small" ghost type="primary" @click="handleOpenModal()">
          <template #icon>
            <icon-ic-round-plus class="text-icon" />
          </template>
          {{ $t('common.add') }}
        </NButton>
      </template>
    </ProDataTable>
    <!-- 新增 编辑 -->
    <ProModalForm
      :form="modalForm"
      :title="modalForm.values.value.id ? '编辑待办事项' : '新增待办事项'"
      :loading="modalForm.values.value.id ? fetchPutTodoLoading : fetchPostTodoLoading"
      preset="card"
      label-width="80"
      width="50%"
    >
      <ModelForm />
    </ProModalForm>
    <!-- 承办人确认 -->
    <ProModalForm
      :form="confirmForm"
      title="承办人确认"
      :loading="fetchPutTodoProcessLoading"
      preset="card"
      label-width="80"
      width="50%"
    >
      <ConfirmForm />
    </ProModalForm>
    <!-- 详情 -->
    <ProModalForm
      :form="detailForm"
      width="50%"
      preset="card"
      title="待办事项详情"
      readonly
      label-placement="left"
      :footer="false"
    >
      <DetailForm @confirm="handleConfirm" />
    </ProModalForm>
  </div>
</template>
