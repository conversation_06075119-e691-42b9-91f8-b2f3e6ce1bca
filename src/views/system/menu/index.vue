<script setup lang="tsx">
import type { DataTableColumns, TreeInst, TreeOption } from 'naive-ui';
import { useBoolean, useLoading } from '@sa/hooks';
import { enableStatusRecord, enableVisibleRecord, menuIsFrameRecord, menuTypeRecord } from '@/constants/business';
import { fetchDeleteMenu, fetchGetMenuList } from '@/service/api/system/menu';
import { useAppStore } from '@/store/modules/app';
import { handleTree } from '@/utils/common';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';
import ButtonIcon from '@/components/custom/button-icon.vue';
import MenuOperateDrawer from './modules/menu-operate-drawer.vue';

const defaultIcon = import.meta.env.VITE_MENU_ICON;

const appStore = useAppStore();
const editingData = ref<Api.System.Menu>();
const operateType = ref<NaiveUI.TableOperateType>('add');
const { loading, startLoading, endLoading } = useLoading();
const { bool: drawerVisible, setTrue: openDrawer } = useBoolean();
const { loading: btnLoading, startLoading: startBtnLoading, endLoading: endBtnLoading } = useLoading();
/** tree pattern name , use tree search */
const name = ref<string>();
const createType = ref<Api.System.MenuType>();
const createPid = ref<CommonType.IdType>(0);
const currentMenu = ref<Api.System.Menu>();
const treeData = ref<Api.System.Menu[]>([]);
const checkedKeys = ref<CommonType.IdType[]>([0]);
const expandedKeys = ref<CommonType.IdType[]>([0]);

const menuTreeRef = ref<TreeInst>();
const btnData = ref<Api.System.MenuList>([]);

const getMeunTree = async () => {
  try {
    startLoading();
    const { data, error } = await fetchGetMenuList();
    if (error) return;
    treeData.value = [
      {
        menuId: 0,
        menuName: $t('page.manage.menu.rootName'),
        icon: 'material-symbols:home-outline-rounded',
        children: handleTree(data, { idField: 'menuId', filterFn: item => item.menuType !== 'F' })
      }
    ] as Api.System.Menu[];
  } catch (e) {
    console.log(e);
  } finally {
    endLoading();
  }
};

getMeunTree();

async function handleSubmitted(menuType?: Api.System.MenuType) {
  if (menuType === 'F') {
    await getBtnMenuList();
    return;
  }
  await getMeunTree();
  if (operateType.value === 'edit') {
    currentMenu.value = menuTreeRef.value?.getCheckedData().options[0] as Api.System.Menu;
  }
}

function handleAddMenu(pid: CommonType.IdType) {
  createPid.value = pid;
  createType.value = pid === 0 ? 'M' : 'C';
  operateType.value = 'add';
  openDrawer();
}

function handleUpdateMenu() {
  operateType.value = 'edit';
  editingData.value = currentMenu.value;
  openDrawer();
}

async function handleDeleteMenu(id?: number) {
  const { error } = await fetchDeleteMenu({ id });
  if (error) return;
  window.$message?.success($t('common.deleteSuccess'));
  expandedKeys.value.filter(item => !checkedKeys.value.includes(item));
  currentMenu.value = undefined;
  checkedKeys.value = [];
  getMeunTree();
}

function renderPrefix({ option }: { option: TreeOption }) {
  const renderLocalIcon = String(option.icon).startsWith('local-icon-');
  const icon = renderLocalIcon ? undefined : String(option.icon);
  const localIcon = renderLocalIcon ? String(option.icon).replace('local-icon-', 'menu-') : undefined;
  return <SvgIcon icon={icon || defaultIcon} localIcon={localIcon} />;
}

function renderSuffix({ option }: { option: TreeOption }) {
  if (!['M'].includes(String(option.menuType))) {
    return null;
  }

  return (
    <div class="flex-center gap-8px">
      <ButtonIcon
        text
        class="h-18px"
        icon="ic-round-plus"
        tooltip-content={$t('page.manage.menu.addChildMenu')}
        onClick={(event: Event) => {
          event.stopPropagation();
          handleAddMenu(option.menuId as CommonType.IdType);
        }}
      />
    </div>
  );
}

function reset() {
  name.value = undefined;
  getMeunTree();
}

function handleClickTree(option: Array<TreeOption | null>) {
  checkedKeys.value = option?.map(item => item?.menuId as CommonType.IdType);

  const menu = option[0] as Api.System.Menu;
  if (menu?.menuId === 0) {
    return;
  }
  currentMenu.value = menu;
  getBtnMenuList();
}

const tagMap: Record<'0' | '1' | '2', NaiveUI.ThemeColor> = {
  '0': 'success',
  '1': 'warning',
  '2': 'primary'
};

let controller = new AbortController();

async function getBtnMenuList() {
  if (!currentMenu.value?.menuId) {
    return;
  }
  controller.abort();
  controller = new AbortController();
  try {
    startBtnLoading();
    btnData.value = [];
    const { data, error } = await fetchGetMenuList({ parentId: currentMenu.value?.menuId, menuType: 'F' });
    if (error) return;
    btnData.value = data || [];
  } catch (e) {
    console.log(e);
  } finally {
    endBtnLoading();
  }
}

function addBtnMenu() {
  operateType.value = 'add';
  createType.value = 'F';
  createPid.value = currentMenu.value?.menuId || 0;
  openDrawer();
}

function handleDeleteBtnMenu(id: number) {
  handleDeleteMenu(id);
}

function handleUpdateBtnMenu(row: Api.System.Menu) {
  operateType.value = 'edit';
  editingData.value = row;
  openDrawer();
}

const btnColumns: DataTableColumns<Api.System.Menu> = [
  {
    key: 'index',
    width: 64,
    align: 'center',
    title() {
      return (
        <NButton circle type="primary" size="small" onClick={() => addBtnMenu()}>
          {{
            icon: () => (
              <NIcon>
                <SvgIcon icon="ic-round-plus" />
              </NIcon>
            )
          }}
        </NButton>
      );
    },
    render(_, index) {
      return index + 1;
    }
  },
  {
    title: $t('page.manage.menu.menuName'),
    key: 'menuName',
    minWidth: 120
  },
  {
    title: $t('page.manage.menu.perms'),
    key: 'perms',
    align: 'center',
    minWidth: 120
  },
  {
    title: $t('page.manage.menu.status'),
    key: 'status',
    minWidth: 80,
    align: 'center',
    render(row) {
      return <NTag type={tagMap[row.status]}>{enableStatusRecord[row.status]}</NTag>;
    }
  },
  {
    title: $t('page.manage.menu.createTime'),
    key: 'createTime',
    align: 'center',
    minWidth: 150
  },
  {
    title: $t('common.action'),
    key: 'actions',
    width: 80,
    align: 'center',
    render(row) {
      const divider = () => {
        return <NDivider vertical />;
      };

      const editBtn = () => {
        return (
          <ButtonIcon
            text
            type="primary"
            icon="material-symbols:drive-file-rename-outline-outline"
            tooltipContent={$t('common.edit')}
            onClick={() => handleUpdateBtnMenu(row)}
          />
        );
      };

      const deleteBtn = () => {
        return (
          <ButtonIcon
            text
            type="error"
            icon="material-symbols:delete-outline"
            tooltipContent={$t('common.delete')}
            popconfirmContent={$t('common.confirmDelete')}
            onPositiveClick={() => handleDeleteBtnMenu(Number(row?.menuId))}
          />
        );
      };

      return (
        <div class="flex-center gap-8px">
          {editBtn()}
          {divider()}
          {deleteBtn()}
        </div>
      );
    }
  }
];
</script>

<template>
  <TableSiderLayout default-expanded>
    <template #header>{{ $t('page.manage.menu.title') }}</template>
    <template #header-extra>
      <ButtonIcon
        size="small"
        icon="material-symbols:add-rounded"
        class="h-28px text-icon"
        :tooltip-content="$t('page.manage.menu.addMenu')"
        @click.stop="handleAddMenu(0)"
      />
      <ButtonIcon
        size="small"
        icon="material-symbols:refresh-rounded"
        class="h-28px text-icon"
        :tooltip-content="$t('common.refresh')"
        @click.stop="reset"
      />
    </template>
    <template #sider>
      <div class="flex gap-6px">
        <NInput v-model:value="name" size="small" :placeholder="$t('page.manage.menu.form.menuName.required')" />
      </div>
      <NSpin :show="loading" class="infinite-scroll">
        <NTree
          ref="menuTreeRef"
          v-model:checked-keys="checkedKeys"
          v-model:expanded-keys="expandedKeys"
          :cancelable="false"
          block-node
          show-line
          :data="treeData"
          :default-expanded-keys="[0]"
          :show-irrelevant-nodes="false"
          :pattern="name"
          class="menu-tree h-full min-h-200px py-3"
          key-field="menuId"
          label-field="menuName"
          virtual-scroll
          checkable
          :render-prefix="renderPrefix"
          :render-suffix="renderSuffix"
          @update:selected-keys="(_: Array<string & number>, option: Array<TreeOption | null>) => handleClickTree(option)"
        >
          <template #empty>
            <NEmpty :description="$t('page.manage.menu.emptyMenu')" class="h-full min-h-200px justify-center" />
          </template>
        </NTree>
      </NSpin>
    </template>
    <div class="h-full flex-col-stretch gap-16px">
      <div v-if="currentMenu">
        <NCard
          :title="$t('page.manage.menu.menuDetail')"
          :bordered="false"
          size="small"
          class="max-h-50% card-wrapper"
          content-class="overflow-auto mb-12px"
        >
          <template #header-extra>
            <NSpace>
              <NButton
                v-if="currentMenu?.menuType === 'M'"
                size="small"
                ghost
                type="primary"
                @click="handleAddMenu(currentMenu?.menuId || 0)"
              >
                <template #icon>
                  <icon-material-symbols-add-rounded />
                </template>
                {{ $t('page.manage.menu.addChildMenu') }}
              </NButton>
              <NButton size="small" ghost type="primary" @click="handleUpdateMenu">
                <template #icon>
                  <icon-material-symbols-drive-file-rename-outline-outline />
                </template>
                {{ $t('common.edit') }}
              </NButton>
              <NPopconfirm @positive-click="() => handleDeleteMenu(currentMenu?.menuId)">
                <template #trigger>
                  <NButton size="small" ghost type="error" :disabled="btnData.length > 0 || btnLoading">
                    <template #icon>
                      <icon-material-symbols-delete-outline />
                    </template>
                    {{ $t('common.delete') }}
                  </NButton>
                </template>
                {{ $t('common.confirmDelete') }}
              </NPopconfirm>
            </NSpace>
          </template>
          <NDescriptions
            label-placement="left"
            size="small"
            bordered
            :column="appStore.isMobile ? 1 : 2"
            label-class="w-20% min-w-88px"
            content-class="w-100px"
          >
            <NDescriptionsItem :label="$t('page.manage.menu.menuType')">
              <NTag class="m-1" size="small" type="primary">
                {{ currentMenu?.menuType ? menuTypeRecord[currentMenu.menuType] : '' }}
              </NTag>
            </NDescriptionsItem>
            <NDescriptionsItem :label="$t('page.manage.menu.status')">
              <NTag class="m-1" size="small" type="primary">{{ enableStatusRecord[currentMenu?.status] }}</NTag>
            </NDescriptionsItem>
            <NDescriptionsItem :label="$t('page.manage.menu.menuName')">
              {{ currentMenu.menuName }}
            </NDescriptionsItem>
            <NDescriptionsItem v-if="currentMenu?.menuType === 'C'" :label="$t('page.manage.menu.component')">
              {{ currentMenu.component }}
            </NDescriptionsItem>
            <NDescriptionsItem
              :label="currentMenu.isFrame !== '0' ? $t('page.manage.menu.path') : $t('page.manage.menu.externalPath')"
            >
              {{ currentMenu.path }}
            </NDescriptionsItem>
            <NDescriptionsItem
              v-if="currentMenu?.menuType === 'C'"
              :label="currentMenu.isFrame !== '2' ? $t('page.manage.menu.query') : $t('page.manage.menu.iframeQuery')"
            >
              {{ currentMenu.queryParam }}
            </NDescriptionsItem>
            <NDescriptionsItem v-if="currentMenu?.menuType !== 'M'" :label="$t('page.manage.menu.perms')">
              {{ currentMenu.perms }}
            </NDescriptionsItem>
            <NDescriptionsItem :label="$t('page.manage.menu.isFrame')">
              <NTag v-if="currentMenu.isFrame" class="m-1" size="small" :type="tagMap[currentMenu.isFrame]">
                {{ menuIsFrameRecord[currentMenu.isFrame] }}
              </NTag>
            </NDescriptionsItem>
            <NDescriptionsItem :label="$t('page.manage.menu.visible')">
              <NTag v-if="currentMenu.visible" class="m-1" size="small" :type="tagMap[currentMenu.visible]">
                {{ enableVisibleRecord[currentMenu.visible] }}
              </NTag>
            </NDescriptionsItem>
            <NDescriptionsItem v-if="currentMenu?.menuType === 'C'" :label="$t('page.manage.menu.isCache')">
              <NTag v-if="currentMenu.isCache" class="m-1" size="small" :type="tagMap[currentMenu.isCache]">
                {{ currentMenu.isCache === '1' ? $t('page.manage.menu.cache') : $t('page.manage.menu.noCache') }}
              </NTag>
            </NDescriptionsItem>
          </NDescriptions>
        </NCard>

        <NCard
          :title="$t('page.manage.menu.buttonPermissionList')"
          :bordered="false"
          size="small"
          class="h-full overflow-auto card-wrapper"
          content-class="overflow-auto mb-12px"
        >
          <template #header-extra>
            <ButtonIcon
              size="small"
              icon="ic-round-refresh"
              class="h-28px text-icon"
              :tooltip-content="$t('common.refresh')"
              @click.stop="getBtnMenuList"
            />
          </template>
          <NDataTable class="h-full" :loading="btnLoading" :columns="btnColumns" :data="btnData" />
        </NCard>
      </div>
      <NCard v-else :bordered="false" size="small" class="h-full card-wrapper">
        <NEmpty class="h-full flex-center" size="large" />
      </NCard>
    </div>
    <MenuOperateDrawer
      v-model:visible="drawerVisible"
      :operate-type="operateType"
      :row-data="editingData"
      :tree-data="treeData"
      :pid="createPid"
      :menu-type="createType"
      @submitted="handleSubmitted"
    />
  </TableSiderLayout>
</template>

<style scoped lang="scss">
:deep(.infinite-scroll) {
  height: calc(100vh - 224px - var(--calc-footer-height, 0px)) !important;
  max-height: calc(100vh - 224px - var(--calc-footer-height, 0px)) !important;
}

@media screen and (max-width: 1024px) {
  :deep(.infinite-scroll) {
    height: calc(100vh - 227px - var(--calc-footer-height, 0px)) !important;
    max-height: calc(100vh - 227px - var(--calc-footer-height, 0px)) !important;
  }
}

:deep(.n-spin-content) {
  height: 100%;
}

:deep(.n-tree-node-checkbox) {
  display: none;
}

:deep(.n-data-table-base-table) {
  height: 100% !important;
}

.menu-tree {
  :deep(.n-tree-node) {
    height: 25px;
  }

  :deep(.n-tree-node-switcher) {
    height: 25px;
  }

  :deep(.n-tree-node-switcher__icon) {
    font-size: 16px !important;
    height: 16px !important;
    width: 16px !important;
  }
}
</style>
