<script setup lang="tsx">
import type { ProDataTableColumns, ProSearchFormColumns } from 'pro-naive-ui';
import { createProModalForm, createProSearchForm, renderProDateText, useNDataTable, useRequest } from 'pro-naive-ui';
import { N<PERSON>utton, NPopconfirm, NTag } from 'naive-ui';
import { DictStatusOptions } from '@/enum';
import { $t } from '@/locales';
import {
  fetchDeleteDictData,
  fetchGetDictData,
  fetchGetDictDataList,
  fetchGetDictTypeList,
  fetchPostDictData,
  fetchPutDictData
} from '@/service/api/system/dict';
import ModelForm from './modules/model-form.vue';

interface Row {
  id: number;
  name: string;
  dictType: string;
  status: number;
  remark: string;
  createTime: number;
}

interface SearchParams {
  name: string;
  dictType: string;
  status: number;
  createTime: string;
}

const route = useRoute();
const searchForm = createProSearchForm({
  initialValues: {
    dictType: route.query.type as string
  }
});

/* 新增 */
const { loading: fetchPostDictLoading, runAsync: runAsyncFetchPostDict } = useRequest(
  async val => {
    const promises = val.dictList.map(item => fetchPostDictData(item));
    const results = await Promise.allSettled(promises);

    results.forEach((result, index) => {
      if (result.status === 'rejected') {
        window.$message?.error(`字典项失败:`, val.dictList[index], result.reason);
      }
    });
  },
  {
    manual: true
  }
);

/* 编辑 */
const { loading: fetchPutDictLoading, runAsync: runAsyncFetchPutDict } = useRequest(fetchPutDictData, {
  manual: true
});
/* 删除 */
const { runAsync: runAsyncFetchDeleteDict } = useRequest(fetchDeleteDictData, {
  manual: true
});
// 删除
/* 获取字典类型 */
const { data: deptOptions } = useRequest(fetchGetDictTypeList);
const {
  table: { tableProps, onChange },
  search: { proSearchFormProps }
} = useNDataTable(
  async ({ current, pageSize }, values) => {
    return fetchGetDictDataList({
      pageSize,
      pageNo: current,
      ...values
    }).then(({ data }) => {
      return {
        list: data?.list || [],
        total: data?.total || 0
      };
    });
  },
  {
    form: searchForm
  }
);

const columns: ProDataTableColumns<Row> = [
  {
    title: '字典编号',
    align: 'center',
    path: 'id'
  },
  {
    title: '字典类型',
    align: 'center',
    path: 'dictType'
  },
  {
    title: '数据标签',
    align: 'center',
    path: 'label'
  },
  {
    title: '字典键值',
    align: 'center',
    path: 'value'
  },
  {
    title: '排序',
    align: 'center',
    path: 'sort'
  },
  {
    title: '状态',
    align: 'center',
    render: row => {
      return (
        <div>
          <NTag type={row.status === 0 ? 'success' : 'error'} size="small">
            {row.status === 0 ? '开启' : '关闭'}
          </NTag>
        </div>
      );
    }
  },
  {
    title: '备注',
    align: 'center',
    path: 'remark'
  },

  {
    title: '创建时间',
    align: 'center',
    render: row =>
      renderProDateText(row.createTime, {
        pattern: 'datetime'
      })
  },
  {
    title: $t('common.operate'),
    width: 180,
    align: 'center',
    render: row => {
      return (
        <div class="flex-center gap-8px">
          <NButton type="primary" size="small" ghost onClick={() => handleOpenModal(row)}>
            {$t('common.edit')}
          </NButton>
          <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
            {{
              default: () => $t('common.confirmDelete'),
              trigger: () => (
                <NButton type="error" ghost size="small">
                  {$t('common.delete')}
                </NButton>
              )
            }}
          </NPopconfirm>
        </div>
      );
    }
  }
];

const searchColumns: ProSearchFormColumns<SearchParams> = [
  {
    title: '字典名称',
    path: 'dictType',
    field: 'select',
    fieldProps() {
      return {
        options: deptOptions.value?.data.map(item => {
          return {
            label: item.name,
            value: item.type
          };
        })
      };
    }
  },
  {
    title: '数据标签',
    path: 'label'
  },
  {
    title: '状态',
    path: 'status',
    field: 'select',
    fieldProps: {
      options: DictStatusOptions
    }
  }
];

const modalForm = createProModalForm<Api.SystemManage.DictTypeRespVO>({
  onSubmit: async values => {
    try {
      if (modalForm.values.value.id) {
        await runAsyncFetchPutDict({
          ...values,
          id: modalForm.values.value.id
        });
        onChange();
        window.$message?.success($t('common.updateSuccess'));
        modalForm.close();
      } else {
        if (values.dictList.length === 0) {
          window.$message?.error('请添加字典数据');
          return;
        }
        await runAsyncFetchPostDict(values);
        onChange({ page: 1 });
        window.$message?.success($t('common.addSuccess'));
        modalForm.close();
      }
    } catch (e) {
      console.log(e);
    }
  }
});

async function handleOpenModal(row?: Row) {
  if (row) {
    const { data, error } = await fetchGetDictData({ id: row.id });
    if (error) {
      return;
    }
    modalForm.values.value = data;
  } else {
    modalForm.values.value = {
      dictType: route.query.type as string
    };
  }
  modalForm.open();
}

async function handleDelete(rowId: number) {
  try {
    await runAsyncFetchDeleteDict({ id: rowId });
    onChange();
    window.$message?.success($t('common.deleteSuccess'));
  } catch (e) {
    console.log(e);
  }
}
</script>

<template>
  <div class="h-full flex flex-col">
    <ProCard class="mb-24px" :show-collapse="false" content-class="!pb-0">
      <ProSearchForm :form="searchForm" :columns="searchColumns" v-bind="proSearchFormProps" />
    </ProCard>
    <ProDataTable title="字典管理" size="small" flex-height :columns="columns" row-key="id" v-bind="tableProps">
      <template #toolbar>
        <NButton size="small" ghost type="primary" @click="handleOpenModal()">
          <template #icon>
            <icon-ic-round-plus class="text-icon" />
          </template>
          {{ $t('common.add') }}
        </NButton>
      </template>
    </ProDataTable>

    <ProModalForm
      :form="modalForm"
      :title="modalForm.values.value.id ? '编辑' : '新增'"
      :loading="modalForm.values.value.id ? fetchPutDictLoading : fetchPostDictLoading"
      preset="card"
      label-width="80"
      width="70%"
    >
      <ModelForm />
    </ProModalForm>
  </div>
</template>
