<script setup lang="ts">
import { useInjectProModalForm } from 'pro-naive-ui';

import { DictStatusOptions } from '@/enum';

const modalForm = useInjectProModalForm();
</script>

<template>
  <ProFormList
    v-if="!modalForm.values.value.id"
    path="dictList"
    :copy-button-props="false"
    only-show-first-item-label
    :min="1"
    :creator-initial-value="() => ({ dictType: modalForm.values.value.dictType, status: 0 })"
    label-align="center"
  >
    <ProInput
      title="字典类型"
      path="dictType"
      :field-props="{
        disabled: true
      }"
    />
    <ProInput title="数据标签" path="label" required />
    <ProInput title="数据键值" path="value" required />
    <ProDigit title="序号" path="sort" required />
    <ProRadioGroup
      title="状态"
      path="status"
      required
      :field-props="{
        options: DictStatusOptions
      }"
    />
    <ProTextarea title="备注" path="remark" />
  </ProFormList>
  <div v-else>
    <ProInput
      title="字典类型"
      path="dictType"
      :field-props="{
        disabled: true
      }"
    />
    <ProInput title="数据标签" path="label" required />
    <ProInput title="数据键值" path="value" required />
    <ProDigit title="序号" path="sort" required />
    <ProRadioGroup
      title="状态"
      path="status"
      required
      :field-props="{
        options: DictStatusOptions
      }"
    />
    <ProTextarea title="备注" path="remark" />
  </div>
</template>

<style scoped></style>
