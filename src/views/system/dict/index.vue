<script setup lang="tsx">
import { NButton, NPopconfirm, NTag } from 'naive-ui';
import type { ProDataTableColumns, ProSearchFormColumns } from 'pro-naive-ui';
import { createProModalForm, createProSearchForm, renderProDateText, useNDataTable, useRequest } from 'pro-naive-ui';

import { DictStatusOptions } from '@/enum';
import { $t } from '@/locales';
import {
  fetchDeleteDict,
  fetchGetDict,
  fetchGetDictList,
  fetchPostDict,
  fetchPutDict
} from '@/service/api/system/dict';
import { formatTimestampRange } from '@/utils/date-format';
import ModelForm from '@/views/system/dict/modules/model-form.vue';

interface Row {
  id: number;
  name: string;
  type: string;
  status: number;
  remark: string;
  createTime: number;
}

interface SearchParams {
  name: string;
  type: string;
  status: number;
  createTime: string;
}

const router = useRouter();

const searchForm = createProSearchForm();

/* 新增 */
const { loading: fetchPostDictLoading, runAsync: runAsyncFetchPostDict } = useRequest(fetchPostDict, {
  manual: true
});

/* 编辑 */
const { loading: fetchPutDictLoading, runAsync: runAsyncFetchPutDict } = useRequest(fetchPutDict, {
  manual: true
});
/* 删除 */
const { runAsync: runAsyncFetchDeleteDict } = useRequest(fetchDeleteDict, {
  manual: true
});

const {
  table: { tableProps, onChange },
  search: { proSearchFormProps }
} = useNDataTable(
  async ({ current, pageSize }, values) => {
    const { createTime, ...ret } = values;
    return fetchGetDictList({
      ...ret,
      pageSize,
      pageNo: current,
      createTime: formatTimestampRange(createTime)
    }).then(({ data }) => {
      return {
        list: data?.list || [],
        total: data?.total || 0
      };
    });
  },
  {
    form: searchForm
  }
);

const columns: ProDataTableColumns<Row> = [
  {
    title: '字典编号',
    align: 'center',
    path: 'id'
  },
  {
    title: '字典名称',
    align: 'center',
    path: 'name'
  },
  {
    title: '字典类型',
    align: 'center',
    path: 'type'
  },
  {
    title: '状态',
    align: 'center',
    render: row => {
      return (
        <div>
          <NTag type={row.status === 0 ? 'success' : 'error'} size="small">
            {row.status === 0 ? '开启' : '关闭'}
          </NTag>
        </div>
      );
    }
  },
  {
    title: '备注',
    align: 'center',
    path: 'remark'
  },
  {
    title: '创建时间',
    align: 'center',
    render: row =>
      renderProDateText(row.createTime, {
        pattern: 'datetime'
      })
  },
  {
    title: $t('common.operate'),
    width: 180,
    align: 'center',
    render: row => {
      return (
        <div class="flex-center gap-8px">
          <NButton type="primary" size="small" ghost onClick={() => handleOpenModal(row)}>
            {$t('common.edit')}
          </NButton>
          <NButton
            type="success"
            size="small"
            ghost
            onClick={() => router.push({ path: '/system/dict-type', query: { type: row.type } })}
          >
            数据
          </NButton>
          <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
            {{
              default: () => $t('common.confirmDelete'),
              trigger: () => (
                <NButton type="error" ghost size="small">
                  {$t('common.delete')}
                </NButton>
              )
            }}
          </NPopconfirm>
        </div>
      );
    }
  }
];

const searchColumns: ProSearchFormColumns<SearchParams> = [
  {
    title: '字典名称',
    path: 'name'
  },
  {
    title: '字典类型',
    path: 'type'
  },
  {
    title: '状态',
    path: 'status',
    field: 'select',
    showFeedback: false,
    fieldProps: {
      options: DictStatusOptions
    }
  },
  {
    title: '创建时间',
    path: 'createTime',
    field: 'date-range'
  }
];

const modalForm = createProModalForm<Api.SystemManage.DictTypeRespVO>({
  onSubmit: async values => {
    try {
      if (modalForm.values.value.id) {
        await runAsyncFetchPutDict({
          ...values,
          id: modalForm.values.value.id
        });
        onChange();
        window.$message?.success($t('common.updateSuccess'));
        modalForm.close();
      } else {
        await runAsyncFetchPostDict(values);
        onChange({ page: 1 });

        window.$message?.success($t('common.addSuccess'));
        modalForm.close();
      }
    } catch (e) {
      console.log(e);
    }
  }
});

async function handleOpenModal(row?: Row) {
  if (row) {
    const { data, error } = await fetchGetDict({ id: row.id });
    if (error) {
      return;
    }
    modalForm.values.value = data;
  }
  modalForm.open();
}

async function handleDelete(rowId: number) {
  try {
    await runAsyncFetchDeleteDict({ id: rowId });
    onChange();
    window.$message?.success($t('common.deleteSuccess'));
  } catch (e) {
    console.log(e);
  }
}
</script>

<template>
  <div class="h-full flex flex-col">
    <ProCard class="mb-24px" :show-collapse="false" content-class="!pb-0">
      <ProSearchForm :form="searchForm" :columns="searchColumns" v-bind="proSearchFormProps" />
    </ProCard>
    <ProDataTable title="字典管理" size="small" flex-height :columns="columns" row-key="id" v-bind="tableProps">
      <template #toolbar>
        <NButton size="small" ghost type="primary" @click="handleOpenModal()">
          <template #icon>
            <icon-ic-round-plus class="text-icon" />
          </template>
          {{ $t('common.add') }}
        </NButton>
      </template>
    </ProDataTable>

    <ProModalForm
      :form="modalForm"
      :title="modalForm.values.value.id ? '编辑' : '新增'"
      :loading="modalForm.values.value.id ? fetchPutDictLoading : fetchPostDictLoading"
      preset="card"
      label-width="80"
      width="30%"
    >
      <ModelForm />
    </ProModalForm>
  </div>
</template>
