<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { fetchGetDept, fetchPutDept } from '@/service/api/kip/department';

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  id?: Api.Kip.DeptSelect['id'];
}

interface Emits {
  (e: 'btnClose'): void;
}

const emit = defineEmits<Emits>();
type RuleKey = keyof Pick<Api.Kip.DeptSelect, 'name' | 'deptDescription'>;
const props = defineProps<Props>();
const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();
const rules = ref<Record<RuleKey, App.Global.FormRule>>({
  name: defaultRequiredRule,
  deptDescription: defaultRequiredRule
});

const model = reactive<Api.Kip.DeptSelectForm>(createDefaultModel());

async function handleInitModel() {
  Object.assign(model, createDefaultModel());
  if (props.operateType === 'edit') {
    const { error, data } = await fetchGetDept(props.id);
    if (error) {
      return;
    }
    Object.assign(model, data);
  }
}

function createDefaultModel(): Api.Kip.DeptSelectForm {
  return {
    id: null,
    name: null,
    deptDescription: null
  };
}

async function handleSubmit() {
  try {
    await validate();
    const { error } = await fetchPutDept({ ...model, status: 0 });
    if (error) {
      return false;
    }
    window?.$message?.success($t('common.addSuccess'));
    emit('btnClose');
    return true;
  } catch {
    return false;
  }
}

defineExpose({
  handleSubmit
});
onMounted(() => {
  handleInitModel();
  restoreValidation();
});
</script>

<template>
  <NForm ref="formRef" :model="model" :rules="rules">
    <NFormItem label="部门名称" path="name">
      <NInput v-model:value="model.name" clearable placeholder="请输入部门名称" disabled />
    </NFormItem>
    <NFormItem label="角色定位" path="deptDescription">
      <NInput v-model:value="model.deptDescription" clearable placeholder="请输入角色定位" type="textarea" :rows="3" />
    </NFormItem>
  </NForm>
</template>

<style scoped></style>
