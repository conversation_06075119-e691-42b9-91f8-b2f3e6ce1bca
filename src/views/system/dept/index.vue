<script setup lang="tsx">
import { type TableDataWithIndex } from '@sa/hooks';
import { NButton, NTag } from 'naive-ui';
import { renderProDateText } from 'pro-naive-ui';

import DeptOperateDrawer from './modules/dept-operate-drawer.vue';
import DeptSearch from './modules/dept-search.vue';

import { editRoleBtn } from '@/directives/permission/permi-btn';
import { useTreeTable, useTreeTableOperate } from '@/hooks/common/tree-table';
import { $t } from '@/locales';
import { fetchGetDeptList } from '@/service/api/system/dept';
import { useAppStore } from '@/store/modules/app';

defineOptions({
  name: 'DeptList'
});

const appStore = useAppStore();

const {
  columns,
  columnChecks,
  data,
  getData,
  loading,
  searchParams,
  resetSearchParams,
  expandedRowKeys,
  isCollapse,
  expandAll,
  collapseAll
} = useTreeTable({
  apiFn: fetchGetDeptList,
  apiParams: {
    name: null,
    status: null
  },
  idField: 'id',
  columns: () => [
    {
      key: 'name',
      title: $t('page.system.dept.deptName'),
      align: 'left',
      width: '550'
    },
    {
      key: 'deptDescription',
      title: '角色定位',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'status',
      title: $t('page.system.dept.status'),
      align: 'center',
      minWidth: 120,
      render(row) {
        return <NTag type={row.status === 0 ? 'success' : 'warning'}>{row.status === 0 ? '正常' : '停用'}</NTag>;
      }
    },
    {
      key: 'createTime',
      title: $t('page.system.dept.createTime'),
      align: 'center',
      minWidth: 120,
      render(row) {
        return renderProDateText(row.createTime, {
          pattern: 'datetime'
        });
      }
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 150,
      render: row => {
        return <div class="flex-center gap-8px">{editRoleBtn(['system:dept:desc'], () => edit(row))}</div>;
      }
    }
  ]
});
const { drawerVisible, operateType, editingData, handleEdit } = useTreeTableOperate(data, getData);

async function edit(row: TableDataWithIndex<Api.System.Dept>) {
  handleEdit(row);
}
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <DeptSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getData" />
    <NCard :title="$t('page.system.dept.title')" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :loading="loading"
          :show-delete="false"
          :show-add="false"
          @refresh="getData"
        >
          <template #prefix>
            <NButton v-if="!isCollapse" :disabled="!data.length" size="small" @click="expandAll">
              <template #icon>
                <icon-quill:expand />
              </template>
              {{ $t('page.system.dept.expandAll') }}
            </NButton>
            <NButton v-if="isCollapse" :disabled="!data.length" size="small" @click="collapseAll">
              <template #icon>
                <icon-quill:collapse />
              </template>
              {{ $t('page.system.dept.collapseAll') }}
            </NButton>
          </template>
        </TableHeaderOperation>
      </template>
      <NDataTable
        v-model:expanded-row-keys="expandedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :indent="32"
        :flex-height="!appStore.isMobile"
        :scroll-x="962"
        :loading="loading"
        :row-key="row => row.id"
        class="sm:h-full"
      />
      <DeptOperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getData"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
