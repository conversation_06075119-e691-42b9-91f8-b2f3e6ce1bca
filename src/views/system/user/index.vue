<script setup lang="tsx">
import { NButton, NDropdown, NSwitch, useDialog } from 'naive-ui';
import { computed } from 'vue';
import { $t } from '@/locales';
import { useAppStore } from '@/store/modules/app';
import { useTable } from '@/hooks/common/table';
import { fetchDeleteUser, fetchGetUserPage, fetchPushAccount, fetchUpdateUserStatus } from '@/service/api/system/user';
import { renderModalBtn } from '@/components/re-modal';
import { editRoleBtn, roleBtn } from '@/directives/permission/permi-btn';
import { useAuth } from '@/hooks/business/auth';
import UserOperateDrawer from './modules/user-operate-drawer.vue';
import UserSearch from './modules/user-search.vue';
import AssigningRoles from './modules/assigning-roles.vue';
import ResetPwd from './modules/reset-pwd.vue';

const appStore = useAppStore();
const dialog = useDialog();
const { hasAuth } = useAuth();

const options = computed(() => {
  const baseOptions = [
    {
      label: '删除',
      key: 'delete',
      auth: ['system:user:delete']
    },
    {
      label: '分配角色',
      key: 'assigningRoles',
      auth: ['system:permission:assign-user-role']
    },
    {
      label: '重置密码',
      key: 'resetPassword',
      auth: ['system:user:update-password']
    },
    {
      label: '账号推送',
      key: 'pushAccount',
      auth: ['system:user:send']
    }
  ];

  return baseOptions.filter(option => hasAuth(option.auth));
});

const { columns, columnChecks, data, getData, getDataByPage, loading, mobilePagination, searchParams } = useTable({
  apiFn: fetchGetUserPage,
  showTotal: true,
  apiParams: {
    pageNo: 1,
    pageSize: 10,
    status: null,
    username: null,
    sex: null,
    nickname: null,
    mobile: null,
    email: null
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48
    },
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'nickname',
      title: $t('page.manage.user.nickName'),
      align: 'center',
      minWidth: 100
    },
    {
      key: 'mobile',
      title: $t('page.manage.user.userPhone'),
      align: 'center',
      width: 120
    },
    {
      key: 'email',
      title: $t('page.manage.user.userEmail'),
      align: 'center',
      minWidth: 200
    },
    {
      key: 'status',
      title: $t('page.manage.user.userStatus'),
      align: 'center',
      width: 100,
      render: row => {
        if (row.status === null) return null;
        const isEnabled = row.status === 0;
        return (
          <NSwitch
            value={isEnabled}
            loading={row.switchLoading}
            onUpdateValue={async (val: boolean) => {
              row.switchLoading = true;
              const newStatus = val ? 0 : 1;

              try {
                const { error } = await fetchUpdateUserStatus({ id: row.id, status: newStatus });
                if (error) {
                  return;
                }
                row.status = newStatus;
                window.$message?.success($t('common.updateSuccess'));
              } catch (e) {
                console.error(e);
              } finally {
                row.switchLoading = false;
              }
            }}
          />
        );
      }
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 130,
      render: row => (
        <div class="flex-center gap-8px">
          {editRoleBtn(['system:user:update'], () => handleOperate('edit', row))}
          {options.value.length ? (
            <NDropdown
              trigger="hover"
              placement="right-start"
              options={options.value}
              onSelect={(key: string) => {
                if (key === 'delete') {
                  openDeleteConfirm(row?.id);
                } else if (key === 'assigningRoles') {
                  handleAssigningRoles(row);
                } else if (key === 'resetPassword') {
                  handleResetPassword(row);
                } else if (key === 'pushAccount') {
                  handlePushAccount(row);
                }
              }}
            >
              <NButton size="small" type="primary" ghost>
                ...
              </NButton>
            </NDropdown>
          ) : null}
        </div>
      )
    }
  ]
});
function handlePushAccount(row: Api.SystemManage.User) {
  const d = dialog.info({
    title: '账号推送',
    content: `即将向${row.nickname}发送系统登录信息。发送前请确认账号已启用并完成权限配置，否则${row.nickname}在登录后可能出现无法进入系统或菜单显示为空的情况。`,
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: async () => {
      d.loading = true;
      await fetchPushAccount({ id: row.id });
      window.$message?.success('推送成功');
      d.loading = false;
    }
  });
}
function handleResetPassword(row) {
  renderModalBtn(
    ResetPwd,
    { operateType: 'edit', rowData: row },
    {
      title: '重置密码',
      style: {
        width: '50%'
      },
      func: getData
    }
  );
}
function handleOperate(type: NaiveUI.TableOperateType, rowData: Api.SystemManage.User | null = null) {
  renderModalBtn(
    UserOperateDrawer,
    { operateType: type, rowData },
    {
      title: type === 'add' ? $t('page.manage.user.addUser') : $t('page.manage.user.editUser'),
      style: {
        width: '50%'
      },
      func: getData
    }
  );
}
function handleAssigningRoles(row: Api.SystemManage.User) {
  renderModalBtn(
    AssigningRoles,
    { operateType: 'edit', rowData: row },
    {
      title: '分配角色',
      style: {
        width: '50%'
      },
      func: getData
    }
  );
}
function openDeleteConfirm(id: number) {
  const d = dialog.info({
    title: $t('common.tip'),
    content: $t('common.confirmDelete'),
    positiveText: $t('common.confirm'),
    negativeText: $t('common.cancel'),
    onPositiveClick: async () => {
      d.loading = true;
      await handleDelete(id);
      d.loading = false;
    }
  });
}

async function handleDelete(id: number) {
  try {
    const { error } = await fetchDeleteUser({ id });
    if (error) return;
    window.$message?.success($t('common.deleteSuccess'));
    await getData();
  } catch (e) {
    console.error(e);
  }
}
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <UserSearch v-model:model="searchParams" @reset="getDataByPage" @search="getDataByPage" />
    <NCard :title="$t('page.manage.user.title')" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :loading="loading"
          :permission="['system:user:create']"
          @add="handleOperate('add')"
          @refresh="getData"
        />
      </template>
      <NDataTable
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="962"
        :loading="loading"
        remote
        :row-key="row => row.id"
        :pagination="mobilePagination"
        class="sm:h-full"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
