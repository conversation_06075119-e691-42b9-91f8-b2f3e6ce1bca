<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { fetchGetSimpleRoleList } from '@/service/api/system/role';
import { fetchGetUserRoles, fetchUpdateUserRoles } from '@/service/api/system/user';

defineOptions({
  name: 'UserOperateDrawer'
});

interface Props {
  /** the edit row data */
  rowData?: Api.SystemManage.User | null;
}

const props = defineProps<Props>();

const { formRef, restoreValidation } = useNaiveForm();
type Model = Pick<Api.SystemManage.User, 'id' | 'username' | 'nickname' | 'roleIds'>;

const model = ref(createDefaultModel());

function createDefaultModel(): Model {
  return {
    id: undefined,
    username: '',
    nickname: '',
    roleIds: []
  };
}

/** the enabled role options */
const roleOptions = ref<CommonType.Option<string>[]>([]);

async function getRoleOptions() {
  const { error, data } = await fetchGetSimpleRoleList();

  if (!error) {
    roleOptions.value = data.map(item => ({
      label: item.name,
      value: item.id
    }));
  }
}

function handleInitModel() {
  model.value = createDefaultModel();
  model.value.id = props.rowData?.id;
  model.value.username = props.rowData?.username || '';
  model.value.nickname = props.rowData?.nickname || '';
  handleGetDetail();
}
async function handleGetDetail() {
  try {
    const { data, error } = await fetchGetUserRoles({ userId: props.rowData?.id });
    if (error) {
      return;
    }
    model.value.roleIds = data || [];
  } catch (e) {
    console.log(e);
  }
}
async function handleSubmit() {
  try {
    const { error } = await fetchUpdateUserRoles({
      userId: model.value.id,
      roleIds: model.value.roleIds
    });
    if (error) {
      return false;
    }
    window.$message?.success($t('common.updateSuccess'));
    return true;
  } catch (e) {
    console.log(e);
    return false;
  }
}
defineExpose({
  handleSubmit
});
onMounted(() => {
  getRoleOptions();
  restoreValidation();
  handleInitModel();
});
</script>

<template>
  <NForm ref="formRef" :model="model">
    <NFormItem :label="$t('page.manage.user.userName')" path="username">
      <NInput
        v-model:value="model.username"
        disabled
        :placeholder="$t('page.manage.user.form.userName')"
        :input-props="{
          name: 'username-fake-' + Math.random().toString(36).slice(2),
          autocomplete: 'off'
        }"
      />
    </NFormItem>

    <NFormItem :label="$t('page.manage.user.nickName')" path="nickname">
      <NInput
        v-model:value="model.nickname"
        disabled
        :placeholder="$t('page.manage.user.form.nickName')"
        :input-props="{
          name: 'nickname-fake-' + Math.random().toString(36).slice(2),
          autocomplete: 'off'
        }"
      />
    </NFormItem>
    <NFormItem :label="$t('page.manage.user.userRole')" path="roles">
      <NSelect
        v-model:value="model.roleIds"
        multiple
        :options="roleOptions"
        :placeholder="$t('page.manage.user.form.userRole')"
      />
    </NFormItem>
  </NForm>
</template>

<style scoped></style>
