<script setup lang="ts">
import { onMounted, reactive } from "vue";
import type { FormRules } from "naive-ui";
import { useNaiveForm } from "@/hooks/common/form";
import { $t } from "@/locales";
import { useAuthStore } from "@/store/modules/auth";
import { fetchResetPassword } from "@/service/api/system/user";
import { MD5 } from "crypto-js";

interface Props {
  /** the edit row data */
  rowData?: Api.SystemManage.User | null;
}

const props = defineProps<Props>();
const authStore = useAuthStore();
const { formRef, validate, restoreValidation } = useNaiveForm();
const model = reactive({
  password: "",
});
const rules = reactive<FormRules>({
  password: [
    {
      required: true,
      message: "请输入密码",
      trigger: "blur",
    },
  ],
});
async function handleSubmit() {
  await validate();

  const { error } = await fetchResetPassword({
    password: MD5(model.password).toString().slice(10, 25),
    id: props.rowData?.id,
  });
  if (error) return false;
  window?.$message?.success($t("common.modifySuccess"));
  return true;
}

defineExpose({
  handleSubmit,
});
onMounted(() => {
  restoreValidation();
});
</script>

<template>
  <NForm ref="formRef" :model="model" :rules="rules">
    <NFormItem label="请输入新密码" path="password">
      <NInput v-model:value="model.password" clearable />
    </NFormItem>
  </NForm>
</template>

<style scoped></style>
