<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { enableStatusOptions } from '@/constants/business';
import { fetchGetUser, fetchPostUser, fetchPutUser } from '@/service/api/system/user';
import { getAllPositionOptions } from '@/utils/async-functions';
import { userGenderOptions } from '@/enum';

defineOptions({
  name: 'UserOperateDrawer'
});

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.SystemManage.User | null;
}

const props = defineProps<Props>();

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();
type Model = Pick<
  Api.SystemManage.User,
  'username' | 'sex' | 'nickname' | 'mobile' | 'email' | 'status' | 'id' | 'password' | 'deptId' | 'postIds' | 'dept'
>;

const model = ref(createDefaultModel());

function createDefaultModel(): Model {
  return {
    deptId: null,
    email: '',
    id: undefined,
    mobile: '',
    nickname: '',
    password: '',
    postIds: [],
    sex: null,
    status: null,
    username: '',
    dept: {}
  };
}

type RuleKey = Extract<keyof Model, 'username' | 'status' | 'nickname' | 'password'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  username: [
    {
      required: true,
      message: '请输入用户名',
      trigger: 'blur'
    },
    {
      pattern: /^[a-zA-Z0-9]+$/,
      message: '用户名格式不正确',
      trigger: 'blur'
    },
    {
      min: 4,
      max: 30,
      message: '用户名长度在 4 到 30 个字符',
      trigger: 'blur'
    }
  ],
  nickname: defaultRequiredRule,
  password: defaultRequiredRule,
  status: defaultRequiredRule
};

function handleInitModel() {
  model.value = createDefaultModel();

  if (props.operateType === 'edit' && props.rowData) {
    model.value.id = props.rowData.id;
    handleGetDetail();
  }
}
async function handleGetDetail() {
  try {
    const { data, error } = await fetchGetUser({ id: props.rowData?.id });
    if (error) {
      return;
    }
    Object.assign(model.value, data);
  } catch (e) {
    console.log(e);
  }
}
async function handleSubmit() {
  await validate();
  try {
    if (props.operateType === 'edit') {
      const { password, ...ret } = model.value;
      const { error } = await fetchPutUser({ ...ret });
      if (error) {
        return false;
      }
      window.$message?.success($t('common.updateSuccess'));
    } else {
      const { error } = await fetchPostUser(model.value);
      if (error) {
        return false;
      }
      window.$message?.success($t('common.addSuccess'));
    }
    return true;
  } catch (e) {
    console.log(e);
    return false;
  }
}
defineExpose({
  handleSubmit
});
onMounted(() => {
  restoreValidation();
  handleInitModel();
});
</script>

<template>
  <NForm ref="formRef" :model="model" :rules="rules">
    <NGrid responsive="screen" item-responsive x-gap="20">
      <NFormItemGi
        v-if="props.operateType === 'add'"
        span="24 m:12"
        :label="$t('page.manage.user.userName')"
        path="username"
      >
        <NInput
          v-model:value="model.username"
          :placeholder="$t('page.manage.user.form.userName')"
          :input-props="{
            name: 'username-fake-' + Math.random().toString(36).slice(2),
            autocomplete: 'off'
          }"
        />
      </NFormItemGi>

      <NFormItemGi span="24 m:12" :label="$t('page.manage.user.nickName')" path="nickname">
        <NInput
          v-model:value="model.nickname"
          :placeholder="$t('page.manage.user.form.nickName')"
          :input-props="{
            name: 'nickname-fake-' + Math.random().toString(36).slice(2),
            autocomplete: 'off'
          }"
        />
      </NFormItemGi>

      <NFormItemGi
        v-if="props.operateType === 'add'"
        span="24 m:12"
        :label="$t('page.manage.user.password')"
        path="password"
      >
        <NInput
          v-model:value="model.password"
          type="password"
          :placeholder="$t('page.manage.user.form.password')"
          :input-props="{
            name: 'new-password',
            autocomplete: 'new-password'
          }"
        />
      </NFormItemGi>
      <NFormItemGi span="24 m:12" :label="$t('page.manage.user.userPhone')" path="mobile">
        <NInput v-model:value="model.mobile" :placeholder="$t('page.manage.user.form.userPhone')" />
      </NFormItemGi>
      <NFormItemGi span="24 m:12" :label="$t('page.manage.user.userEmail')" path="email">
        <NInput v-model:value="model.email" :placeholder="$t('page.manage.user.form.userEmail')" />
      </NFormItemGi>
      <NFormItemGi span="24 m:12" label="归属部门" path="deptId">
        <DeptTreeSelect v-model:value="model.deptId" placeholder="请选择部门" />
      </NFormItemGi>
      <NFormItemGi span="24 m:12" label="岗位" path="postIds">
        <SelectWithSearch
          v-model:value="model.postIds"
          :api-func="getAllPositionOptions"
          :page-size="0"
          placeholder="部门"
          :selected-options="[]"
          multiple
        />
      </NFormItemGi>
      <NFormItemGi span="24 m:12" :label="$t('page.manage.user.userStatus')" path="status">
        <NRadioGroup v-model:value="model.status">
          <NRadio
            v-for="item in enableStatusOptions"
            :key="item.value"
            :value="Number(item.value)"
            :label="$t(item.label)"
          />
        </NRadioGroup>
      </NFormItemGi>
      <NFormItemGi span="24 m:12" :label="$t('page.manage.user.userGender')" path="gender">
        <NRadioGroup v-model:value="model.sex">
          <NRadio
            v-for="item in userGenderOptions"
            :key="item.value"
            :value="Number(item.value)"
            :label="$t(item.label)"
          />
        </NRadioGroup>
      </NFormItemGi>
    </NGrid>
    <!--    <NFormItem :label="$t('page.manage.user.userRole')" path="roles">-->
    <!--      <NSelect-->
    <!--        v-model:value="model.roleIds"-->
    <!--        multiple-->
    <!--        :options="roleOptions"-->
    <!--        :placeholder="$t('page.manage.user.form.userRole')"-->
    <!--      />-->
    <!--    </NFormItem>-->
  </NForm>
</template>

<style scoped></style>
