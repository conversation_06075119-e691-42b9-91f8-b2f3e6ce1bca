<script setup lang="tsx">
import type { ProSearchFormColumns } from 'pro-naive-ui';
import { createProSearchForm, useRequest } from 'pro-naive-ui';

import ModuleConfig from './modules/module-config.vue';
import OperateVersion from './modules/operate-version.vue';
import PrePublish from './modules/pre-publish.vue';
import RemarkForm from './modules/remark-form.vue';
import VersionModules from './modules/version-modules.vue';

import { renderModal, renderModalBtn } from '@/components/re-modal';
import { PublishStatusOptions } from '@/enum';
import { useEmitt } from '@/hooks/common/useEmitt';
import { $t } from '@/locales';
import { fetchDeleteVersion, fetchGetVersionPage } from '@/service/api/system/version';
import { getEnumTagType, getEnumValue } from '@/utils/useful_func';
const { emitter } = useEmitt();
// 删除
const { runAsync: runAsyncFetchDeleteVersion } = useRequest(fetchDeleteVersion, {
  manual: true
});

const loading = ref(false);
const total = ref(0);
const pageCount = ref(0);
const lists = ref<Api.System.VersionRespVO[]>([]);
const searchParams = reactive<Api.System.VersionSearchParams>({
  versionNo: undefined,
  status: undefined,
  actualReleaseAt: undefined,
  pageNo: 1,
  pageSize: 10
});

const searchColumns = ref<ProSearchFormColumns<Api.System.Version>>([
  {
    title: '版本号',
    path: 'versionNo'
  },
  {
    title: '实际发布日期',
    path: 'actualReleaseAt',
    field: 'date-range',
    fieldProps: {
      valueFormat: 'yyyy-MM-dd'
    },
    labelWidth: 100
  }
]);

const searchForm = createProSearchForm({
  defaultCollapsed: true,
  initialValues: searchParams,
  onReset: () => {
    searchParams.pageNo = 1;
    searchParams.pageSize = 10;
    searchParams.versionNo = undefined;
    searchParams.actualReleaseAt = undefined;
    getData();
  },
  onSubmit: () => {
    searchParams.pageNo = 1;
    getData();
  }
});

// 模块配置
function handleModuleConfig() {
  renderModal(
    ModuleConfig,
    {},
    {
      title: '模块配置',
      style: {
        width: '60%'
      },
      contentClass: 'h-[80vh] overflow-auto',
      onClose: () => {
        getData();
      }
    }
  );
}

// 模块版本
function handleVersionModule(rowData: Api.System.VersionRespVO) {
  renderModalBtn(
    VersionModules,
    {
      rowData
    },
    {
      title: '模块版本',
      style: {
        width: '60%'
      },
      contentClass: 'h-[50vh] overflow-auto'
    }
  );
}

// 编辑版本
function handleOperateVersion(operateType: NaiveUI.TableOperateType, rowData?: Api.System.VersionRespVO) {
  renderModalBtn(
    OperateVersion,
    {
      operateType,
      rowData
    },
    {
      title: operateType === 'add' ? '新增版本' : '编辑版本',
      style: {
        width: '60%'
      },
      contentClass: 'max-h-[80vh] overflow-auto',
      func: getData
    }
  );
}

// 删除版本
async function handleDelete(row: Api.System.VersionRespVO) {
  const { error } = await runAsyncFetchDeleteVersion(row.id as number);
  if (!error) {
    getData();
    window.$message?.success($t('common.deleteSuccess'));
  }
}

// 钉钉推送
function handlePrePublish(row: Api.System.VersionRespVO) {
  renderModalBtn(
    PrePublish,
    {
      rowData: row
    },
    {
      title: '钉钉推送',
      style: {
        width: '60%'
      },
      contentClass: 'h-[80vh] overflow-auto'
    }
  );
}

// 更新说明
function handleRemark(row: Api.System.VersionRespVO) {
  renderModal(
    RemarkForm,
    {
      rowData: row
    },
    {
      title: '更新说明',
      style: {
        width: '60%'
      },
      contentClass: 'h-[80vh] overflow-auto'
    }
  );
}

// 切换页数
function handlePageSizeChange(num: number) {
  searchParams.pageNo = 1;
  searchParams.pageSize = num;
  getData();
}

// 获取版本列表
async function getData() {
  try {
    loading.value = true;
    const { actualReleaseAt } = searchParams;
    const { error, data } = await fetchGetVersionPage({
      ...searchParams,
      actualReleaseAt: actualReleaseAt
        ? [`${actualReleaseAt[0]} 00:00:00`, `${actualReleaseAt[1]} 23:59:59`]
        : undefined
    });
    if (error) {
      lists.value = [];
      return;
    }
    lists.value = data?.list || [];
    total.value = data?.total || 0;
    pageCount.value = Math.ceil((data?.total || 0) / searchParams.pageSize);
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
}

onMounted(() => {
  getData();
  emitter.on('version-refresh', getData);
});
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-8px overflow-hidden lt-sm:overflow-auto">
    <ProCard :show-collapse="false" content-class="!pb-0">
      <ProSearchForm :form="searchForm" :columns="searchColumns" />
    </ProCard>
    <NCard title="版本管理" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <NSpace>
          <NButton size="small" ghost type="primary" @click="handleOperateVersion('add')">
            <template #icon>
              <icon-ic-round-plus class="text-icon" />
            </template>
            新增版本
          </NButton>
          <NButton type="primary" size="small" ghost @click="handleModuleConfig">模块配置</NButton>
          <NButton size="small" ghost type="primary" @click="getData">
            <template #icon>
              <icon-mdi-refresh class="text-icon" :class="{ 'animate-spin': loading }" />
            </template>
            {{ $t('common.refresh') }}
          </NButton>
        </NSpace>
      </template>
      <div class="flex flex-col gap-16px">
        <!-- 版本 -->
        <NScrollbar class="mx-auto h-full max-h-500px max-w-5xl w-full px-2">
          <div v-if="lists.length > 0" class="flex flex-col gap-20px">
            <div
              v-for="item in lists"
              :key="item.id"
              class="flex flex-col gap-4px border border-[#f0f0f0] rounded-2xl bg-white p-16px shadow-sm transition-shadow hover:shadow-md"
            >
              <!-- 顶部信息 + 按钮组 -->
              <div class="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
                <!-- 版本信息 -->
                <div class="flex flex-col gap-4px">
                  <div class="flex flex-wrap items-center gap-8px text-15px">
                    <span class="font-semibold">版本号：</span>
                    <span class="text-[#333]">{{ item.versionNo }}</span>
                    <NTag :type="getEnumTagType(PublishStatusOptions, item.status)" size="small">
                      {{ getEnumValue(PublishStatusOptions, item.status) }}
                    </NTag>
                  </div>
                  <div class="whitespace-nowrap text-[#999]">发布时间：{{ item.actualReleaseAt || '--' }}</div>
                </div>
                <!-- 按钮组 -->
                <div class="flex flex-wrap gap-8px sm:flex-nowrap sm:items-center sm:justify-end">
                  <NButton size="small" type="info" ghost @click="handleOperateVersion('edit', item)">编辑版本</NButton>
                  <NButton size="small" type="info" ghost @click="handleVersionModule(item)">模块版本</NButton>
                  <NButton size="small" type="info" ghost @click="handleRemark(item)">更新说明</NButton>
                  <NButton size="small" type="warning" ghost @click="handlePrePublish(item)">钉钉推送</NButton>
                  <NPopconfirm @positive-click="handleDelete(item)">
                    <template #trigger>
                      <NButton size="small" type="error" ghost>删除</NButton>
                    </template>
                    确定删除该版本？
                  </NPopconfirm>
                </div>
              </div>

              <div class="my-4 border-t border-[#e0e0e0] border-dashed"></div>

              <!-- 模块配置 -->
              <div>
                <div class="mb-4px text-15px font-semibold">模块配置</div>
                <ul class="list-disc pl-4 text-14px text-[#666] leading-6">
                  <li v-for="module in item.moduleVersionList" :key="module.id">
                    <span class="font-semibold">{{ module.module }}：</span>
                    <span>{{ module.moduleVersionNo }}</span>
                  </li>
                </ul>
              </div>

              <!-- 更新说明 -->
              <div>
                <div class="mb-4px text-15px font-semibold">更新说明</div>
                <div class="text-14px text-[#666] leading-6">
                  {{ item.detailList?.[0]?.content }}
                </div>
              </div>
            </div>
          </div>
          <div v-else class="h-full flex-center">
            <NEmpty description="暂无数据" />
          </div>
        </NScrollbar>

        <NPagination
          v-if="lists.length > 0"
          v-model:page="searchParams.pageNo"
          :page-count="pageCount"
          :page-sizes="[10, 20, 30, 40, 50]"
          class="mt-10px flex justify-end"
          show-size-picker
          @update:page="getData"
          @update:page-size="handlePageSizeChange"
        >
          <template #prefix>共 {{ total }} 条</template>
        </NPagination>
      </div>
    </NCard>
  </div>
</template>
