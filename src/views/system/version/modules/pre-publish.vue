<script setup lang="ts">
import dayjs from 'dayjs';

import { NotificationType, NotificationTypeOptions } from '@/enum';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { useEmitt } from '@/hooks/common/useEmitt';
import { fetchGetVersionPushUsers, fetchPushVersionMessage } from '@/service/api/system/version';
import { getUserListByDeptOptions } from '@/utils/async-functions';
import { renderLabel, renderSingleSelectTag } from '@/utils/select-tag';

const props = defineProps<{
  rowData: Api.System.VersionRespVO;
}>();
const checkBox = ref<boolean>(false);
const { emitter } = useEmitt();
const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();
const rules = ref<Record<keyof Api.System.VersionDingTalkMessageReqVO & { type: number }, App.Global.FormRule>>({
  content: defaultRequiredRule,
  title: defaultRequiredRule,
  type: defaultRequiredRule
});
const userList = ref<Api.System.VersionPushUserRespVO>({
  pushUserList: [],
  pushUsers: []
});
const model = ref<Api.System.VersionDingTalkMessageReqVO & { type: number | undefined; otherPushUsers: number[] }>({
  content: '',
  id: undefined,
  otherPushUsers: [],
  pushUsers: [],
  title: '',
  type: undefined
});

async function handleSubmit() {
  await validate();
  model.value.pushUsers = [
    ...(checkBox.value ? [] : userList.value.pushUsers || []),
    ...(model.value.otherPushUsers || [])
  ];
  const { error } = await fetchPushVersionMessage(model.value);
  if (error) {
    return false;
  }
  window?.$message?.success('推送成功');
  emitter.emit('version-refresh');
}

function handleTypeChange(value: number) {
  let content = '';
  if (value === NotificationType.PreUpdate) {
    model.value.title = '【预更新通知】今晚 18:30 功能上新';
    content = `
亲爱的同事们：

${props.rowData.plannedDesc || '--'}

---

温馨提醒：

- 📌 请提前保存未提交的数据
- 📌 升级期间系统将短暂维护（约 10 分钟）
- 📌 如有紧急事务需处理，请及时与智能开发部的同事联系
  `;
  } else if (value === NotificationType.Countdown) {
    model.value.title = '【更新倒计时通知】系统升级倒计时 10 分钟';
    content = `
各位伙伴：

系统升级即将于 18:30 准时启动，预计持续 10 分钟。

为确保数据安全，请您：

- ⚠️ **立即保存当前操作文档**
- ⚠️ **暂停使用系统直至升级完成**
- ⚠️ **关闭所有相关后台程序**

---

升级期间系统将暂停服务，完成后将通过此机器人推送通知。感谢您的理解与配合！
  `;
  } else if (value === NotificationType.Completed) {
    model.value.title = '【更新完成通知】新功能已上线';
    content = `
尊敬的用户：

${props.rowData.actualDesc}

---
**当前版本：** v${props.rowData.versionNo}

**发布时间：** ${props.rowData.actualReleaseAt ? dayjs(props.rowData.actualReleaseAt).format('YYYY年MM月DD日 HH:mm') : ''}

使用过程中如有任何疑问，请及时与智能开发部的同事沟通。
  `;
  }

  model.value.content = content;
}

async function getPushUsers() {
  const { error, data } = await fetchGetVersionPushUsers(props.rowData.id as number);
  if (error) return;
  userList.value = data;
}
onMounted(() => {
  restoreValidation();
  getPushUsers();
  model.value.id = props.rowData.id;
});
defineExpose({
  handleSubmit
});
</script>

<template>
  <div>
    <NForm ref="formRef" :model="model" :rules="rules">
      <NFormItem label="通知类型" path="type">
        <NRadioGroup v-model:value="model.type" @update:value="handleTypeChange">
          <NRadioButton
            v-for="item in NotificationTypeOptions"
            :key="item.value"
            :value="item.value"
            :label="item.label"
          />
        </NRadioGroup>
      </NFormItem>
      <NFormItem label="标题" path="title">
        <NInput v-model:value="model.title" clearable />
      </NFormItem>
      <NFormItem label="内容" path="content">
        <Markdown v-model="model.content" class="!h-[300px]" />
      </NFormItem>
      <NFormItem label="推送角色内全部人员">
        <div
          v-if="Array.isArray(userList.pushUserList) && userList.pushUserList.length > 0"
          class="flex flex-col gap-8px"
        >
          <div class="flex flex-wrap gap-8px">
            <NTag v-for="item in userList.pushUserList" :key="item.id" :bordered="false" type="info">
              {{ item.nickname }}
            </NTag>
          </div>
          <NCheckbox v-model:checked="checkBox">不选择以上人员</NCheckbox>
        </div>

        <NAlert v-else type="warning">无推送人员</NAlert>
      </NFormItem>
      <NFormItem label="额外抄送人员" path="otherPushUsers">
        <SelectWithSearch
          v-model:value="model.otherPushUsers"
          :api-func="getUserListByDeptOptions"
          :selected-options="rowData.ccUserList || []"
          multiple
          clearable
          value-field="id"
          label-field="nickname"
          placeholder="额外抄送人员"
          :render-label="renderLabel"
          :render-tag="renderSingleSelectTag"
        />
      </NFormItem>
    </NForm>
  </div>
</template>
