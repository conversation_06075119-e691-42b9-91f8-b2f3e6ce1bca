<script setup lang="ts">
import { NotificationType, NotificationTypeOptions } from '@/enum';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { useEmitt } from '@/hooks/common/useEmitt';
import { fetchGetVersionPushUsers, fetchPushVersionMessage } from '@/service/api/system/version';
import { getUserListByDeptOptions } from '@/utils/async-functions';
import { renderLabel, renderSingleSelectTag } from '@/utils/select-tag';

const props = defineProps<{
  rowData: Api.System.VersionRespVO;
}>();
const { emitter } = useEmitt();
const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();
const rules = ref<Record<keyof Api.System.VersionDingTalkMessageReqVO & { type: number }, App.Global.FormRule>>({
  content: defaultRequiredRule,
  title: defaultRequiredRule,
  type: defaultRequiredRule
});
const userList = ref<Api.System.VersionPushUserRespVO>({
  pushUserList: [],
  pushUsers: []
});
const model = ref<Api.System.VersionDingTalkMessageReqVO & { type: number | undefined }>({
  content: '',
  id: undefined,
  pushUsers: [],
  title: '',
  type: undefined
});

async function handleSubmit() {
  await validate();
  const { error } = await fetchPushVersionMessage(model.value);
  if (error) {
    return false;
  }
  window?.$message?.success('推送成功');
  emitter.emit('version-refresh');
  return true;
}

function handleTypeChange(value: number) {
  let content = '';
  if (value === NotificationType.PreUpdate) {
    model.value.title = '【系统升级预告】今晚XX:XX功能上新';
    content = `<p>亲爱的同事们：</p>${props.rowData.plannedDesc || '--'}<p>本次更新将带来以下优化：</p>${props.rowData.actualDesc}<hr /><p>温馨提醒：</p><p>📌 请提前保存未提交的数据</p><p>📌 升级期间系统将短暂维护（约XX分钟）</p><p>📌 如有紧急事务需处理，请及时与我们联系</p>`;
  } else if (value === NotificationType.Countdown) {
    model.value.title = '【重要提醒】系统升级倒计时XX分钟';
    content = `<p>各位伙伴：</p><p>系统升级即将于XX:XX准时启动,预计持续XX分钟。</p><p>为确保数据安全，请您：</p><p>⚠️ 立即保存当前操作文档</p><p>⚠️ 暂停使用系统直至升级完成</p><p>⚠️ 关闭所有相关后台程序</p><hr /><p>升级期间系统将暂停服务，完成后将通过此机器人推送通知。</p><p>感谢您的理解与配合！</p>`;
  } else if (value === NotificationType.Completed) {
    model.value.title = '【系统升级完成】新功能已上线';
    content = `<p>尊敬的用户：</p>${props.rowData.plannedDesc}<p>本次更新重点优化：${props.rowData.actualDesc}<hr /><p>使用过程中如有任何疑问，请及时与研发部门沟通。</p>`;
  }
  model.value.content = content;
}

async function getPushUsers() {
  const { error, data } = await fetchGetVersionPushUsers(props.rowData.id as number);
  if (error) return;
  userList.value = data;
}
onMounted(() => {
  restoreValidation();
  getPushUsers();
  model.value.id = props.rowData.id;
  model.value.pushUsers = props.rowData.ccUsers || [];
});
defineExpose({
  handleSubmit
});
</script>

<template>
  <div>
    <NForm ref="formRef" :model="model" :rules="rules">
      <NFormItem label="通知类型" path="type">
        <NRadioGroup v-model:value="model.type" @update:value="handleTypeChange">
          <NRadioButton
            v-for="item in NotificationTypeOptions"
            :key="item.value"
            :value="item.value"
            :label="item.label"
          />
        </NRadioGroup>
      </NFormItem>
      <NFormItem label="标题" path="title">
        <NInput v-model:value="model.title" />
      </NFormItem>
      <NFormItem label="内容" path="content">
        <WangeEditor
          v-model="model.content"
          upload-type="COMPANY"
          :height="200"
          :toolbar-config="{ excludeKeys: ['group-video', 'group-image'] }"
        />
      </NFormItem>
      <NFormItem label="推送人员">
        <div class="flex flex-wrap gap-8px">
          <NTag v-for="item in userList.pushUserList" :key="item.id" :bordered="false" type="info">
            {{ item.nickname }}
          </NTag>
        </div>
      </NFormItem>
      <NFormItem label="额外抄送人员" path="pushUsers">
        <SelectWithSearch
          v-model:value="model.pushUsers"
          :api-func="getUserListByDeptOptions"
          :selected-options="rowData.ccUserList || []"
          multiple
          clearable
          value-field="id"
          label-field="nickname"
          placeholder="额外抄送人员"
          :render-label="renderLabel"
          :render-tag="renderSingleSelectTag"
        />
      </NFormItem>
    </NForm>
  </div>
</template>
