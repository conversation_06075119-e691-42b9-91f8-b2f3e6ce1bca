<script setup lang="tsx">
import { type DataTableColumns, NButton, NFormItem, NInput, NPopconfirm, NSelect } from 'naive-ui';

import { useNaiveForm } from '@/hooks/common/form';
import { useEmitt } from '@/hooks/common/useEmitt';
import { $t } from '@/locales';
import { fetchGetModuleList, fetchGetModuleVersionList, fetchUpdateModuleVersion } from '@/service/api/system/version';
const { emitter } = useEmitt();
const props = defineProps<{
  rowData: Api.System.VersionRespVO;
}>();
const { formRef, validate, restoreValidation } = useNaiveForm();
const moduleList = ref<{ label: string; value: number }[]>([]);
const form = ref<{ formList: Api.System.ModuleVersionSearchParams[] }>({
  formList: []
});

function validateModuleId(_rule: any, value: number) {
  // 获取当前所有 moduleId
  const ids = form.value.formList.map(item => item.moduleId);
  // 判断重复
  const hasDuplicate = ids.filter(id => id === value).length > 1;
  if (hasDuplicate) {
    return new Error('模块已存在，不能重复选择');
  }
  return true;
}

const columns = ref<DataTableColumns<Api.System.ModuleVersionSearchParams>>([
  {
    key: 'sort',
    title: '序号',
    align: 'center',
    render: (rowData: Api.System.ModuleVersionSearchParams, rowIndex: number) => {
      return (
        <NFormItem
          label-width={0}
          path={`formList[${rowIndex}].sort`}
          rule={[{ required: true, message: `序号是必填项` }]}
        >
          <NInput v-model:value={rowData.sort} />
        </NFormItem>
      );
    }
  },
  {
    key: 'moduleId',
    title: '模块',
    align: 'center',
    minWidth: 150,
    render: (rowData: Api.System.ModuleVersionSearchParams, rowIndex: number) => {
      return (
        <NFormItem
          label-width={0}
          path={`formList[${rowIndex}].moduleId`}
          rule={[{ required: true, message: `模块是必填项` }, { validator: validateModuleId }]}
        >
          <NSelect v-model:value={rowData.moduleId} options={moduleList.value} clearable />
        </NFormItem>
      );
    }
  },
  {
    key: 'moduleVersionNo',
    title: '模块版本号',
    align: 'center',
    render: (rowData: Api.System.ModuleVersionSearchParams, rowIndex: number) => {
      return (
        <NFormItem
          label-width={0}
          path={`formList[${rowIndex}].moduleVersionNo`}
          rule={[{ required: true, message: `模块版本号是必填项` }]}
        >
          <NInput v-model:value={rowData.moduleVersionNo} v-slots={{ prefix: () => <span>v</span> }} clearable />
        </NFormItem>
      );
    }
  },

  {
    key: 'action',
    title: '操作',
    align: 'center',
    render: (_rowData: Api.System.ModuleVersionSearchParams, rowIndex: number) => {
      return (
        <NPopconfirm onPositiveClick={() => handleDelete(rowIndex)}>
          {{
            default: () => $t('common.confirmDelete'),
            trigger: () => (
              <NButton type="error" ghost size="small">
                {$t('common.delete')}
              </NButton>
            )
          }}
        </NPopconfirm>
      );
    }
  }
]);

// 重新计算所有序号
function recalculateSort() {
  form.value.formList.forEach((item, index) => {
    item.sort = index + 1;
  });
}

// 获取下一个序号
function getNextSort(): number {
  if (form.value.formList.length === 0) {
    return 1;
  }
  const maxSort = Math.max(...form.value.formList.map(item => item.sort || 0));
  return maxSort + 1;
}

function handleDelete(rowIndex: number) {
  form.value.formList.splice(rowIndex, 1);
  // 删除后重新计算序号
  recalculateSort();
}

async function getModuleList() {
  const { error, data } = await fetchGetModuleList({ module: undefined });
  if (error) {
    return;
  }
  moduleList.value = data.map(item => ({
    label: `${item.module} ${item.lastModuleVersionNo ? `(v${item.lastModuleVersionNo})` : ''}`,
    value: item.id as number
  }));
}

function addRow() {
  form.value.formList.push({
    moduleId: undefined,
    moduleVersionNo: undefined,
    sort: getNextSort() // 自动设置序号
  });
}

async function getModuleVersionList() {
  const { error, data } = await fetchGetModuleVersionList(props.rowData.id as number);
  if (error) {
    return;
  }
  form.value.formList = data || [];
}

onMounted(() => {
  restoreValidation();
  getModuleList();
  getModuleVersionList();
});
async function handleSubmit() {
  if (form.value.formList.length === 0) {
    window?.$message?.error('请至少添加一个模块');
    return false;
  }
  await validate();
  const { error } = await fetchUpdateModuleVersion(props.rowData.id as number, form.value.formList);
  if (error) {
    return false;
  }
  window?.$message?.success('更新成功');
  emitter.emit('version-refresh');
  return true;
}
defineExpose({
  handleSubmit
});
</script>

<template>
  <NForm ref="formRef" :model="form">
    <NDataTable :columns="columns" :data="form.formList" size="small" :row-key="row => row.id" />
    <div class="mt-5">
      <NButton class="w-1/1" @click="addRow">新增一行</NButton>
    </div>
  </NForm>
</template>
