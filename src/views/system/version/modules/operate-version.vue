<script setup lang="ts">
import { PublishStatusOptions } from '@/enum';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { fetchCreateVersion, fetchGetVersionDetail, fetchUpdateVersion } from '@/service/api/system/version';
import { getUserListByDeptOptions } from '@/utils/async-functions';
import { renderLabel, renderSingleSelectTag } from '@/utils/select-tag';

const props = defineProps<{
  operateType: NaiveUI.TableOperateType;
  rowData?: Api.System.VersionRespVO;
}>();
type RuleKey = keyof Pick<Api.System.VersionRespVO, 'versionNo' | 'status'>;
const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();
const model = ref<Api.System.VersionRespVO>({
  actualDesc: '',
  actualReleaseAt: '',
  ccUsers: [],
  id: undefined,
  plannedDesc: '',
  plannedReleaseAt: '',
  requirementDoc: '',
  status: 1,
  versionNo: ''
});
const rules = ref<Record<RuleKey, App.Global.FormRule>>({
  versionNo: defaultRequiredRule,
  status: defaultRequiredRule
});

async function getDetail() {
  const { error, data } = await fetchGetVersionDetail(props.rowData!.id as number);
  if (error) return;
  Object.assign(model.value, data);
}

async function handleSubmit() {
  await validate();
  if (props.operateType === 'add') {
    const { error } = await fetchCreateVersion(model.value);
    if (error) return false;
  } else {
    const { error } = await fetchUpdateVersion(props.rowData!.id as number, model.value);
    if (error) return false;
  }
  window.$message?.success('新增成功');
  return true;
}
onMounted(() => {
  restoreValidation();
  if (props.operateType === 'edit') {
    getDetail();
  }
});
defineExpose({
  handleSubmit
});
</script>

<template>
  <div>
    <NForm ref="formRef" :model="model" :rules="rules">
      <NGrid responsive="screen" item-responsive>
        <NFormItemGi span="24 s:12 m:12" label="版本号" path="versionNo" class="pr-24px">
          <NInput v-model:value="model.versionNo" clearable />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:12" label="发布状态" path="status" class="pr-24px">
          <NSelect v-model:value="model.status" :options="PublishStatusOptions" />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:12" label="预计发布日期" path="plannedReleaseAt" class="pr-24px">
          <DateTimePicker v-model:time="model.plannedReleaseAt" class="w-full" label="实际发布日期" type="datetime" />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:12" label="实际发布日期" path="actualReleaseAt" class="pr-24px">
          <DateTimePicker v-model:time="model.actualReleaseAt" class="w-full" label="实际发布日期" type="datetime" />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:12" label="预计发布概述" path="plannedDesc" class="pr-24px">
          <WangeEditor
            v-model="model.plannedDesc"
            upload-type="COMPANY"
            :height="130"
            :toolbar-config="{ excludeKeys: ['group-video', 'group-image'] }"
          />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:12" label="实际发布概述" path="actualDesc" class="pr-24px">
          <WangeEditor
            v-model="model.actualDesc"
            upload-type="COMPANY"
            :height="130"
            :toolbar-config="{ excludeKeys: ['group-video', 'group-image'] }"
          />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:12" label="关联需求文档" path="requirementDoc" class="pr-24px">
          <NInput v-model:value="model.requirementDoc" clearable placeholder="请输入关联需求文档" />
        </NFormItemGi>
        <NFormItemGi span="24 s:12 m:12" label="额外抄送人员" path="ccUsers" class="pr-24px">
          <SelectWithSearch
            v-model:value="model.ccUsers"
            :api-func="getUserListByDeptOptions"
            :selected-options="model.ccUserList || []"
            multiple
            clearable
            value-field="id"
            label-field="nickname"
            placeholder="额外抄送人员"
            :render-label="renderLabel"
            :render-tag="renderSingleSelectTag"
          />
        </NFormItemGi>
      </NGrid>
    </NForm>
  </div>
</template>
