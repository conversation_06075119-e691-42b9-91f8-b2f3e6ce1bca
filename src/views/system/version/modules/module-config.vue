<script setup lang="tsx">
import { N<PERSON><PERSON>on, NPopconfirm } from 'naive-ui';
import type { ProDataTableColumns } from 'pro-naive-ui';
import { createProModalForm, useNDataTable, useRequest } from 'pro-naive-ui';

import ModelForm from './model-form.vue';

import { $t } from '@/locales';
import {
  fetchCreateModule,
  fetchDeleteModule,
  fetchGetModuleDetail,
  fetchGetModuleList,
  fetchUpdateModule
} from '@/service/api/system/version';
// 新增
const { loading: fetchPostModuleLoading, runAsync: runAsyncFetchPostModule } = useRequest(fetchCreateModule, {
  manual: true
});
// 编辑
const { loading: fetchPutModuleLoading, runAsync: runAsyncFetchPutModule } = useRequest(fetchUpdateModule, {
  manual: true
});
// 获取单个模版信息
const { runAsync: runAsyncFetchGetModuleDetail } = useRequest(fetchGetModuleDetail, {
  manual: true
});
// 删除
const { runAsync: runAsyncFetchDeleteModule } = useRequest(fetchDeleteModule, {
  manual: true
});
const columns: ProDataTableColumns<Api.System.ModuleRespVO> = [
  {
    path: 'module',
    title: '模块名称',
    align: 'center'
  },
  {
    path: 'description',
    title: '说明',
    align: 'center'
  },
  {
    path: 'roleNameList',
    title: '推送角色',
    align: 'center',
    render: (row: Api.System.ModuleRespVO) => {
      return <div>{row?.roleNameList?.join(',')}</div>;
    }
  },
  {
    path: 'action',
    title: '操作',
    align: 'center',
    render: (row: Api.System.ModuleRespVO) => {
      return (
        <div class="flex-center gap-8px">
          <NButton size="small" type="primary" ghost onClick={() => handleOpenModal('edit', row)}>
            编辑
          </NButton>
          <NPopconfirm onPositiveClick={() => handleDelete(row.id as number)}>
            {{
              default: () => $t('common.confirmDelete'),
              trigger: () => (
                <NButton type="error" ghost size="small">
                  {$t('common.delete')}
                </NButton>
              )
            }}
          </NPopconfirm>
        </div>
      );
    }
  }
];

const {
  table: { tableProps, onChange }
} = useNDataTable(async values => {
  return fetchGetModuleList({
    module: values.module
  }).then(({ data }) => {
    return {
      list: data || [],
      total: data?.length || 0
    };
  });
});
/* 新增 编辑 */
const modalForm = createProModalForm<Api.System.ModuleRespVO>({
  onSubmit: async values => {
    try {
      if (modalForm.values.value.id) {
        const { error } = await runAsyncFetchPutModule(modalForm.values.value.id, values);
        if (!error) {
          onChange();
          window.$message?.success($t('common.updateSuccess'));
          modalForm.close();
        }
      } else {
        const { error } = await runAsyncFetchPostModule(values);
        if (!error) {
          onChange({ page: 1 });
          window.$message?.success($t('common.addSuccess'));
          modalForm.close();
        }
      }
    } catch (e) {
      console.log(e);
    }
  }
});

async function handleDelete(id: number) {
  const { error } = await runAsyncFetchDeleteModule(id);
  if (!error) {
    onChange();
    window.$message?.success($t('common.deleteSuccess'));
  }
}

async function handleOpenModal(type: 'add' | 'edit', row?: Api.System.ModuleRespVO) {
  if (type === 'edit') {
    const { data } = await runAsyncFetchGetModuleDetail(row?.id as number);
    modalForm.values.value = data as Api.System.ModuleRespVO;
  } else {
    modalForm.values.value = {
      module: '',
      description: '',
      pushRoles: []
    };
  }
  modalForm.open();
}
</script>

<template>
  <div class="h-full flex flex-col">
    <ProDataTable
      title="模块配置"
      size="small"
      flex-height
      :columns="columns"
      row-key="id"
      v-bind="tableProps"
      :pagination="false"
    >
      <template #toolbar>
        <NButton size="small" ghost type="primary" @click="handleOpenModal('add')">
          <template #icon>
            <icon-ic-round-plus class="text-icon" />
          </template>
          {{ $t('common.add') }}
        </NButton>
      </template>
    </ProDataTable>
    <!-- 新增 编辑 -->
    <ProModalForm
      :form="modalForm"
      :title="modalForm.values.value.id ? '编辑模块' : '新增模块'"
      :loading="modalForm.values.value.id ? fetchPutModuleLoading : fetchPostModuleLoading"
      preset="card"
      label-width="80"
      width="50%"
    >
      <ModelForm />
    </ProModalForm>
  </div>
</template>
