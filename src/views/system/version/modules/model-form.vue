<script setup lang="tsx">
import { fetchGetRoleList } from '@/service/api/system/role';
const roleList = ref<CommonType.Option<number>[]>([]);

async function getNSelectProps() {
  const { data } = await fetchGetRoleList({ pageSize: -1 });
  roleList.value =
    data?.list.map(item => ({
      label: item.name,
      value: item.id
    })) || [];
}
onMounted(() => {
  getNSelectProps();
});
</script>

<template>
  <ProInput
    title="模块名称"
    path="module"
    required
    :field-props="{
      clearable: true
    }"
  />
  <ProSelect
    title="推送角色"
    path="pushRoles"
    placeholder="推送角色"
    :field-props="{
      clearable: true,
      options: roleList,
      multiple: true
    }"
  />
  <ProTextarea
    title="说明"
    path="description"
    rows="3"
    clearable
    :field-props="{
      clearable: true
    }"
  />
</template>

<style scoped></style>
