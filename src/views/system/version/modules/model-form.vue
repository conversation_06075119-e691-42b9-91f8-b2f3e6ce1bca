<script setup lang="tsx">
import { fetchGetRoleList } from '@/service/api/system/role';
const roleList = ref<CommonType.Option<number>[]>([]);

async function getNSelectProps() {
  const { data } = await fetchGetRoleList({ pageSize: -1 });
  roleList.value =
    data?.list.map(item => ({
      label: item.name,
      value: item.id
    })) || [];
}
onMounted(() => {
  getNSelectProps();
});
</script>

<template>
  <ProInput title="模块名称" path="module" required />
  <ProSelect
    title="推送角色"
    path="pushRoles"
    placeholder="推送角色"
    required
    :field-props="{
      options: roleList,
      multiple: true
    }"
  />
  <ProTextarea title="说明" path="description" required rows="3" />
</template>

<style scoped></style>
