<script setup lang="ts">
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { fetchPushVersionMessage } from '@/service/api/system/version';
import { getUserListByDeptOptions } from '@/utils/async-functions';
import { renderLabel, renderSingleSelectTag } from '@/utils/select-tag';

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();
const rules = ref<Record<keyof Omit<Api.System.VersionDingTalkMessageReqVO, 'id'>, App.Global.FormRule>>({
  content: defaultRequiredRule,
  title: defaultRequiredRule,
  pushUsers: defaultRequiredRule
});

const model = ref<Api.System.VersionDingTalkMessageReqVO>({
  content: '',
  pushUsers: [],
  title: ''
});

async function handleSubmit() {
  await validate();
  const { error } = await fetchPushVersionMessage(model.value);
  if (error) {
    return false;
  }
  window?.$message?.success('推送成功');
}
onMounted(() => {
  restoreValidation();
});
defineExpose({
  handleSubmit
});
</script>

<template>
  <div>
    <NForm ref="formRef" :model="model" :rules="rules">
      <NFormItem label="标题" path="title">
        <NInput v-model:value="model.title" clearable />
      </NFormItem>
      <NFormItem label="内容" path="content">
        <Markdown v-model="model.content" class="!h-[300px]" />
      </NFormItem>
      <NFormItem label="抄送人员" path="pushUsers">
        <SelectWithSearch
          v-model:value="model.pushUsers"
          :api-func="getUserListByDeptOptions"
          :selected-options="[]"
          multiple
          clearable
          value-field="id"
          label-field="nickname"
          placeholder="抄送人员"
          :render-label="renderLabel"
          :render-tag="renderSingleSelectTag"
        />
      </NFormItem>
    </NForm>
  </div>
</template>
