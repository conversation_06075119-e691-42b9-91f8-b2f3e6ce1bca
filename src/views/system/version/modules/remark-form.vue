<script setup lang="tsx">
import { type DataTableColumns, NButton, NFormItem, NInput, NPopconfirm, NSelect } from 'naive-ui';

import { useNaiveForm } from '@/hooks/common/form';
import { useEmitt } from '@/hooks/common/useEmitt';
import { $t } from '@/locales';
import { fetchGetModuleList, fetchGetModuleVersionList, fetchUpdateModuleVersion } from '@/service/api/system/version';

const props = defineProps<{
  rowData: Api.System.VersionRespVO;
}>();
const { emitter } = useEmitt();
const { formRef, validate, restoreValidation } = useNaiveForm();
const moduleList = ref<{ label: string; value: number }[]>([]);
const form = ref<{ formList: Api.System.ModuleVersionSearchParams[] }>({
  formList: []
});
const columns = ref<DataTableColumns<Api.System.ModuleVersionSearchParams>>([
  {
    key: 'sort',
    title: '序号',
    align: 'center',
    render: (rowData: Api.System.ModuleVersionSearchParams, rowIndex: number) => {
      return (
        <NFormItem
          label-width={0}
          path={`formList[${rowIndex}].sort`}
          rule={[{ required: true, message: `序号是必填项` }]}
        >
          <NInput v-model:value={rowData.sort} />
        </NFormItem>
      );
    }
  },
  {
    key: 'moduleId',
    title: '模块',
    align: 'center',
    minWidth: 150,
    render: (rowData: Api.System.ModuleVersionSearchParams, rowIndex: number) => {
      return (
        <NFormItem
          label-width={0}
          path={`formList[${rowIndex}].moduleId`}
          rule={[{ required: true, message: `模块是必填项` }]}
        >
          <NSelect v-model:value={rowData.moduleId} options={moduleList.value} />
        </NFormItem>
      );
    }
  },
  {
    key: 'moduleVersionNo',
    title: '模块版本号',
    align: 'center',
    render: (rowData: Api.System.ModuleVersionSearchParams, rowIndex: number) => {
      return (
        <NFormItem
          label-width={0}
          path={`formList[${rowIndex}].moduleVersionNo`}
          rule={[{ required: true, message: `模块版本号是必填项` }]}
        >
          <NInput v-model:value={rowData.moduleVersionNo} />
        </NFormItem>
      );
    }
  },

  {
    key: 'action',
    title: '操作',
    align: 'center',
    render: (_rowData: Api.System.ModuleVersionSearchParams, rowIndex: number) => {
      return (
        <NPopconfirm onPositiveClick={() => handleDelete(rowIndex)}>
          {{
            default: () => $t('common.confirmDelete'),
            trigger: () => (
              <NButton type="error" ghost size="small">
                {$t('common.delete')}
              </NButton>
            )
          }}
        </NPopconfirm>
      );
    }
  }
]);

function handleDelete(rowIndex: number) {
  form.value.formList.splice(rowIndex, 1);
}

async function getModuleList() {
  const { error, data } = await fetchGetModuleList({ pageNo: 1, pageSize: 10 });
  if (error) {
    return;
  }
  moduleList.value = data.list.map(item => ({
    label: item.module,
    value: item.id as number
  }));
}

function addRow() {
  form.value.formList.push({
    moduleId: undefined,
    moduleVersionNo: undefined,
    sort: undefined
  });
}

async function getModuleVersionList() {
  const { error, data } = await fetchGetModuleVersionList({
    moduleId: props.rowData.id as number,
    pageSize: 1
  });
  if (error) {
    return;
  }
  form.value.formList = data.list || [];
}

onMounted(() => {
  restoreValidation();
  getModuleList();
  getModuleVersionList();
});
async function handleSubmit() {
  if (form.value.formList.length === 0) {
    window?.$message?.error('请至少添加一个模块');
    return false;
  }
  await validate();
  const { error } = await fetchUpdateModuleVersion(props.rowData.id as number, form.value.formList);
  if (error) {
    return false;
  }
  window?.$message?.success('更新成功');
  emitter.emit('version-refresh');
  return true;
}
defineExpose({
  handleSubmit
});
</script>

<template>
  <NForm ref="formRef" :model="form">
    <NDataTable :columns="columns" :data="form.formList" size="small" :row-key="row => row.id" />
    <div class="mt-5">
      <NButton class="w-1/1" @click="addRow">新增一行</NButton>
    </div>
  </NForm>
</template>
