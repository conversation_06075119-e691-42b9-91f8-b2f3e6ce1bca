<script setup lang="tsx">
import { type DataTableColumns, NButton, NFormItem, NInput, NPopconfirm, NSelect } from 'naive-ui';

import { useNaiveForm } from '@/hooks/common/form';
import { useEmitt } from '@/hooks/common/useEmitt';
import { $t } from '@/locales';
import { fetchGetVersionDetailInfo, fetchUpdateVersionDetail } from '@/service/api/system/version';

const props = defineProps<{
  rowData: Api.System.VersionRespVO;
}>();

const moduleVersionList = computed(() => {
  return props.rowData.moduleVersionList?.map(item => ({
    label: `${item.module} (v${item.moduleVersionNo})`,
    moduleVersionId: item.id as number,
    value: item.moduleId as number
  }));
});
const purposesOptions = ref([
  { label: '新增功能', value: '新增功能' },
  { label: '功能优化', value: '功能优化' },
  { label: '问题修复', value: '问题修复' }
]);
const { emitter } = useEmitt();
const { formRef, validate, restoreValidation } = useNaiveForm();
const form = ref<{ formList: Api.System.VersionDetailSearchParams[] }>({
  formList: []
});
const columns = ref<DataTableColumns<Api.System.VersionDetailReqVO>>([
  {
    key: 'sort',
    title: '序号',
    align: 'center',
    width: 120,
    render: (rowData: Api.System.VersionDetailSearchParams, rowIndex: number) => {
      return (
        <NFormItem
          label-width={0}
          path={`formList[${rowIndex}].sort`}
          rule={[{ required: true, message: `序号是必填项` }]}
        >
          <NInput v-model:value={rowData.sort} />
        </NFormItem>
      );
    }
  },
  {
    key: 'moduleId',
    title: '模块',
    align: 'center',
    width: 180,
    render: (rowData: Api.System.VersionDetailSearchParams, rowIndex: number) => {
      return (
        <NFormItem
          label-width={0}
          path={`formList[${rowIndex}].moduleId`}
          rule={[{ required: true, message: `模块是必填项` }]}
        >
          <NSelect
            clearable
            v-model:value={rowData.moduleId}
            options={moduleVersionList.value}
            onUpdate:value={value => {
              const param = moduleVersionList.value?.find(item => item.value === value);
              if (param) {
                rowData.moduleVersionId = param.moduleVersionId;
              }
            }}
          />
        </NFormItem>
      );
    }
  },
  {
    key: 'purposes',
    title: '用途',
    align: 'center',
    width: 180,
    render: (rowData: Api.System.VersionDetailSearchParams, rowIndex: number) => {
      return (
        <NFormItem
          label-width={0}
          path={`formList[${rowIndex}].purposes`}
          rule={[{ required: true, message: `用途是必填项`, trigger: 'change' }]}
        >
          <NSelect
            clearable
            v-model:value={rowData.purposes}
            tag
            filterable
            placeholder="输入，按回车确认"
            options={purposesOptions.value}
          />
        </NFormItem>
      );
    }
  },
  {
    key: 'content',
    title: '更新内容',
    render: (rowData: Api.System.VersionDetailSearchParams, rowIndex: number) => {
      return (
        <NFormItem
          label-width={0}
          path={`formList[${rowIndex}].content`}
          rule={[{ required: true, message: `模块版本号是必填项` }]}
        >
          <NInput v-model:value={rowData.content} type="textarea" rows={1} clearable />
        </NFormItem>
      );
    }
  },
  {
    key: 'menuId',
    title: '菜单ID',
    width: 140,
    render: (rowData: Api.System.VersionDetailSearchParams, rowIndex: number) => {
      return (
        <NFormItem label-width={0} path={`formList[${rowIndex}].menuId`}>
          <NInput v-model:value={rowData.menuId} clearable />
        </NFormItem>
      );
    }
  },

  {
    key: 'action',
    title: '操作',
    align: 'center',
    render: (_rowData: Api.System.VersionDetailSearchParams, rowIndex: number) => {
      return (
        <NPopconfirm onPositiveClick={() => handleDelete(rowIndex)}>
          {{
            default: () => $t('common.confirmDelete'),
            trigger: () => (
              <NButton type="error" ghost size="small">
                {$t('common.delete')}
              </NButton>
            )
          }}
        </NPopconfirm>
      );
    }
  }
]);

function handleDelete(rowIndex: number) {
  form.value.formList.splice(rowIndex, 1);
  // 删除后重新计算序号
  recalculateSort();
}

function addRow() {
  // 计算新的序号
  const newSort = form.value.formList.length > 0 ? Math.max(...form.value.formList.map(item => item.sort || 0)) + 1 : 1;

  form.value.formList.push({
    content: '',
    menuId: undefined,
    sort: newSort,
    moduleId: undefined,
    moduleVersionId: undefined,
    versionId: props.rowData.id as number
  });
}

// 重新计算序号的函数
function recalculateSort() {
  form.value.formList.forEach((item, index) => {
    item.sort = index + 1;
  });
}

async function getModuleVersionList() {
  const { error, data } = await fetchGetVersionDetailInfo(props.rowData.id as number);
  if (error) {
    return;
  }
  form.value.formList = data || [];
  // 初始化时重新计算序号
  recalculateSort();
}

async function handleSubmit() {
  if (form.value.formList.length === 0) {
    window?.$message?.error('请至少添加一个更新内容');
    return false;
  }
  await validate();
  const { error } = await fetchUpdateVersionDetail(props.rowData.id as number, form.value.formList);
  if (error) {
    return false;
  }
  window?.$message?.success('更新成功');
  emitter.emit('version-refresh');
  return true;
}
onMounted(() => {
  restoreValidation();
  getModuleVersionList();
});
defineExpose({
  handleSubmit
});
</script>

<template>
  <NForm ref="formRef" :model="form">
    <NDataTable :columns="columns" :data="form.formList" size="small" :row-key="row => row.id" />
    <div class="mt-5">
      <NButton class="w-1/1" @click="addRow">新增一行</NButton>
    </div>
  </NForm>
</template>
