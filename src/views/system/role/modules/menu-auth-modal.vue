<script setup lang="ts">
import { onMounted, ref, useTemplateRef } from 'vue';
import { useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { fetchGetTreeMenuList } from '@/service/api/system/menu';
import { fetchGetTreeMenuRoleSelect, fetchPostTreeMenuRoleList } from '@/service/api/system/role';

interface Props {
  rowData: Api.SystemManage.Role;
}

type Model = {
  roleId: number;
  name?: string;
  code?: string;
  menuIds: number[];
};

const props = defineProps<Props>();
const { formRef, restoreValidation } = useNaiveForm();
const treeRef = useTemplateRef<HTMLElement>('treeRef');
const model = ref(createDefaultModel());
const treeOptions = ref<Api.SystemManage.TreeMenuRespVOList>();
const isExpandAll = ref<boolean>(true);
const isSelectAll = ref<boolean>(false);
const selectIds = ref<number[]>([]); // 选中
const halfChooseIds = ref<number[]>([]); // 半选

function createDefaultModel(): Model {
  return {
    name: '',
    code: '',
    roleId: 0,
    menuIds: []
  };
}

async function handleInitModel() {
  model.value = createDefaultModel();
  await handleGetRoleMenu();
  Object.assign(model.value, props.rowData);
  model.value.roleId = props.rowData.id;
}

async function handleGetRoleMenu() {
  try {
    const { data, error } = await fetchGetTreeMenuRoleSelect({ roleId: props.rowData.id });
    if (error) {
      return;
    }
    selectIds.value = data?.selectMenuIds || [];
  } catch (e) {
    console.log(e);
  }
}

function handleSelectAllChange(val: boolean) {
  if (val) {
    selectIds.value = getAllMenuIds(treeOptions.value);
    return;
  }
  selectIds.value = [];
}
function getAllMenuIds(menus: any[]) {
  const menuIds: CommonType.IdType[] = [];
  menus.forEach(item => {
    if (item.menuId) {
      menuIds.push(item.menuId);
    }
    if (item.children && Array.isArray(item.children)) {
      menuIds.push(...getAllMenuIds(item.children));
    }
  });
  return menuIds;
}
function handleExpandAllChange(val: boolean) {
  isExpandAll.value = val;
}

async function handleFetchGetMenuList() {
  try {
    const { data, error } = await fetchGetTreeMenuList();
    if (error) {
      return;
    }
    treeOptions.value = data || [];
  } catch (e) {
    console.log(e);
  }
}

async function handleSubmit() {
  try {
    const { error } = await fetchPostTreeMenuRoleList({
      roleId: model.value.roleId,
      menuIds: [...treeRef.value?.getCheckedData().keys, ...treeRef.value?.getIndeterminateData().keys]
    });
    if (error) {
      return false;
    }
    window.$message?.success($t('common.updateSuccess'));
    return true;
  } catch (e) {
    console.log(e);
    return false;
  }
}
// 选中
function updateCheckedKeys(keys: Array<number>) {
  selectIds.value = keys;
}
// 半选
function updateIndeterminateKeys(keys: Array<number>) {
  halfChooseIds.value = keys;
}
defineExpose({
  handleSubmit
});
onMounted(async () => {
  await restoreValidation();
  await handleFetchGetMenuList();
  await handleInitModel();
});
</script>

<template>
  <NForm ref="formRef" :model="model" label-placement="left">
    <NFormItem span="24 m:12" :label="$t('page.manage.role.roleName')" path="name">
      <NTag type="primary" size="small">{{ model.name }}</NTag>
    </NFormItem>

    <NFormItem :label="$t('page.manage.role.roleCode')" path="code">
      <NTag type="primary" size="small">{{ model.code }}</NTag>
    </NFormItem>

    <NFormItem label="菜单权限" path="menuIds">
      <div class="w-full">
        <div class="w-full flex flex-wrap gap-4 border p-2">
          <span>
            全选/全不选:
            <NSwitch v-model:value="isSelectAll" @update:value="handleSelectAllChange" />
          </span>
          <span>
            全部展开/折叠:
            <NSwitch v-model:value="isExpandAll" @update:value="handleExpandAllChange" />
          </span>
        </div>

        <div class="max-h-550px overflow-auto">
          <NTree
            ref="treeRef"
            v-model:checked-keys="selectIds"
            checkable
            :data="treeOptions"
            value-field="menuId"
            key-field="menuId"
            label-field="menuName"
            cascade
            :default-expand-all="isExpandAll"
            @update:checked-keys="updateCheckedKeys"
            @update:indeterminate-keys="updateIndeterminateKeys"
          />
        </div>
      </div>
    </NFormItem>
  </NForm>
</template>

<style scoped></style>
