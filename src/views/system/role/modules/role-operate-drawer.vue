<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { enableStatusOptions } from '@/constants/business';
import { fetchPostRole, fetchPutRole } from '@/service/api/system/role';

defineOptions({
  name: 'RoleOperateDrawer'
});

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.SystemManage.Role | null;
}

const props = defineProps<Props>();

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

type Model = Pick<Api.SystemManage.Role, 'name' | 'code' | 'remark' | 'status' | 'sort'>;

const model = ref(createDefaultModel());

function createDefaultModel(): Model {
  return {
    code: '',
    name: '',
    remark: '',
    status: 0,
    sort: 0
  };
}

type RuleKey = Exclude<keyof Model, 'remark'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  name: defaultRequiredRule,
  code: defaultRequiredRule,
  sort: defaultRequiredRule,
  status: defaultRequiredRule
};

function handleInitModel() {
  model.value = createDefaultModel();

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model.value, props.rowData);
  }
}

async function handleSubmit() {
  await validate();
  if (props.operateType === 'edit') {
    const { error } = await fetchPutRole(model.value);
    if (error) {
      return false;
    }
    window.$message?.success($t('common.updateSuccess'));
  } else {
    const { error } = await fetchPostRole(model.value);
    if (error) {
      return false;
    }
    window.$message?.success($t('common.addSuccess'));
  }
  return true;
}
defineExpose({
  handleSubmit
});
onMounted(() => {
  handleInitModel();
  restoreValidation();
});
</script>

<template>
  <NForm ref="formRef" :model="model" :rules="rules">
    <NFormItem :label="$t('page.manage.role.roleName')" path="name">
      <NInput v-model:value="model.name" :placeholder="$t('page.manage.role.form.roleName')" />
    </NFormItem>
    <NFormItem :label="$t('page.manage.role.roleCode')" path="code">
      <NInput v-model:value="model.code" :placeholder="$t('page.manage.role.form.roleCode')" />
    </NFormItem>
    <NFormItem :label="$t('page.manage.role.roleStatus')" path="status">
      <NRadioGroup v-model:value="model.status">
        <NRadio
          v-for="item in enableStatusOptions"
          :key="item.value"
          :value="Number(item.value)"
          :label="$t(item.label)"
        />
      </NRadioGroup>
    </NFormItem>
    <NFormItem label="显示顺序" path="sort">
      <NInputNumber v-model:value="model.sort" placeholder="请输入显示顺序" class="w-full" />
    </NFormItem>
    <NFormItem :label="$t('page.manage.role.roleDesc')" path="remark">
      <NInput v-model:value="model.remark" :placeholder="$t('page.manage.role.form.roleDesc')" />
    </NFormItem>
  </NForm>
</template>

<style scoped></style>
