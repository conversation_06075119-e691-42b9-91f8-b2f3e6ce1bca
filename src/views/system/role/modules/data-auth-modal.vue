<script setup lang="ts">
import { onMounted, ref, useTemplateRef } from 'vue';

import { DataScopeType, DataScopeTypeOptions } from '@/enum';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { fetchGetDeptTreeList } from '@/service/api/system/dept';
import { fetchGetSystemOptions, fetchPostRoleAuth } from '@/service/api/system/role';

interface Props {
  rowData: Api.SystemManage.Role;
}

type Model = Pick<Api.SystemManage.Role, 'dataScope' | 'dataScopeDeptIds'> & {
  roleId: number;
  name?: string;
  code?: string;
  systemName: string;
};
type RuleKey = keyof Model['systemName'];
const props = defineProps<Props>();
const { formRef, restoreValidation, validate } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();
const treeRef = useTemplateRef<HTMLElement>('treeRef');
const model = ref(createDefaultModel());
const treeOptions = ref<Api.SystemManage.TreeDeptRespList>();
const isExpandAll = ref<boolean>(true);
const isSelectAll = ref<boolean>(false);
const isCascade = ref<boolean>(false);
const systemOptions = ref<string[]>();
const rules = ref<Record<RuleKey, App.Global.FormRule>>({
  systemName: defaultRequiredRule
});

function createDefaultModel(): Model {
  return {
    dataScope: 0,
    dataScopeDeptIds: [],
    roleId: 0,
    name: '',
    systemName: '',
    code: ''
  };
}

function handleInitModel() {
  model.value = createDefaultModel();
  Object.assign(model.value, props.rowData);
  model.value.dataScopeDeptIds = props.rowData.dataScopeDeptIds;

  if (!Array.isArray(model.value.dataScopeDeptIds)) {
    model.value.dataScopeDeptIds = [];
  }
  model.value.roleId = props.rowData.id;
}

function getAllKeys(nodes: Api.SystemManage.TreeDeptRespList): (string | number)[] {
  const keys: number[] = [];

  function traverse(items: Api.SystemManage.TreeDeptRespList) {
    for (const item of items) {
      keys.push(item.id);
      if (item.children?.length) {
        traverse(item.children);
      }
    }
  }

  traverse(nodes);
  return keys;
}

function handleSelectAllChange(val: boolean) {
  if (val) {
    model.value.dataScopeDeptIds = getAllDeptIds(treeOptions.value);
    return;
  }
  model.value.dataScopeDeptIds = [];
}

function getAllDeptIds(depts: any[]) {
  const deptIds: CommonType.IdType[] = [];
  depts.forEach(item => {
    if (item.id) {
      deptIds.push(item.id);
    }
    if (item.children && Array.isArray(item.children)) {
      deptIds.push(...getAllDeptIds(item.children));
    }
  });
  return deptIds;
}

function handleExpandAllChange(val: boolean) {
  isExpandAll.value = val;
}

async function handleFetchGetDeptList() {
  try {
    const { data, error } = await fetchGetDeptTreeList();
    if (error) {
      return;
    }
    treeOptions.value = data || [];
  } catch (e) {
    console.log(e);
  }
}

async function handleSubmit() {
  await validate();
  try {
    const { error } = await fetchPostRoleAuth({
      roleId: model.value.roleId,
      dataScope: model.value.dataScope,
      dataScopeDeptIds: model.value.dataScope === DataScopeType.Custom ? model.value.dataScopeDeptIds : [],
      systemName: model.value.systemName
    });
    if (error) {
      return false;
    }
    window.$message?.success($t('common.updateSuccess'));
    return true;
  } catch (e) {
    console.log(e);
    return false;
  }
}

async function handleGetSystemOptions() {
  try {
    const { data, error } = await fetchGetSystemOptions();
    if (error) {
      return;
    }
    systemOptions.value = data || [];
  } catch (e) {
    console.log(e);
  }
}
defineExpose({
  handleSubmit
});
onMounted(async () => {
  handleFetchGetDeptList();
  handleGetSystemOptions();
  handleInitModel();
  restoreValidation();
});
</script>

<template>
  <NForm ref="formRef" :model="model" label-placement="left" :rules="rules">
    <NFormItem span="24 m:12" :label="$t('page.manage.role.roleName')" path="name">
      <NTag type="primary" size="small">{{ model.name }}</NTag>
    </NFormItem>

    <NFormItem :label="$t('page.manage.role.roleCode')" path="code">
      <NTag type="primary" size="small">{{ model.code }}</NTag>
    </NFormItem>
    <NFormItem label="模块权限" path="systemName">
      <NRadioGroup v-model:value="model.systemName">
        <NRadio v-for="item in systemOptions" :key="item" :value="item" :label="item" />
      </NRadioGroup>
    </NFormItem>
    <NFormItem label="权限范围" path="dataScope">
      <NSelect v-model:value="model.dataScope" :options="DataScopeTypeOptions" />
    </NFormItem>

    <NFormItem v-if="model.dataScope === DataScopeType.Custom" label="部门范围" path="dataScopeDeptIds">
      <div class="w-full">
        <div class="w-full flex flex-wrap gap-4 border p-2">
          <span>
            全选/全不选:
            <NSwitch v-model:value="isSelectAll" @update:value="handleSelectAllChange" />
          </span>
          <span>
            全部展开/折叠:
            <NSwitch v-model:value="isExpandAll" @update:value="handleExpandAllChange" />
          </span>
          <span>
            父子联动:
            <NSwitch v-model:value="isCascade" />
          </span>
        </div>

        <div v-if="model.dataScope === DataScopeType.Custom" class="max-h-[50vh] overflow-auto">
          <NTree
            ref="treeRef"
            v-model:checked-keys="model.dataScopeDeptIds"
            checkable
            :cascade="isCascade"
            :data="treeOptions"
            value-field="id"
            key-field="id"
            label-field="name"
            :selectable="false"
            :default-expand-all="isExpandAll"
          />
        </div>
      </div>
    </NFormItem>
  </NForm>
</template>

<style scoped></style>
