<script setup lang="tsx">
import { NButton, NDropdown, NTag, useDialog } from 'naive-ui';

import dataAuthModal from './modules/data-auth-modal.vue';
import MenuAuthModal from './modules/menu-auth-modal.vue';
import RoleSearch from './modules/role-search.vue';

import { renderModalBtn } from '@/components/re-modal';
import { enableStatusRecord } from '@/constants/business';
import { RoleTypeOptions } from '@/enum';
import { useTable } from '@/hooks/common/table';
import { $t } from '@/locales';
import { fetchDeleteRole, fetchGetRoleList } from '@/service/api/system/role';
import { useAppStore } from '@/store/modules/app';
import { getEnumTagType, getEnumValue } from '@/utils/useful_func';
import RoleOperateDrawer from '@/views/system/role/modules/role-operate-drawer.vue';

const appStore = useAppStore();
const dialog = useDialog();
const options = [
  {
    label: '菜单权限',
    key: 'menu'
  },
  {
    label: '数据权限',
    key: 'data'
  },
  {
    label: '删除',
    key: 'delete'
  }
];

const {
  columns,
  columnChecks,
  data,
  loading,
  getData,
  getDataByPage,
  mobilePagination,
  searchParams,
  resetSearchParams
} = useTable({
  apiFn: fetchGetRoleList,
  showTotal: true,
  apiParams: {
    pageNo: 1,
    pageSize: 10,
    name: null,
    code: null,
    status: null
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48
    },
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center'
    },
    {
      key: 'name',
      title: $t('page.manage.role.roleName'),
      align: 'center'
    },
    {
      key: 'code',
      title: $t('page.manage.role.roleCode'),
      align: 'center'
    },
    {
      key: 'type',
      title: '角色类型',
      align: 'center',
      render: row => {
        return <NTag type={getEnumTagType(RoleTypeOptions, row.type)}>{getEnumValue(RoleTypeOptions, row.type)}</NTag>;
      }
    },
    {
      key: 'remark',
      align: 'center',
      title: $t('page.manage.role.roleDesc')
    },
    {
      key: 'status',
      title: $t('page.manage.role.roleStatus'),
      align: 'center',
      render: row => {
        if (row.status === null) {
          return null;
        }

        const tagMap: Record<Api.Common.EnableStatus, NaiveUI.ThemeColor> = {
          0: 'success',
          1: 'warning'
        };

        const label = $t(enableStatusRecord[row.status]);

        return <NTag type={tagMap[row.status]}>{label}</NTag>;
      }
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 150,
      render: row => (
        <div class="flex-center gap-8px">
          <NButton type="primary" ghost size="small" onClick={() => handleOperate('edit', row)}>
            {$t('common.edit')}
          </NButton>
          <NDropdown
            trigger="hover"
            placement="right-start"
            options={options}
            onSelect={(key: string) => {
              if (key === 'delete') {
                openDeleteConfirm(row?.id);
              } else if (key === 'menu') {
                handleMenuAuth(row);
              } else if (key === 'data') {
                handleDataAuth(row);
              }
            }}
          >
            <NButton size="small" type="primary" ghost>
              ...
            </NButton>
          </NDropdown>
        </div>
      )
    }
  ]
});

async function handleMenuAuth(rowData: Api.SystemManage.Role) {
  renderModalBtn(
    MenuAuthModal,
    { rowData },
    {
      title: '菜单权限',
      style: {
        width: '50%'
      },
      func: getData
    }
  );
}

async function handleDataAuth(rowData: Api.SystemManage.Role) {
  renderModalBtn(
    dataAuthModal,
    { rowData },
    {
      title: '数据权限',
      style: {
        width: '50%'
      },
      func: getData
    }
  );
}

async function handleDelete(id?: number) {
  try {
    const { error } = await fetchDeleteRole({ id });
    if (error) return;
    window.$message?.success($t('common.deleteSuccess'));
    await getData();
  } catch (e) {
    console.log(e);
  }
}

function openDeleteConfirm(id: number) {
  const d = dialog.info({
    title: $t('common.tip'),
    content: $t('common.confirmDelete'),
    positiveText: $t('common.confirm'),
    negativeText: $t('common.cancel'),
    onPositiveClick: async () => {
      d.loading = true;
      await handleDelete(id);
      d.loading = false;
    }
  });
}

function handleOperate(type: NaiveUI.TableOperateType, rowData: Api.SystemManage.Role | null = null) {
  renderModalBtn(
    RoleOperateDrawer,
    { operateType: type, rowData },
    {
      title: type === 'add' ? $t('page.manage.role.addRole') : $t('page.manage.role.editRole'),
      style: {
        width: '50%'
      },
      func: getData
    }
  );
}
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <RoleSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <NCard :title="$t('page.manage.role.title')" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :loading="loading"
          @add="handleOperate('add')"
          @refresh="getData"
        />
      </template>
      <NDataTable
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="702"
        :loading="loading"
        remote
        :row-key="row => row.id"
        :pagination="mobilePagination"
        class="sm:h-full"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
