<script setup>
import { onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useAuthStore } from '@/store/modules/auth';

const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();

onMounted(async () => {
  // 强制跳出 iframe
  if (window.top !== window.self) {
    window.top.location.href = window.location.href;
    return;
  }
  const code = route.query.code;

  if (!code) {
    window.$message?.error('授权失败，请重新登录');
    await router.replace('/login');
    return;
  }

  try {
    // 调用后端接口登录
    await authStore.handleSocialLogin({ code });
  } catch (error) {
    console.error('钉钉登录失败：', error);
    await router.replace('/login');
    window.$message?.error('登录失败，请重试');
  }
});
</script>

<template>
  <div class="loading">
    <NSpin />
    <p>正在登录，请稍候...</p>
  </div>
</template>

<style scoped>
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #f0f2f5;
}
</style>
