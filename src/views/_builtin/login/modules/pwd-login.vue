<script setup lang="ts">
import { computed, reactive, ref } from 'vue';
import { MD5 } from 'crypto-js';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { useAuthStore } from '@/store/modules/auth';
import { useRouterPush } from '@/hooks/common/router';

const { toggleLoginModule } = useRouterPush();
defineOptions({
  name: 'PwdLogin'
});
const verify = ref();
const captchaType = ref('blockPuzzle'); // blockPuzzle 滑块 clickWord 点击文字
const authStore = useAuthStore();
const { formRef, validate } = useNaiveForm();

interface FormModel {
  username: string;
  password: string;
  captchaEnable: string;
}

const model: FormModel = reactive({
  username: '',
  password: '',
  captchaEnable: import.meta.env.VITE_APP_CAPTCHA_ENABLE
});

const rules = computed<Record<keyof FormModel, App.Global.FormRule[]>>(() => {
  // inside computed to make locale reactive, if not apply i18n, you can define it without computed
  const { formRules } = useFormRules();

  return {
    username: formRules.userName,
    password: formRules.pwd
  };
});
// 获取验证码
const getCode = async () => {
  await validate();
  // 情况一，未开启：则直接登录
  if (model.captchaEnable === 'false') {
    await handleSubmit({});
  } else {
    // 情况二，已开启：则展示验证码；只有完成验证码的情况，才进行登录
    // 弹出验证码
    verify.value.show();
  }
};
async function handleSubmit(params: any) {
  await validate();
  const password = MD5(model.password).toString().slice(10, 25);
  await authStore.login(model.username, password, params.captchaVerification, model.captchaEnable);
}
</script>

<template>
  <NForm ref="formRef" :model="model" :rules="rules" size="large" :show-label="false" @keyup.enter="getCode">
    <NFormItem path="username">
      <NInput v-model:value="model.username" :placeholder="$t('page.login.common.userNamePlaceholder')" />
    </NFormItem>
    <NFormItem path="password">
      <NInput
        v-model:value="model.password"
        type="password"
        show-password-on="click"
        :placeholder="$t('page.login.common.passwordPlaceholder')"
      />
    </NFormItem>
    <NSpace vertical :size="24">
      <NButton type="primary" size="large" round block :loading="authStore.loginLoading" @click="getCode">
        {{ $t('common.confirm') }}
      </NButton>
    </NSpace>
  </NForm>
  <Verify
    v-if="model.captchaEnable === 'true'"
    ref="verify"
    :captcha-type="captchaType"
    :img-size="{ width: '400px', height: '200px' }"
    mode="pop"
    @success="handleSubmit"
  />
</template>

<style scoped></style>
