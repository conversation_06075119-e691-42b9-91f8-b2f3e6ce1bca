<script setup lang="ts">
defineOptions({
  name: 'CodeLogin'
});
const route = useRoute();
const redirectUri = encodeURIComponent(
  `${window.location.origin + import.meta.env.VITE_LOGIN_CALLBACK_URI}?redirect=${route.query?.redirect}`
);
const clientId = import.meta.env.VITE_CLIENT_ID;
const iframeSrc = `https://login.dingtalk.com/oauth2/challenge.htm?redirect_uri=${redirectUri}&response_type=code&client_id=${clientId}&scope=openid&state=dddd&prompt=consent%20login&FEForceLogin=true`;
</script>

<template>
  <div class="login-main-layout relative">
    <iframe width="740" height="580" :src="iframeSrc" />
    <div class="absolute bottom-[22px] z-20 h-[30px] w-full bg-[#fff]" />
    <div class="login-tip">登录钉钉账号后将自动进入办公平台系统</div>
  </div>
</template>

<style scoped>
.login-tip {
  position: absolute;
  bottom: 22px;
  font-size: 13px;
  color: #666;
  z-index: 22;
}
.login-main-layout {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  gap: 40px;
  max-width: 900px;
  margin: 0 auto;
}
</style>
