<script setup lang="ts">
import { getPaletteColorByNumber, mixColor } from '@sa/color';
import type { Component } from 'vue';
import { computed } from 'vue';

import BindWechat from './modules/bind-wechat.vue';
import CodeLogin from './modules/code-login.vue';
import PwdLogin from './modules/pwd-login.vue';
import Register from './modules/register.vue';
import ResetPwd from './modules/reset-pwd.vue';

import { loginModuleRecord } from '@/constants/app';
import { useRouterPush } from '@/hooks/common/router';
import { useThemeStore } from '@/store/modules/theme';
import { getAppTitle } from '@/utils/useful_func';

interface Props {
  /** The login module */
  module?: UnionKey.LoginModule;
}
const screenHeight = window.screen.height;

const props = defineProps<Props>();
const route = useRoute();
const themeStore = useThemeStore();

const { toggleLoginModule } = useRouterPush();

interface LoginModule {
  label: string;
  component: Component;
}

const moduleMap: Record<UnionKey.LoginModule, LoginModule> = {
  'pwd-login': { label: loginModuleRecord['pwd-login'], component: PwdLogin },
  'code-login': { label: loginModuleRecord['code-login'], component: CodeLogin },
  register: { label: loginModuleRecord.register, component: Register },
  'reset-pwd': { label: loginModuleRecord['reset-pwd'], component: ResetPwd },
  'bind-wechat': { label: loginModuleRecord['bind-wechat'], component: BindWechat }
};

const activeModule = computed(() => moduleMap[props.module || 'code-login']);

const bgThemeColor = computed(() =>
  themeStore.darkMode ? getPaletteColorByNumber(themeStore.themeColor, 600) : themeStore.themeColor
);

const bgColor = computed(() => {
  const COLOR_WHITE = '#ffffff';

  const ratio = themeStore.darkMode ? 0.5 : 0.2;

  return mixColor(COLOR_WHITE, themeStore.themeColor, ratio);
});
</script>

<template>
  <div class="size-full flex-center overflow-hidden" :style="{ backgroundColor: bgColor }">
    <WaveBg :theme-color="bgThemeColor" />
    <NCard
      :bordered="false"
      class="z-4 w-auto rd-12px"
      :style="
        screenHeight > 860 || route.path === '/login/pwd-login'
          ? {}
          : {
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: `translate(-50%, -50%) scale(${0.8})`,
              transformOrigin: 'center center'
            }
      "
    >
      <div
        :class="`${route.path !== '/login/pwd-login' ? `${screenHeight > 860 || route.path === '/login/pwd-login' ? 'h-650px' : 'h-630px'} w-450px` : 'w-400px lt-sm:w-300px'}`"
      >
        <header class="flex-y-center justify-between">
          <SvgIcon local-icon="logo" class="text-64px text-primary lt-sm:text-48px" />
          <h3 class="text-28px text-primary font-500 lt-sm:text-22px">{{ getAppTitle() }}</h3>
          <div class="i-flex-col">
            <ButtonIcon
              v-if="route.path !== '/login/pwd-login'"
              local-icon="account"
              tooltip-content="账号密码登录"
              style="font-size: 30px"
              @click="toggleLoginModule('pwd-login')"
            />
            <NBadge v-else value="推荐">
              <ButtonIcon
                local-icon="dingding"
                tooltip-content="钉钉登录"
                style="font-size: 30px"
                @click="toggleLoginModule('code-login')"
              />
            </NBadge>
          </div>
        </header>
        <main :class="`${route.path === '/login/pwd-login' ? 'mt-40px' : ''}`">
          <div>
            <Transition :name="themeStore.page.animateMode" mode="out-in" appear>
              <component :is="activeModule.component" />
            </Transition>
          </div>
        </main>
      </div>
    </NCard>
  </div>
</template>

<style scoped></style>
