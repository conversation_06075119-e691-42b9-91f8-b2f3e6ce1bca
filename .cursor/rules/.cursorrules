# Cursor 规则文件

## 代码注释
- 使用JSDoc注释格式
- 所有函数、类、组件必须有详细的JSDoc注释

## Git Commit 规范

### Commit 消息格式
```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### Type 类型（必填）
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `perf`: 性能优化
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动
- `ci`: CI/CD相关
- `build`: 构建系统或外部依赖的变动
- `revert`: 回滚之前的commit

### Scope 范围（可选）
- `auth`: 认证相关
- `api`: API相关
- `ui`: 用户界面
- `store`: 状态管理
- `router`: 路由相关
- `utils`: 工具函数
- `i18n`: 国际化
- `theme`: 主题相关

### Description 描述（必填）
- 使用中文描述
- 简洁明了，不超过50个字符
- 使用祈使句，如"添加"、"修复"、"更新"

### Commit 示例

```
feat(auth): 添加用户登录功能
fix(api): 修复用户信息获取接口500错误
docs: 更新API文档
refactor(store): 重构用户状态管理
```

## 代码风格
- 使用2个空格缩进
- 使用单引号
- 行末必须有分号
- 变量和函数使用camelCase
- 组件使用PascalCase
- 文件名使用kebab-case

## 分支管理
- `main`: 主分支
- `develop`: 开发分支
- `feature/*`: 功能分支
- `hotfix/*`: 热修复分支

## 代码审查
- 所有代码必须经过Code Review
- 使用Pull Request进行代码合并
- 至少需要一名团队成员批准 